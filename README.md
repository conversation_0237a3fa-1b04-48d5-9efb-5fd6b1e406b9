# MoeGo DevOps Console
* 用于执行对 MoeGo 环境动态资源的变更，例如：执行 SQL，创建 Topic，执行 Redis 命令等。参见：[DevOpsConsole](DevOpsConsole.md)
* 定时清理开发集群无用的资源，例如过期的特性分支资源。如何配置清理规则，参见：[ClusterCleaner](ClusterCleaner.md)

## Schema
* [schema/proto](schema/proto)：定义了 DevOps Console 所有可执行的 API。由于 DevOps Console 还在持续开发中，还未达到 API 可以公开的程度，因此这里仅公开部分可以执行的 API。
* [schema/resources](schema/resources)：定义了目前 MoeGo 平台可以执行的资源

## RoadMap
* DevOps Console 计划支持的功能，包括：
  * 支持 pulsar topic 管理
  * 支持 clickhouse sql 的执行
* 接入 SSO 认证
* 支持在 pod 中执行脚本
* 提供 DevOps Console UI
