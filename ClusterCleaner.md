# Cluster Cleaner
定时清理开发集群无用的资源，例如过期的特性分支资源。

## 清理规则
1. 每4个小时执行一次
1. 清理动作是基于特性分支为基础的，如果一个仓库的特性分支被删除，那么对应的 Pod 将被删除。
1. 如果特性分支长时间存在，并且 Pod 在超过一定时间内都没有更新的话也会被删除。
1. 其他 Service/PodDisruptionBudget/EnvoyFilter 登资源随着 Pod 的删除也一并删除
1. 允许对 3 中的 Pod 配置特定的自定义过期策略

### 清理配置
清理配置文件为：[config/cluster-cleaner.yaml](config/cluster-cleaner.yaml)，修改后下次执行时生效
```yaml
cluster: moego-server-cluster-development
namespace: ns-testing
prefixes:
  - feature
  - bugfix
  - hotfix
timezone: "UTC+08:00"
github:
  org: MoeGolibrary
default:
  duration: 15d
rules:
  # 服务名称前缀为："moego-web-"，并且分支名前缀为："feature-adyen-" 的 Pod 将在2025年1月底过期。
  - name: moego-web-.*
    branch: feature-adyen-.*
    time:
      deadline: "2025-02-01 00:00:00"
  # 所有分支名为 "feature-oddball" 的 Pod 将在2025年1月底过期。
  - branch: feature-oddball
    time:
      deadline: "2025-02-01 00:00:00"
  # 所有分支名为 "feature-some1" 的 Pod 如果 20 天内都没有更新则被清理。
  - branch: feature-some1
    time:
      duration: "20d"
  # moego-server-grooming 的 feature-some2 分支 Pod 最多保留 10 天或在 2025年1月28号过期。
  - name: moego-server-grooming
    branch: feature-some2
    time:
      duration: "10d"
      deadline: "2025-01-28 00:00:00"
```
* prefixes：支持清理的分支前缀名，Pod 分支名不匹配此列表的不在清理范围之内。
* timezone：仅对本配置文件中出现的时间戳进行解释。
* default：默认的过期时间，有效的表达式：`10d12h30m20s`
* rules：指定对可能长期存在的分支的自定义清理规则
  * name：正则表达式，匹配应用或仓库的名称，name 与 branch 必须至少指定其一。
  * branch：正则表达式，匹配分支名，name 与 branch 必须至少指定其一。
  * time：指定过期时间 duration 和 deadline 至少指定其一，如果两者同时指定，任何一个条件匹配都视为过期。
    * duration：指定一段时间后过期
    * deadline：指定在特定时间点过期

#### 匹配规则
* 如果一个仓库的某个特性分支被删除了，则对应的 pod 将立即被删除。
* 如果一个分支长期存在，将按 rules 的顺序进行评估：
  * name 和 branch 命中则视为规则命中，将按指定的 time 计算是否过期。
  * 如果所有的 rules 都没有命中，将按 default 计算是否过期。
