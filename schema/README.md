# Requests Schema
```yaml
# The name, description and issue fields are only used to display some information, they are optional.
name: "DevOps Console Request Example"
description: "just test"
issues:
  - "IFRBE-492"

# For the requests in the current file, specify whether to stop when an error occurs.
# If false is specified, subsequent requests will continue to execute even if a request fails. default true
stopWhenError: true

# For api and body, Please see proto definition: schema/proto
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body: ...
  - api: "/moego.devops.api.console.v1.MessageQueueApi/ListTopics"
    body: ...
```