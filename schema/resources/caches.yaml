- platform: SELF_HOSTED
  identifier: "development/testing/redis"
  engine: redis
  version: "7.2"
  endpoints:
    - address: "redis.ns-testing.svc.cluster.local"
      port: 6379
  tls: false
  allocatedSize: 8
  instanceClass: "r5a.2xlarge"
  publiclyAccessible: true
  createdAt: "2024-04-25T07:24:39Z"
- platform: SELF_HOSTED
  identifier: "development/staging/redis"
  engine: redis
  version: "7.2"
  endpoints:
    - address: "redis.ns-staging.svc.cluster.local"
      port: 6379
  tls: false
  allocatedSize: 8
  instanceClass: "r5a.2xlarge"
  publiclyAccessible: true
  createdAt: "2024-04-25T07:27:56Z"
- platform: AWS_ELASTIC_CACHE
  identifier: "moego-redis-prod"
