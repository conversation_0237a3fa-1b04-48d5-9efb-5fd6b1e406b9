- platform: SELF_HOSTED
  identifier: "development/pulsar/pulsar"
  engine: pulsar
  version: "2.10.4"
  endpoints:
    - address: "pulsar://pulsar-broker-0.pulsar-broker.pulsar.svc.cluster.local"
      port: 6650
    - address: "pulsar://pulsar-broker-1.pulsar-broker.pulsar.svc.cluster.local"
      port: 6650
    - address: "pulsar://pulsar-broker-2.pulsar-broker.pulsar.svc.cluster.local"
      port: 6650
  replicas: 3
  allocatedSize: 128
  instanceClass: "r5a.2xlarge"
  publiclyAccessible: false
  createdAt: "2023-05-21T11:51:23Z"
- platform: SELF_HOSTED
  identifier: "production/pulsar/pulsar"
  engine: pulsar
  version: "2.10.4"
  endpoints:
    - address: "pulsar://pulsar-broker-0.pulsar-broker.pulsar.svc.cluster.local"
      port: 6650
    - address: "pulsar://pulsar-broker-1.pulsar-broker.pulsar.svc.cluster.local"
      port: 6650
    - address: "pulsar://pulsar-broker-2.pulsar-broker.pulsar.svc.cluster.local"
      port: 6650
  replicas: 3
  allocatedSize: 128
  instanceClass: "r5a.2xlarge"
  publiclyAccessible: false
  createdAt: "2023-04-19T09:37:38Z"
- platform: SELF_HOSTED
  identifier: "development/kafka/kafka-controller"
  engine: kafka
  version: "3.7.0"
  endpoints:
    - address: "kafka-controller-0.kafka-controller-headless.kafka.svc.cluster.local"
      port: 9092
    - address: "kafka-controller-1.kafka-controller-headless.kafka.svc.cluster.local"
      port: 9092
    - address: "kafka-controller-2.kafka-controller-headless.kafka.svc.cluster.local"
      port: 9092
  replicas: 3
  allocatedSize: 64
  instanceClass: "r5a.2xlarge"
  publiclyAccessible: false
  createdAt: "2024-02-06T03:00:48Z"
- platform: AWS_MSK
  identifier: "kafka-staging"
- platform: AWS_MSK
  identifier: "moego-kafka"
