- name: staging
  description: "staging environment for external/internal customer"
  identifier:
    cluster: "moego-server-cluster-development"
    namespace: "ns-staging"
  isManaged: true
  createdAt: '2024-09-25 11:19:24'
  databases:
    - platform: AWS_RDS
      identifier: "staging-mysql-cluster"
    - platform: AWS_RDS
      identifier: "staging-postgres-cluster"
    - platform: SELF_HOSTED
      identifier: "production/clickhouse/clickhouse-moego"
    - platform: SELF_HOSTED
      identifier: "production/clickhouse/clickhouse-reporting"
    - platform: SELF_HOSTED
      identifier: "production/clickhouse/clickhouse-moego-new"
  caches:
    - platform: SELF_HOSTED
      identifier: "development/ns-staging/redis"
  messageQueues:
    - platform: SELF_HOSTED
      identifier: "development/pulsar/pulsar"
    - platform: AWS_MSK
      identifier: "kafka-staging"
- name: testing
  description: "testing environment for internal developer"
  identifier:
    cluster: "moego-server-cluster-development"
    namespace: "ns-testing"
  isManaged: false
  createdAt: '2022-03-31 11:55:29'
  databases:
    - platform: AWS_RDS
      identifier: "testing-mysql-aurora"
    - platform: AWS_RDS
      identifier: "testing-postgres-aurora"
    - platform: SELF_HOSTED
      identifier: "development/clickhouse/clickhouse-moego"
    - platform: SELF_HOSTED
      identifier: "development/clickhouse/clickhouse-reporting"
  caches:
    - platform: SELF_HOSTED
      identifier: "development/ns-testing/redis"
  messageQueues:
    - platform: SELF_HOSTED
      identifier: "development/pulsar/pulsar"
    - platform: SELF_HOSTED
      identifier: "development/kafka/kafka-controller"
- name: production
  description: "production environment for external customer"
  identifier:
    cluster: "moego-server-cluster-production"
    namespace: "ns-production"
  isManaged: false
  createdAt: '2022-01-21 08:36:27'
  databases:
    - platform: AWS_RDS
      identifier: "prod-mysql-cluster"
    - platform: AWS_RDS
      identifier: "prod-postgres-cluster"
    - platform: SELF_HOSTED
      identifier: "production/clickhouse/clickhouse-moego"
    - platform: SELF_HOSTED
      identifier: "production/clickhouse/clickhouse-reporting"
    - platform: SELF_HOSTED
      identifier: "production/clickhouse/clickhouse-moego-new"
  caches:
    - platform: AWS_ELASTIC_CACHE
      identifier: "moego-redis-prod"
  messageQueues:
    - platform: SELF_HOSTED
      identifier: "production/pulsar/pulsar"
    - platform: AWS_MSK
      identifier: "moego-kafka"
