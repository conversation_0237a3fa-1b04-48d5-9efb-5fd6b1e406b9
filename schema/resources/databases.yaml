- platform: SELF_HOSTED
  identifier: "development/clickhouse/clickhouse-moego"
  engine: "clickhouse"
  version: "23.7.4"
  endpoints:
    - address: "clickhouse-clickhouse.clickhouse.svc.cluster.local"
      port: 9000
  allocatedSize: 1200
  publiclyAccessible: true
  instanceClass: "r5a.2xlarge"
  defaultUser: moego_developer
  createdAt: "2024-07-19T09:01:51Z"
  updatedAt: "2024-07-19T09:01:51Z"
- platform: SELF_HOSTED
  identifier: "development/clickhouse/clickhouse-reporting"
  engine: "clickhouse"
  version: "23.7.6"
  endpoints:
    - address: "clickhouse-reporting.clickhouse.svc.cluster.local"
      port: 9000
  allocatedSize: 100
  publiclyAccessible: false
  instanceClass: "r5a.2xlarge"
  defaultUser: moego_developer
  createdAt: "2024-07-19T09:22:44Z"
  updatedAt: "2024-07-19T09:22:44Z"
- platform: SELF_HOSTED
  identifier: "production/clickhouse/clickhouse-moego"
  engine: "clickhouse"
  version: "23.7.6"
  endpoints:
    - address: "clickhouse.bridge-prod.moego.pet"
      port: 9000
  allocatedSize: 128
  publiclyAccessible: false
  instanceClass: "r5a.2xlarge"
  defaultUser: moego_writer
  createdAt: "2024-09-24T08:17:05Z"
  updatedAt: "2024-09-24T08:17:05Z"
- platform: SELF_HOSTED
  identifier: "production/clickhouse/clickhouse-reporting"
  engine: "clickhouse"
  version: "23.7.6"
  endpoints:
    - address: "clickhouse-reporting.bridge-prod.moego.pet"
      port: 9001
  allocatedSize: 300
  publiclyAccessible: false
  instanceClass: "t3.2xlarge"
  defaultUser: moego_writer
  createdAt: "2024-06-19T09:05:53Z"
  updatedAt: "2024-06-19T09:05:53Z"
- platform: SELF_HOSTED
  identifier: "production/clickhouse/clickhouse-moego-new"
  engine: "clickhouse"
  version: "24.7.3"
  endpoints:
    - address: "clickhouse-moego-new.bridge-prod.moego.pet"
      port: 9002
  allocatedSize: 1000
  publiclyAccessible: false
  instanceClass: "t3.2xlarge"
  defaultUser: moego_writer
  createdAt: "2024-08-27T04:39:39Z"
  updatedAt: "2024-08-27T04:39:39Z"
- platform: AWS_RDS
  identifier: "testing-mysql-aurora"
  engine: mysql
  version: "8.0.mysql_aurora.3.08.0"
  defaultUser: moego_developer_240310_eff7a0dc
  labels:
    moego.env/name: testing
    moego.env/cluster: moego-server-cluster-development
    moego.env/namespace: ns-testing
- platform: AWS_RDS
  identifier: "testing-mysql-grooming"
  engine: mysql
  version: "8.0.mysql_aurora.3.08.1"
  defaultUser: moego_developer_240310_eff7a0dc
  labels:
    moego.env/name: testing
    moego.env/cluster: moego-server-cluster-development
    moego.env/namespace: ns-testing
- platform: AWS_RDS
  identifier: "testing-postgres-aurora"
  engine: postgres
  version: "14.13"
  defaultUser: moego_developer_240310_eff7a0dc
  labels:
    moego.env/name: testing
    moego.env/cluster: moego-server-cluster-development
    moego.env/namespace: ns-testing
- platform: AWS_RDS
  identifier: "staging-mysql-cluster"
  engine: mysql
  version: "8.0.mysql_aurora.3.08.0"
  defaultUser: moego_mysql_24111015522837e894
  labels:
    moego.env/name: staging
    moego.env/cluster: moego-server-cluster-development
    moego.env/namespace: ns-staging
- platform: AWS_RDS
  identifier: "staging-mysql-grooming"
  engine: mysql
  version: "8.0.mysql_aurora.3.08.1"
  defaultUser: moego_mysql_24111015522837e894
  labels:
    moego.env/name: staging
    moego.env/cluster: moego-server-cluster-development
    moego.env/namespace: ns-staging
- platform: AWS_RDS
  identifier: "staging-postgres-cluster"
  engine: postgres
  version: "14.13"
  defaultUser: moego_postgres_241110155234d6f87a
  labels:
    moego.env/name: staging
    moego.env/cluster: moego-server-cluster-development
    moego.env/namespace: ns-staging
- platform: AWS_RDS
  identifier: "prod-mysql-cluster"
  engine: mysql
  version: "8.0.mysql_aurora.3.08.0"
  labels:
    moego.env/name: production
    moego.env/cluster: moego-server-cluster-production
    moego.env/namespace: ns-production
- platform: AWS_RDS
  identifier: "prod-postgres-cluster"
  engine: postgres
  version: "14.13"
  labels:
    moego.env/name: production
    moego.env/cluster: moego-server-cluster-production
    moego.env/namespace: ns-production
