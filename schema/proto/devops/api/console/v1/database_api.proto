// @since 2024/9/11 3:15 PM
// <AUTHOR>

syntax = "proto3";

package moego.devops.api.console.v1;

import "devops/models/console/v1/params_models.proto";

option go_package = "github.com/MoeGolibrary/moego-devops-platform/out/go/moego/devops/api/console/v1;consoleapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.devops.api.console.v1";

// DatabaseApi
service DatabaseApi {

    // Register a cloud database information to the devops console
    // rpc RegisterDatabase(devops.models.console.v1.RegisterDatabaseRequest) returns (devops.models.console.v1.RegisterDatabaseResponse);

    // list databases
    rpc ListDatabases(devops.models.console.v1.ListDatabasesRequest) returns (devops.models.console.v1.ListDatabasesResponse);

    // get a database
    rpc GetDatabase(devops.models.console.v1.GetDatabaseRequest) returns (devops.models.console.v1.GetDatabaseResponse);

    // watch a cloud database status
    rpc WatchDatabase(devops.models.console.v1.WatchDatabaseRequest) returns (devops.models.console.v1.WatchDatabaseResponse);

    // execute sql on database
    rpc ExecuteSql(devops.models.console.v1.ExecuteSqlRequest) returns (devops.models.console.v1.ExecuteSqlResponse);

}
