// @since 2024/10/10 2:15 PM
// <AUTHOR>

syntax = "proto3";

package moego.devops.api.console.v1;

import "devops/models/console/v1/params_models.proto";

option go_package = "github.com/MoeGolibrary/moego-devops-platform/out/go/moego/devops/api/console/v1;consoleapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.devops.api.console.v1";

// CacheApi
service CacheApi {

    // rpc RegisterCache(devops.models.console.v1.RegisterCacheRequest) returns (devops.models.console.v1.RegisterCacheResponse);

    rpc ListCaches(devops.models.console.v1.ListCachesRequest) returns (devops.models.console.v1.ListCachesResponse);

    rpc GetCache(devops.models.console.v1.GetCacheRequest) returns (devops.models.console.v1.GetCacheResponse);

    rpc ExecuteCacheCommand(devops.models.console.v1.ExecuteCacheCommandRequest) returns (devops.models.console.v1.ExecuteCacheCommandResponse);

}
