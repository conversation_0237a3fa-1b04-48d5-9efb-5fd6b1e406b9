// @since 2024/9/11 3:15 PM
// <AUTHOR>

syntax = "proto3";

package moego.devops.api.console.v1;

import "devops/models/console/v1/params_models.proto";

option go_package = "github.com/MoeGolibrary/moego-devops-platform/out/go/moego/devops/api/console/v1;consoleapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.devops.api.console.v1";

// EnvironmentApi
service EnvironmentApi {

    // rpc RegisterEnvironments(devops.models.console.v1.RegisterEnvironmentsRequest) returns (devops.models.console.v1.RegisterEnvironmentsResponse);

    rpc ListEnvironments(devops.models.console.v1.ListEnvironmentsRequest) returns (devops.models.console.v1.ListEnvironmentsResponse);

    rpc GetEnvironment(devops.models.console.v1.GetEnvironmentRequest) returns (devops.models.console.v1.GetEnvironmentResponse);

}
