// @since 2024/11/13 10:15 AM
// <AUTHOR>

syntax = "proto3";

package moego.devops.api.console.v1;

import "devops/models/console/v1/params_models.proto";

option go_package = "github.com/MoeGolibrary/moego-devops-platform/out/go/moego/devops/api/console/v1;consoleapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.devops.api.console.v1";

// MessageQueueApi
service MessageQueueApi {

    // rpc RegisterMessageQueue(devops.models.console.v1.RegisterMessageQueueRequest) returns (devops.models.console.v1.RegisterMessageQueueResponse);

    rpc ListMessageQueues(devops.models.console.v1.ListMessageQueuesRequest) returns (devops.models.console.v1.ListMessageQueuesResponse);

    rpc GetMessageQueue(devops.models.console.v1.GetMessageQueueRequest) returns (devops.models.console.v1.GetMessageQueueResponse);

    rpc ListTopics(devops.models.console.v1.ListTopicsRequest) returns (devops.models.console.v1.ListTopicsResponse);

    rpc CreateTopics(devops.models.console.v1.CreateTopicsRequest) returns (devops.models.console.v1.CreateTopicsResponse);

    rpc DeleteTopics(devops.models.console.v1.DeleteTopicsRequest) returns (devops.models.console.v1.DeleteTopicsResponse);

    rpc GetTopics(devops.models.console.v1.GetTopicsRequest) returns (devops.models.console.v1.GetTopicsResponse);

    // rpc UpdateTopic(devops.models.console.v1.UpdateTopicRequest) returns (devops.models.console.v1.UpdateTopicResponse);

}
