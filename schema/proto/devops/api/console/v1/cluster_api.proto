// @since 2024/9/11 3:15 PM
// <AUTHOR>

syntax = "proto3";

package moego.devops.api.console.v1;

import "devops/models/console/v1/params_models.proto";

option go_package = "github.com/MoeGolibrary/moego-devops-platform/out/go/moego/devops/api/console/v1;consoleapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.devops.api.console.v1";

// ClusterApi
service ClusterApi {

    rpc ListClusters(devops.models.console.v1.ListClustersRequest) returns (devops.models.console.v1.ListClustersResponse);

    rpc GetCluster(devops.models.console.v1.GetClusterRequest) returns (devops.models.console.v1.GetClusterResponse);

    rpc ListNodeGroups(devops.models.console.v1.ListNodeGroupsRequest) returns (devops.models.console.v1.ListNodeGroupsResponse);

    rpc GetNodeGroup(devops.models.console.v1.GetNodeGroupRequest) returns (devops.models.console.v1.GetNodeGroupResponse);

    rpc ListNodes(devops.models.console.v1.ListNodesRequest) returns (devops.models.console.v1.ListNodesResponse);

    rpc GetNode(devops.models.console.v1.GetNodeRequest) returns (devops.models.console.v1.GetNodeResponse);

    rpc ListNamespaces(devops.models.console.v1.ListNamespacesRequest) returns (devops.models.console.v1.ListNamespacesResponse);

    rpc GetNamespace(devops.models.console.v1.GetNamespaceRequest) returns (devops.models.console.v1.GetNamespaceResponse);

    // rpc RestartWorkloads(devops.models.console.v1.RestartWorkloadsRequest) returns (devops.models.console.v1.RestartWorkloadsResponse);

}
