// @since 2024/9/11 3:15 PM
// <AUTHOR>

syntax = "proto3";

package moego.devops.models.mq.v1;

import "devops/models/utils/v1/network_models.proto";
import "devops/models/utils/v1/platform_models.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-devops-platform/out/go/moego/devops/models/mq/v1;mqpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.devops.models.mq.v1";

// MessageQueue
message MessageQueue {
    // MQ identifier
    devops.models.utils.v1.PlatformIdentifier identifier = 1;
    // MQ status
    string status = 2;
    // MQ engine
    string engine = 3;
    // MQ engine version
    string version = 4;
    // MQ endpoints
    repeated devops.models.utils.v1.Endpoint endpoints = 5;
    // MQ replicas
    int32 replicas = 6;
    // MQ storage size
    int32 allocated_size = 7;
    // MQ instance class
    string instance_class = 8;
    // Whether the MQ has deletion protection enabled
    bool deletion_protection = 9;
    // Whether the MQ is publicly accessible
    bool publicly_accessible = 10;
    // The labels attached to the MQ
    map<string, string> labels = 13 [(validate.rules).map = {
        max_pairs: 256,
        keys: {
            string: {max_len: 255}
        },
        values: {
            string: {max_len: 63}
        }
    }];
    // MQ extra info
    google.protobuf.Struct extra = 14;
    // create time
    google.protobuf.Timestamp created_at = 15;
    // update time
    google.protobuf.Timestamp updated_at = 16;
}

// Topic
message Topic {
    // topic id
    optional string id = 1;
    // topic name
    string name = 2;
    // topic engine
    string engine = 3;
    // topic tenant
    optional string tenant = 4;
    // topic namespace
    optional string namespace = 5;
    // is internal topic?
    optional bool internal = 6;
    // partition number
    optional int32 partition_number = 7;
    // replication factor
    optional int32 replication_factor = 8;
    // extra config
    optional google.protobuf.Struct config = 9;
}
