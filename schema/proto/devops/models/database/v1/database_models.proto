// @since 2024/9/11 3:15 PM
// <AUTHOR>

syntax = "proto3";

package moego.devops.models.database.v1;

import "devops/models/utils/v1/network_models.proto";
import "devops/models/utils/v1/platform_models.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-devops-platform/out/go/moego/devops/models/database/v1;databasepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.devops.models.database.v1";

// Database
message Database {
    // database identifier
    devops.models.utils.v1.PlatformIdentifier identifier = 1;
    // database status
    string status = 2;
    // database engine
    string engine = 3;
    // database engine version
    string version = 4;
    // database endpoints
    repeated devops.models.utils.v1.Endpoint endpoints = 5;
    // database engine version
    int32 port = 6;
    // database instance class
    string instance_class = 7;
    // database storage size
    int32 allocated_size = 8;
    // Whether the database is publicly accessible
    bool publicly_accessible = 9;
    // The labels attached to the database
    map<string, string> labels = 13 [(validate.rules).map = {
        max_pairs: 256,
        keys: {
            string: {max_len: 255}
        },
        values: {
            string: {max_len: 63}
        }
    }];
    // extra info
    google.protobuf.Struct extra = 14;
    // create time
    google.protobuf.Timestamp created_at = 15;
    // update time
    google.protobuf.Timestamp updated_at = 16;
}
