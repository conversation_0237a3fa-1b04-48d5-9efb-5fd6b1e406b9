// @since 2024/9/11 3:15 PM
// <AUTHOR>

syntax = "proto3";

package moego.devops.models.console.v1;

import "google/protobuf/struct.proto";
import "devops/models/cache/v1/cache_models.proto";
import "devops/models/cluster/v1/cluster_models.proto";
import "devops/models/environment/v1/environment_models.proto";
import "devops/models/mq/v1/mq_models.proto";
import "devops/models/database/v1/database_models.proto";
import "devops/models/utils/v1/list_models.proto";
import "devops/models/utils/v1/platform_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-devops-platform/out/go/moego/devops/models/console/v1;consolepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.devops.models.console.v1";

// Environment API
//------------------------------------------------------------------------------
// ListEnvironmentsRequest
message ListEnvironmentsRequest {
    // environment status
    repeated devops.models.environment.v1.Environment.Status statuses = 1 [(validate.rules).repeated = {
        unique: true,
        max_items: 5
    }];
}

// ListEnvironmentsResponse
message ListEnvironmentsResponse {
    // list of environments
    repeated devops.models.environment.v1.Environment environments = 1;
}

// GetEnvironmentRequest
message GetEnvironmentRequest {
    // environment identifier
    devops.models.environment.v1.Environment.Identifier identifier = 1;
}

// GetEnvironmentResponse
message GetEnvironmentResponse {
    // environment
    optional devops.models.environment.v1.Environment environment = 1;
}

// Cluster API
//------------------------------------------------------------------------------

// ListClustersRequest
message ListClustersRequest {
    // the platform where the cluster is running
    optional string platform = 1 [(validate.rules).string = {max_len: 128}];
}

// ListClustersResponse
message ListClustersResponse {
    // list of clusters
    repeated devops.models.cluster.v1.Cluster clusters = 1;
}

// GetClusterRequest
message GetClusterRequest {
    // cluster identifier
    devops.models.utils.v1.PlatformIdentifier identifier = 1;
}

// GetClusterResponse
message GetClusterResponse {
    // cluster
    optional devops.models.cluster.v1.Cluster cluster = 1;
}

// ListNodeGroupsRequest
message ListNodeGroupsRequest {
    // platform
    string platform = 1 [(validate.rules).string = {max_len: 128}];
    // cluster
    string cluster = 2 [(validate.rules).string = {max_len: 63}];
}

// ListNodeGroupsResponse
message ListNodeGroupsResponse {
    // list of node groups
    repeated devops.models.cluster.v1.NodeGroup node_groups = 1;
}

// GetNodeGroupRequest
message GetNodeGroupRequest {
    // nodegroup identifier
    devops.models.cluster.v1.NodeGroup.Identifier identifier = 1;
}

// GetNodeGroupResponse
message GetNodeGroupResponse {
    // node group
    optional devops.models.cluster.v1.NodeGroup node_group = 1;
}

// ListNodesRequest
message ListNodesRequest {
    // platform
    string platform = 1 [(validate.rules).string = {max_len: 128}];
    // cluster
    string cluster = 2 [(validate.rules).string = {max_len: 63}];
}

// ListNodesResponse
message ListNodesResponse {
    // list of nodes
    repeated google.protobuf.Struct nodes = 1;
}

// GetNamespaceRequest
message GetNodeRequest {
    // cluster where node in
    string cluster = 1 [(validate.rules).string = {max_len: 63}];
    // node name
    string name = 2;
}

// GetNamespaceResponse
message GetNodeResponse {
    // namespace
    optional google.protobuf.Struct node = 1;
}

// ListNamespacesRequest
message ListNamespacesRequest {
    // platform
    string platform = 1 [(validate.rules).string = {max_len: 128}];
    // cluster
    string cluster = 2 [(validate.rules).string = {max_len: 63}];
}

// ListNamespacesResponse
message ListNamespacesResponse {
    // list of namespaces
    repeated google.protobuf.Struct namespaces = 1;
}

// GetNamespaceRequest
message GetNamespaceRequest {
    // namespace environment
    optional devops.models.environment.v1.Environment.Identifier environment = 1;
}

// GetNamespaceResponse
message GetNamespaceResponse {
    // namespace
    optional google.protobuf.Struct namespace = 1;
}

// DeployApplicationsResponse
message DeployApplicationsResponse {
    // environment to deploy
    devops.models.environment.v1.Environment.Identifier environment = 1;

    // applications status
    repeated google.protobuf.Struct applications = 2;
}

// Database API
//------------------------------------------------------------------------------
// ListDatabasesRequest
message ListDatabasesRequest {
    // the platform where the database is running
    optional string platform = 2 [(validate.rules).string = {max_len: 128}];
    // database engine
    optional string engine = 3 [(validate.rules).string = {max_len: 128}];
    // database labels
    map<string, string> labels = 4 [(validate.rules).map = {
        max_pairs: 256,
        keys: {
            string: {max_len: 255}
        },
        values: {
            string: {max_len: 63}
        }
    }];
}

// ListDatabasesResponse
message ListDatabasesResponse {
    // list of database
    repeated devops.models.database.v1.Database databases = 1;
}

// GetDatabaseRequest
message GetDatabaseRequest {
    // database identifier
    devops.models.utils.v1.PlatformIdentifier identifier = 1;
}

// GetDatabaseResponse
message GetDatabaseResponse {
    // database
    optional devops.models.database.v1.Database database = 1;
}

// WatchDatabaseRequest
message WatchDatabaseRequest {
    // database identifier
    devops.models.utils.v1.PlatformIdentifier identifier = 1;
}

// WatchDatabaseResponse
message WatchDatabaseResponse {
    // database
    devops.models.database.v1.Database database = 1;
}

// SqlRequest
message SqlRequest {
    // database to execute sql
    string database = 1;
    // list of sql statement
    repeated string sql = 2;
}

// ExecuteSqlRequest
message ExecuteSqlRequest {
    // database identifier
    devops.models.utils.v1.PlatformIdentifier identifier = 1;
    // actions for create database
    repeated DatabaseAction actions = 2;

    // DatabaseAction
    message DatabaseAction {
        // action type
        oneof action_type {
            // action for create database
            CreateDatabaseAction create_database = 1;
            // action for execute sql
            ExecuteSqlAction execute_sql = 2;
        }

        // CreateDatabaseAction
        message CreateDatabaseAction {
            // database name to create
            string database = 1;
            // Specify whether a user needs to be created
            // If `create_user` is true but the `username` is not specified, a username is automatically generated
            optional bool create_user = 2;
            // Specify the owner of the new database to be created.
            // If `username` is specified:
            //  - If the specified user already exists, the `create_user` parameter is ignored and the user will be directly authorized after the database is created.
            //  - If the specified user does not exist, the `create_user` parameter is either not specified or specified as true,
            //      the user will be created and the corresponding permissions will be granted, otherwise it is considered an illegal parameter.
            // If not specified `username`:
            //  - If the `create_user` parameter is explicitly specified as true, an account will be automatically generated and authorized after the database is created.
            //  - Otherwise, the default account will be authorized after the database is created.
            //  - If the default account does not exist, only the database will be created without authorizing any users.
            // There are two special tags to specify specific users:
            //  - ${db.user.admin}   specifies the administrator user
            //  - ${db.user.default} specifies the default user, If this flag is specified explicitly and the default user does not exist, an error is returned.
            optional string username = 3;
        }

        // ExecuteSqlAction
        message ExecuteSqlAction {
            // sql in database
            string database = 1;
            // sql content
            string sql = 2 [(validate.rules).string = {max_len: 16384}];
            // Specifies the user to execute sql.
            // If not specified, the appropriate user will be selected by default based on the database, such as the owner of the database or the default user.
            // There are two special tags to specify specific users:
            //  - ${db.user.admin}   specifies the administrator user
            //  - ${db.user.default} specifies the default user, If this flag is specified explicitly and the default user does not exist, an error is returned.
            optional string username = 3;
        }
    }
}

// ExecuteSqlResponse
message ExecuteSqlResponse {
    // database identifier
    devops.models.utils.v1.PlatformIdentifier identifier = 1;
    // action outputs
    repeated ActionOutput outputs = 2;

    // ActionOutput
    message ActionOutput {
        // sql in database
        optional string database = 1;
        // result output
        optional string output = 2;
        // extra info
        optional google.protobuf.Struct extra = 3;
    }
}

// Cache API
//------------------------------------------------------------------------------
// ListCachesRequest
message ListCachesRequest {
    // the platform where the cache is running
    optional string platform = 2 [(validate.rules).string = {max_len: 128}];
    // cache engine
    optional string engine = 3 [(validate.rules).string = {max_len: 128}];
    // cache labels
    map<string, string> labels = 4 [(validate.rules).map = {
        max_pairs: 256,
        keys: {
            string: {max_len: 255}
        },
        values: {
            string: {max_len: 63}
        }
    }];
}

// ListCachesResponse
message ListCachesResponse {
    // list of cache
    repeated devops.models.cache.v1.Cache caches = 1;
}

// GetCacheRequest
message GetCacheRequest {
    // cache identifier
    devops.models.utils.v1.PlatformIdentifier identifier = 1;
}

// GetCacheResponse
message GetCacheResponse {
    // cache
    optional devops.models.cache.v1.Cache cache = 1;
}

// ExecuteCacheCommandRequest
message ExecuteCacheCommandRequest {
    // cache identifier
    devops.models.utils.v1.PlatformIdentifier identifier = 1;

    oneof commands_type {
        // general commands
        string commands = 2 [(validate.rules).string = {max_len: 65536}];
        // redis script
        RedisScript redis_script = 3;
    }

    // RedisScript
    message RedisScript{
        // redis script keys
        repeated string keys = 1;
        // redis script argv
        repeated string argv = 2;
        // redis script
        string script = 3 [(validate.rules).string = {max_len: 65536}];
    }
}

// ExecuteCacheCommandResponse
message ExecuteCacheCommandResponse {
    // database identifier
    devops.models.utils.v1.PlatformIdentifier identifier = 1;
    // action outputs
    google.protobuf.Value output = 2;
}

// Message Queue API
//------------------------------------------------------------------------------
// ListMessageQueuesRequest
message ListMessageQueuesRequest {
    // the platform where the message queue is running
    optional string platform = 2 [(validate.rules).string = {max_len: 128}];
    // MQ engine
    optional string engine = 3 [(validate.rules).string = {max_len: 128}];
    // MQ labels
    map<string, string> labels = 4 [(validate.rules).map = {
        max_pairs: 256,
        keys: {
            string: {max_len: 255}
        },
        values: {
            string: {max_len: 63}
        }
    }];
}

// ListMessageQueuesResponse
message ListMessageQueuesResponse {
    // list of message queues
    repeated devops.models.mq.v1.MessageQueue message_queues = 1;
}

// GetMessageQueueRequest
message GetMessageQueueRequest {
    // message queue identifier
    devops.models.utils.v1.PlatformIdentifier identifier = 1;
}

// GetMessageQueueResponse
message GetMessageQueueResponse {
    // message queue
    optional devops.models.mq.v1.MessageQueue message_queue = 1;
}

// ListTopicsRequest
message ListTopicsRequest {
    // message queue identifier
    devops.models.utils.v1.PlatformIdentifier identifier = 1;
    // topic tenant
    optional string tenant = 2;
    // topic namespace
    optional string namespace = 3;
    // whether should list internal topics.
    optional bool internal = 4;
}

// ListTopicsResponse
message ListTopicsResponse {
    // list of topics
    repeated devops.models.mq.v1.Topic topics = 1;
}

// CreateTopicsRequest
message CreateTopicsRequest {
    // message queue identifier
    devops.models.utils.v1.PlatformIdentifier identifier = 1;
    // list of topic details
    repeated TopicInfo topics = 2;

    // TopicInfo
    message TopicInfo {
        // topic name
        string name = 1;
        // topic tenant
        optional string tenant = 2;
        // topic namespace
        optional string namespace = 3;
        // partition number
        optional int32 partition_number = 4;
        // replication factor
        optional int32 replication_factor = 5;
        // extra config
        optional google.protobuf.Struct config = 6;
    }
}

// CreateTopicsResponse
message CreateTopicsResponse {
    // list of topic names
    repeated string topics = 1;
}

// DeleteTopicsRequest
message DeleteTopicsRequest {
    // message queue identifier
    devops.models.utils.v1.PlatformIdentifier identifier = 1;
    // list of topic names
    repeated string topics = 2;
}

// DeleteTopicsResponse
message DeleteTopicsResponse {
    // list of topic names
    repeated string topics = 1;
}

// GetTopicsRequest
message GetTopicsRequest {
    // message queue identifier
    devops.models.utils.v1.PlatformIdentifier identifier = 1;
    // list of topic name
    repeated string topics = 2;
}

// GetTopicsResponse
message GetTopicsResponse {
    // list of topics
    repeated devops.models.mq.v1.Topic topics = 1;
}
