// @since 2024/9/11 3:15 PM
// <AUTHOR>

syntax = "proto3";

package moego.devops.models.environment.v1;

import "devops/models/utils/v1/platform_models.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-devops-platform/out/go/moego/devops/models/environment/v1;environmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.devops.models.environment.v1";

// Environment
message Environment {
    // environment identifier
    Identifier identifier = 1;
    // environment status
    Status status = 2;
    // environment is managed?
    bool is_managed = 3;
    // environment name, use to display
    optional string name = 4 [(validate.rules).string = {max_len: 64}];
    // environment description
    optional string description = 5 [(validate.rules).string = {max_len: 1024}];
    // list of databases
    repeated devops.models.utils.v1.PlatformIdentifier databases = 6;
    // list of caches
    repeated devops.models.utils.v1.PlatformIdentifier caches = 7;
    // list of message queues
    repeated devops.models.utils.v1.PlatformIdentifier message_queues = 8;

    // extra info
    optional Extra extra = 13;
    // create time
    google.protobuf.Timestamp created_at = 14;
    // update time
    google.protobuf.Timestamp updated_at = 15;
    // delete time
    google.protobuf.Timestamp deleted_at = 16;

    // Identifier: unique identifier for an environment
    message Identifier {
        // kubernetes cluster
        string cluster = 1 [(validate.rules).string = {max_len: 63}];
        // kubernetes namespace
        string namespace = 2 [(validate.rules).string = {max_len: 63}];
    }

    // Extra
    message Extra {
        // ignored app
        repeated string ignored_apps = 1;
    }

    // Status
    enum Status {
        // unspecified
        ENVIRONMENT_STATUS_UNSPECIFIED = 0;
        // available
        AVAILABLE = 1;
        // pending
        PENDING = 2;
        // deploying
        DEPLOYING = 3;
        // updating
        UPDATING = 4;
        // deleting
        DELETING = 5;
        // deleted
        DELETED = 6;
    }

}
