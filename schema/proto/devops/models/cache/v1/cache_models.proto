// @since 2024/9/11 3:15 PM
// <AUTHOR>

syntax = "proto3";

package moego.devops.models.cache.v1;

import "devops/models/utils/v1/network_models.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "devops/models/utils/v1/platform_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-devops-platform/out/go/moego/devops/models/cache/v1;cachepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.devops.models.cache.v1";

// Cache
message Cache {
    // cache identifier
    devops.models.utils.v1.PlatformIdentifier identifier = 1;
    // cache status
    string status = 2;
    // cache engine
    string engine = 3;
    // cache engine version
    string version = 4;
    // cache endpoints
    repeated devops.models.utils.v1.Endpoint endpoints = 5;
    // cache engine version
    int32 port = 6;
    // cache instance class
    string instance_class = 7;
    // cache storage size
    int32 allocated_size = 8;
    // whether the cache is publicly accessible
    bool publicly_accessible = 9;
    // whether the cache is enable tls
    bool tls = 10;
    // The labels attached to the cache
    map<string, string> labels = 13 [(validate.rules).map = {
        max_pairs: 256,
        keys: {
            string: {max_len: 255}
        },
        values: {
            string: {max_len: 63}
        }
    }];
    // extra info
    google.protobuf.Struct extra = 14;
    // create time
    google.protobuf.Timestamp created_at = 15;
    // update time
    google.protobuf.Timestamp updated_at = 16;
}
