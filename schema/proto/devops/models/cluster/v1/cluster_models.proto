// @since 2024/9/13 3:40 PM
// <AUTHOR>

syntax = "proto3";

package moego.devops.models.cluster.v1;

import "devops/models/utils/v1/platform_models.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-devops-platform/out/go/moego/devops/models/cluster/v1;clusterpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.devops.models.cluster.v1";

// Cluster
message Cluster {
    // cluster identifier
    devops.models.utils.v1.PlatformIdentifier identifier = 1;
    // cluster version
    string version = 2;
    // cluster endpoint
    string endpoint = 3;
    // cluster status
    string status = 4;

    // cluster labels
    map<string, string> labels = 6;
    // create time
    google.protobuf.Timestamp created_at = 7;
}

// NodeGroup
message NodeGroup {
    // nodegroup identifier
    Identifier identifier = 1;
    // node group status
    string status = 2;
    // The instance class of the node group
    string instance_class = 3;
    // The image used by the node group
    string image = 4;
    // Minimum number of nodes in a node group
    optional int32 min_size = 5;
    // Maximum number of nodes in a node group
    optional int32 max_size = 6;
    // Desired number of nodes in a node group
    optional int32 desired_size = 7;
    // disk size for instance
    optional int32 disk_size = 8;
    // The labels attached to the node group
    map<string, string> labels = 13 [(validate.rules).map = {
        max_pairs: 256,
        keys: {
            string: {max_len: 255}
        },
        values: {
            string: {max_len: 63}
        }
    }];

    // extra info
    google.protobuf.Struct extra = 14;
    // create time
    google.protobuf.Timestamp created_at = 15;
    // update time
    google.protobuf.Timestamp updated_at = 16;

    // Identifier
    message Identifier {
        // The platform where the node group is located
        string platform = 1 [(validate.rules).string = {max_len: 128}];
        // The cluster where the node group is located
        string cluster = 2 [(validate.rules).string = {max_len: 63}];
        // node group name
        string name = 3 [(validate.rules).string = {max_len: 63}];
    }
}

// ResourceIdentifier
message ResourceIdentifier {
    // api group/version
    optional string api_version = 1;
    // resource kind
    string kind = 2;
    // resource name
    string name = 3;
}

