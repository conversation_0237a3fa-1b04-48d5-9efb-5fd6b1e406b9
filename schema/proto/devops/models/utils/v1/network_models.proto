syntax = "proto3";

package moego.devops.models.utils.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-devops-platform/out/go/moego/devops/models/utils/v1;utilspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.devops.models.utils.v1";

// Endpoint
message Endpoint {
    // address
    string address = 1 [(validate.rules).string = { max_len: 1024}];
    // port
    int32 port = 2 [(validate.rules).int32 = { lt: 65536}];
}