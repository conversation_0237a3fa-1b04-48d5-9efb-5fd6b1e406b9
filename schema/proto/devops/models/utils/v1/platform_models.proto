// @since 2024/9/11 00:40 AM
// <AUTHOR>

syntax = "proto3";

package moego.devops.models.utils.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-devops-platform/out/go/moego/devops/models/utils/v1;utilspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.devops.models.utils.v1";

// PlatformType
enum PlatformType {
    // unspecified
    PLATFORM_TYPE_UNSPECIFIED = 0;
    // self-hosted
    SELF_HOSTED = 1;
    // AWS Elastic Kubernetes Service
    AWS_EKS = 11;
    // AWS Elastic Container Registry
    AWS_ECR = 12;
    // AWS Relation Database Service
    AWS_RDS = 13;
    // AWS Managed Streaming for Apache Kafka
    AWS_MSK = 14;
    // AWS Simple Storage Service
    AWS_S3 = 15;
    // AWS Elastic Cache
    AWS_ELASTIC_CACHE = 16;
}


// PlatformIdentifier
message PlatformIdentifier {
    // resource platform
    string platform = 1 [(validate.rules).string = {max_len: 128}];
    // resource identifier
    string identifier = 2 [(validate.rules).string = {max_len: 1024}];
}
