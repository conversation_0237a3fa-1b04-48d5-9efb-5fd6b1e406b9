// @since 2024/9/13 3:15 PM
// <AUTHOR>

syntax = "proto3";

package moego.devops.models.utils.v1;

option go_package = "github.com/MoeGolibrary/moego-devops-platform/out/go/moego/devops/models/utils/v1;utilspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.devops.models.utils.v1";

// Int32Items
message Int32Items {
    // list of values in int32
    repeated int32 items = 1;
}

// Int64Items
message Int64Items {
    // list of values in int64
    repeated int64 items = 1;
}

// StringItems
message StringItems {
    // list of values in string
    repeated string items = 1;
}
