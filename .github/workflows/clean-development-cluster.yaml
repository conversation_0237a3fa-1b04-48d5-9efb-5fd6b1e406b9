name: "Clean Development Cluster"

on:
  schedule:
    - cron: '0 */4 * * *'
  workflow_dispatch: {}

jobs:
  devops:
    runs-on: ubuntu-latest
    env:
      AWS_REGION: "${{ vars.AWS_REGION }}"
      AWS_ECR_URL: "${{ vars.AWS_ECR_URL }}"
      DEVOPS_EXECUTOR_IMAGE: "${{ vars.AWS_ECR_URL }}/moego-devops-platform:moego-devops-executor-1.4.0"
      GITHUB_TOKEN: "${{ secrets.DEVOPS_CONSOLE_TOKEN }}"
    steps:
      - uses: actions/checkout@v4
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      - name: Login AWS ECR
        run: |
          aws --region ${AWS_REGION} ecr get-login-password | docker login -u AWS --password-stdin ${AWS_ECR_URL}
      - name: Setup Kubernetes
        run: |
          aws eks --region ${AWS_REGION} update-kubeconfig --name moego-server-cluster-development --alias moego-server-cluster-development
      - name: Setup DevOps Executor
        run: |
          docker run --rm -v .:/tmp -w /tmp --entrypoint cp ${DEVOPS_EXECUTOR_IMAGE} /usr/local/bin/devops-executor ./
          ./devops-executor version
      - name: Clean Development Cluster
        run: |
          ./devops-executor clean-cluster --config config/cluster-cleaner.yaml --debug
