name: "<PERSON><PERSON><PERSON> Console Explainer"

on:
  pull_request:
    branches:
      - main
    paths:
      - 'src/**/*.yaml'

jobs:
  devops:
    runs-on: ubuntu-latest
    env:
      AWS_REGION: "${{ vars.AWS_REGION }}"
      AWS_ECR_URL: "${{ vars.AWS_ECR_URL }}"
      DEVOPS_CONSOLE_HOST: "${{ vars.DEVOPS_CONSOLE_HOST }}"
      DEVOPS_CONSOLE_TOKEN: "${{ secrets.DEVOPS_CONSOLE_TOKEN }}"
      DEVOPS_EXECUTOR_IMAGE: "${{ vars.AWS_ECR_URL }}/moego-devops-platform:moego-devops-executor-1.2.1"
      REQUEST_COMMITER_NAME: "${{ github.event.head_commit.committer.name }}"
      REQUEST_COMMITER_EMAIL: "${{ github.event.head_commit.committer.email }}"
    steps:
      - uses: actions/checkout@v4
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      - name: Login AWS ECR
        run: |
          aws --region ${AWS_REGION} ecr get-login-password | docker login -u AWS --password-stdin ${AWS_ECR_URL}
      - name: explain DevOps Console Request
        run: |
          docker run --rm -v $(pwd):/data -w /data -e DEVOPS_CONSOLE_TOKEN=${DEVOPS_CONSOLE_TOKEN} \
            ${DEVOPS_EXECUTOR_IMAGE} devops-console \
            -f src/requests.yaml                    \
            --dry-run                               \
            --debug                                 \
            --host ${DEVOPS_CONSOLE_HOST}           \
            --header "x-repo-commiter-name=${REQUEST_COMMITER_NAME}" \
            --header "x-repo-commiter-email=${REQUEST_COMMITER_EMAIL}"
