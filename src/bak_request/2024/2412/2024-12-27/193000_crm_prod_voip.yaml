name: "Support VOIP for engagement center"
description: "add column"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_engagement"
            sql: |
              create table twilio_account_keys
              (
                  id                            bigserial
                      primary key,
                  account_sid                   text      default ''::text not null
                      constraint uk_twilio_account_keys_account_sid
                          unique,
                  auth_token                    text      default ''::text not null,
                  api_key                       text      default ''::text not null,
                  api_secret                    text      default ''::text not null,
                  voice_application_sid         text      default ''::text not null,
                  ios_notify_credential_sid     text      default ''::text not null,
                  android_notify_credential_sid text      default ''::text not null,
                  created_at                    timestamp default now()    not null,
                  updated_at                    timestamp default now()    not null
              );

              create index idx_twilio_account_keys_account_sid
                  on twilio_account_keys (account_sid);
  