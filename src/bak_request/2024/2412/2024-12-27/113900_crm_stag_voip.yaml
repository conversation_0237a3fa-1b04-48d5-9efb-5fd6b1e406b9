name: "Support VOIP for engagement center"
description: "add column"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_engagement"
            sql: |
              alter table calling_setting
              add column ring_duration integer not null default 20;
  