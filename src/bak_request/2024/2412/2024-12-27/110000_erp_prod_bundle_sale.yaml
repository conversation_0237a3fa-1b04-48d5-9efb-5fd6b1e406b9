name: "Add table for bundle sale"
description: "add new table"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_offering"
            sql: |
              create table service_bundle_sale_mapping
              (
                  id                bigserial PRIMARY KEY,
                  company_id        bigint                              not null,
                  service_id        bigint                              not null,
                  bundle_service_id bigint                              not null,
                  created_at        timestamp default CURRENT_TIMESTAMP not null
              );

              create index service_id_idx
                  on service_bundle_sale_mapping (service_id, company_id);