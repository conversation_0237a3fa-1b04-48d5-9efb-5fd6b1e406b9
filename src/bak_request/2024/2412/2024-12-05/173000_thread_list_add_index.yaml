name: "add index for moe_business_message_thread"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_message"
            username: "${db.user.admin}"
            sql: |
              create index idx_cid_status_last_message_time on moe_message.moe_business_message_thread(company_id, status, last_message_time);