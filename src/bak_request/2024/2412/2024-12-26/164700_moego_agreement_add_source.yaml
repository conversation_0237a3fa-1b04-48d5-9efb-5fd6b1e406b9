name: "pg moego_agreement agreement 表加 source "
description: "pg moego_agreement  agreement 表加 source"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_agreement"
            sql: |
              ALTER TABLE "public"."agreement"
                ADD COLUMN "source" int4 NOT NULL DEFAULT 1;
              COMMENT ON COLUMN "public"."agreement"."source" IS '1-company 2-enterprise';
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_agreement"
            sql: |
              ALTER TABLE "public"."agreement"
                ADD COLUMN "source" int4 NOT NULL DEFAULT 1;
              COMMENT ON COLUMN "public"."agreement"."source" IS '1-company 2-enterprise';