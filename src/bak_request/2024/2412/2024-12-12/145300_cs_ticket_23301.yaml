name: "修改noshow数据"
description: "fix order source type error and update order status"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              UPDATE public.order SET status = 3, payment_status = 'PAID', sub_total_amount = 70.00, remain_amount = 0.00, paid_amount = 73.50, total_amount = 73.50, extra_fee_amount = 3.50, update_time = NOW() WHERE id = 131614304 AND business_id = '101890';