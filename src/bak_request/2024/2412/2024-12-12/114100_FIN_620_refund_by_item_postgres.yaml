name: "New Invoice 支持 Refund By Item"
description: "New Invoice 支持 Refund By Item"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              ---- 正向流程
              ---- order_payment
              drop table if EXISTS order_payment;
              create table if not exists order_payment (
                       id bigint generated always as identity primary key,
                       order_id             bigint default 0 not null,
                       company_id           bigint default 0 not null,
                       business_id         bigint default 0 not null,
                       staff_id             bigint default 0 not null,
                       customer_id          bigint default 0 not null,
                       payment_id           bigint default 0 not null,
                       payment_method_id    bigint default 0 not null,
                       payment_method  TEXT   default '' not null,
                       payment_method_extra JSONB  default '{}' not null,
                       payment_method_vendor TEXT default '' not null,
                       is_online            BOOLEAN default false not null,
                       is_deposit           BOOLEAN default false not null,
                       paid_by              TEXT default '' not null,
                       currency             TEXT default '' not null,
                       total_amount         numeric(20, 2) default 0.00 not null,
                       amount               numeric(20, 2) default 0.00 not null,
                       refunded_amount      numeric(20, 2) default 0.00 not null,
                       processing_fee       numeric(20, 2) default -1.00 not null,
                       convenience_fee       numeric(20, 2) default 0.00 not null,
                       refunded_convenience_fee numeric(20, 2) default 0.00 not null,
                       payment_tips         numeric(20, 2) default -1.00 not null,
                       payment_tips_before_create         numeric(20, 2) default -1.00 not null,
                       payment_tips_after_create          numeric(20, 2) default -1.00 not null,
                       payment_status       TEXT default 'TYPE_UNSPECIFIED' not null,
                       reason               TEXT default '' not null,
                       pay_time              timestamp default to_timestamp(0) not null,
                       cancel_time           timestamp default to_timestamp(0) not null,
                       fail_time             timestamp default to_timestamp(0) not null,
                       create_time           timestamp default now() not null,
                       update_time           timestamp default now() not null
              );
              create index idx_order_id on order_payment(order_id);
              create index idx_company_customer on order_payment(company_id, customer_id);
              -- order payment 添加 status + create_time 索引
              create index idx_order_payment_create_time_pstc on order_payment(create_time) where payment_status = 'ORDER_PAYMENT_STATUS_TRANSACTION_CREATED';
              CREATE TRIGGER auto_update_order_payment_update_time
                  BEFORE UPDATE ON order_payment
                  FOR EACH ROW
                  EXECUTE PROCEDURE update_modified_column();
              -- moego_order.order
              ALTER TABLE public.order
                  add COLUMN tax_round_mod smallint default  0 not null,
                  ADD COLUMN company_id           bigint default 0 not null,
                  ADD COLUMN currency_code TEXT default '' not null;
              ALTER TABLE public.order_line_tax
                  ADD COLUMN tax_name             TEXT default '' not null;
              -- moego_order.order_line_item
              ALTER TABLE public.order_line_item
                  ADD COLUMN pet_id                      bigint default 0 not null,
                  ADD COLUMN tax_id               bigint default 0 not null,
                  ADD COLUMN tax_name             TEXT default '' not null,
                  ADD COLUMN tax_rate      numeric(20, 4) default 0.00 not null,
                  ADD COLUMN currency_code        TEXT default '' not null,
                  ADD COLUMN refunded_quantity         int default 0 not null,
                  ADD COLUMN refunded_amount      numeric(20, 2) default 0.00 not null,
                  ADD COLUMN refunded_tax_amount      numeric(20, 2) default 0.00 not null,
                  ADD COLUMN refunded_discount_amount      numeric(20, 2) default 0.00 not null,
                  ADD COLUMN refunded_convenience_fee      numeric(20, 2) default 0.00 not null;
              -- moego_order.order_staff_split_detail
              ALTER TABLE public.order_staff_split_detail
                  ADD COLUMN object_id         bigint default 0 not null,
                  ADD COLUMN pet_id         bigint default 0 not null;
        - execute_sql:
            database: "moego_order"
            sql: |
              ---- 逆向流程
              CREATE TABLE IF NOT EXISTS refund_order
              (
                id                     BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
                order_id               BIGINT         DEFAULT 0                     NOT NULL,
                company_id             BIGINT         DEFAULT 0                     NOT NULL,
                business_id            BIGINT         DEFAULT 0                     NOT NULL,
                staff_id               BIGINT         DEFAULT 0                     NOT NULL,
                customer_id            BIGINT         DEFAULT 0                     NOT NULL,
                refund_mode            TEXT           DEFAULT ''                    NOT NULL,
                refund_reason          TEXT           DEFAULT ''                    NOT NULL,
                order_status_snapshot  TEXT           DEFAULT ''                    NOT NULL,
                currency_code          TEXT           DEFAULT ''                    NOT NULL,
              
                refund_total_amount    NUMERIC(20, 2) DEFAULT 0.00                  NOT NULL,
                refund_item_sub_total  NUMERIC(20, 2) DEFAULT 0.00                  NOT NULL,
                refund_discount_amount NUMERIC(20, 2) DEFAULT 0.00                  NOT NULL,
                refund_tips_amount     NUMERIC(20, 2) DEFAULT 0.00                  NOT NULL,
                refund_convenience_fee NUMERIC(20, 2) DEFAULT 0.00                  NOT NULL,
                refund_tax_amount      NUMERIC(20, 2) DEFAULT 0.00                  NOT NULL,
              
                refund_order_status    TEXT           DEFAULT ''                    NOT NULL,
                create_time            TIMESTAMP      DEFAULT now()                 NOT NULL,
                update_time            TIMESTAMP      DEFAULT now()                 NOT NULL,
                refund_time            TIMESTAMP      DEFAULT to_timestamp(0) NOT NULL
              );
              
              CREATE INDEX refund_order_idx_order_id ON refund_order (order_id);
              CREATE INDEX refund_order_idx_bid_cid ON refund_order (business_id, customer_id);
              
              CREATE TABLE IF NOT EXISTS refund_order_item
              (
                id                     BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
                order_id               BIGINT         DEFAULT 0     NOT NULL,
                order_item_id          BIGINT         DEFAULT 0     NOT NULL,
                refund_order_id        BIGINT         DEFAULT 0     NOT NULL,
                company_id             BIGINT         DEFAULT 0     NOT NULL,
                business_id            BIGINT         DEFAULT 0     NOT NULL,
                staff_id               BIGINT         DEFAULT 0     NOT NULL,
                customer_id            BIGINT         DEFAULT 0     NOT NULL,
              
                item_type              TEXT           DEFAULT ''    NOT NULL,
                item_id                BIGINT         DEFAULT 0     NOT NULL,
                item_name              TEXT           DEFAULT ''    NOT NULL,
                item_description       TEXT           DEFAULT ''    NOT NULL,
                item_unit_price        NUMERIC(20, 2) DEFAULT 0.00  NOT NULL,
              
                tax_id                 BIGINT         DEFAULT 0     NOT NULL,
                tax_name               TEXT           DEFAULT ''    NOT NULL,
                tax_rate               NUMERIC(20, 4) DEFAULT 0.00  NOT NULL,
                currency_code          TEXT           DEFAULT ''    NOT NULL,
                refund_item_mode       TEXT           DEFAULT ''    NOT NULL,
                refund_total_amount    NUMERIC(20, 2) DEFAULT 0.00  NOT NULL,
                refund_quantity        INT            DEFAULT 0     NOT NULL,
                refund_amount          NUMERIC(20, 2) DEFAULT 0.00  NOT NULL,
                refund_discount_amount NUMERIC(20, 2) DEFAULT 0.00  NOT NULL,
                refund_tax_amount      NUMERIC(20, 2) DEFAULT 0.00  NOT NULL,
              
                create_time            TIMESTAMP      DEFAULT now() NOT NULL
              );
              
              CREATE INDEX refund_order_item_idx_order_id ON refund_order_item (order_id);
              CREATE INDEX refund_order_item_idx_refund_order_id ON refund_order_item (refund_order_id);
              CREATE INDEX refund_order_item_idx_order_item_id ON refund_order_item (order_item_id);
              CREATE INDEX refund_order_item_idx_bid_cid ON refund_order_item (business_id, customer_id);
              
              CREATE TABLE IF NOT EXISTS refund_order_payment
              (
                id                           BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
                order_id                     BIGINT         DEFAULT 0               NOT NULL,
                order_payment_id             BIGINT         DEFAULT 0               NOT NULL,
                refund_order_id              BIGINT         DEFAULT 0               NOT NULL,
                company_id                   BIGINT         DEFAULT 0               NOT NULL,
                business_id                  BIGINT         DEFAULT 0               NOT NULL,
                staff_id                     BIGINT         DEFAULT 0               NOT NULL,
                customer_id                  BIGINT         DEFAULT 0               NOT NULL,
              
                order_status_snapshot        TEXT           DEFAULT ''              NOT NULL,
                refund_payment_method_id     BIGINT         DEFAULT 0               NOT NULL,
                refund_payment_method        TEXT           DEFAULT ''              NOT NULL,
                refund_payment_method_extra  jsonb          DEFAULT '{}'            NOT NULL,
                refund_payment_method_vendor TEXT           DEFAULT ''              NOT NULL,
              
                currency_code                TEXT           DEFAULT ''              NOT NULL,
                refund_amount                NUMERIC(20, 2) DEFAULT 0.00            NOT NULL,
                refund_convenience_fee       NUMERIC(20, 2) DEFAULT 0.00            NOT NULL,
              
                description                  TEXT           DEFAULT ''              NOT NULL,
                refund_status                TEXT           DEFAULT ''              NOT NULL,
                refund_status_reason         TEXT           DEFAULT ''              NOT NULL,
              
                create_time                  TIMESTAMP      DEFAULT now()           NOT NULL,
                update_time                  TIMESTAMP      DEFAULT now()           NOT NULL,
                refund_time                  TIMESTAMP      DEFAULT to_timestamp(0) NOT NULL,
                fail_time                    TIMESTAMP      DEFAULT to_timestamp(0) NOT NULL,
                cancel_time                  TIMESTAMP      DEFAULT to_timestamp(0) NOT NULL
              );
              
              CREATE INDEX refund_order_payment_idx_order_id_order_status ON refund_order_payment (order_id, order_status_snapshot);
              CREATE INDEX refund_order_payment_idx_refund_order_id ON refund_order_payment (refund_order_id);
              CREATE INDEX refund_order_payment_idx_order_payment_id ON refund_order_payment (order_payment_id);
              CREATE INDEX refund_order_payment_idx_bid_cid ON refund_order_payment (business_id, customer_id);
              CREATE INDEX refund_order_payment_idx_refund_status_update_time_index ON refund_order_payment (refund_status, update_time);
              COMMENT ON INDEX refund_order_payment_idx_refund_status_update_time_index IS '用于 CRON 补偿异常中断的 Refund 流程.';
        - execute_sql:
            database: "moego_permission"
            sql: |
              -- database: moego_permission 新增权限
              INSERT INTO public."permission" 
              (ID, "name",display_name,description,parent_permission_id,category_id) VALUES
              (106,'canProcessRefund','Can process refund','',0,1);
              -- 新权限初始化： 确认新增权限 ID 为 xx 后再执行
              insert into public."role_permission_mapping" (role_id, permission_id) 
              select id, 106 from role where deleted_at is null;