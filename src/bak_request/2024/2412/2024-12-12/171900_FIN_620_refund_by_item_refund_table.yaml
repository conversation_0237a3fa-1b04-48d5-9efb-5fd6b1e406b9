name: "New Invoice 支持 Refund By Item"
description: "New Invoice 支持 Refund By Item，refund 表增加字段和索引"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              ALTER TABLE `moe_payment`.`refund`
                ADD COLUMN `refund_order_payment_id` bigint NOT NULL DEFAULT 0 COMMENT 'moego_order.refund_order_payment primary key';
                
              CREATE INDEX idx_refund_order_payment_id
                ON `moe_payment`.`refund`(refund_order_payment_id);
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              ALTER TABLE refund_order_payment
                ADD COLUMN refund_payment_id BIGINT NOT NULL DEFAULT 0;
