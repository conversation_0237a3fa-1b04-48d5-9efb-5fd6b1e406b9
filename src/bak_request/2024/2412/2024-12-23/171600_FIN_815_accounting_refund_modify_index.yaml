name: "accounting sync refund modify index"
description: "accounting sync refund modify index"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_accounting"
            sql: |
              CREATE UNIQUE INDEX uniq_channel_refund_line_item_id ON public.biz_refund_invoice_line_item USING btree (channel_refund_line_item_id, channel_type);
              DROP INDEX IF EXISTS uniq_refund_invoice_line_item_id;