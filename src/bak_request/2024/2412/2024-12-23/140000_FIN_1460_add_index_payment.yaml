name: "payment新增索引"
description: "payment新增索引 business_id_create_time"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              ---- 添加index
              ---- moe_payment.payment
              CREATE INDEX idx_business_id_create_time
                ON `moe_payment`.`payment`(business_id, create_time);