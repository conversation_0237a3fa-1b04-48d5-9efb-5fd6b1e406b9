name: "accounting retry log modify"
description: "accounting retry log modify"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_accounting"
            sql: |
              DELETE FROM public.accounting_sync_retry_log WHERE retry_entity_type = 'PAYMENT' AND retry_entity_id IN ('********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********');
              UPDATE public.accounting_sync_log SET sync_entity_status = 'SUCCESS' WHERE sync_entity_type = 'PAYMENT' AND sync_entity_id IN ('********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********');