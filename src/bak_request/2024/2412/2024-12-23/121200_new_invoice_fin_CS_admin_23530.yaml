name: "new invoice cancel appointment after completing"
description: "manually cancel appointment after completing invoice"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              UPDATE public.order SET status = 3, update_time = NOW() where id = 128836854 and business_id = 111382;
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              UPDATE moe_grooming.moe_grooming_appointment SET status = 4, update_time = unix_timestamp(now()) WHERE id = 55930619 and business_id = 111382;