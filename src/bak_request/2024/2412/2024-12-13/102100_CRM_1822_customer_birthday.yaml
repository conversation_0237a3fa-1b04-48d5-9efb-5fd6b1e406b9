name: "customer 添加 customer birthday 字段"
description: "customer 添加 customer birthday 字段"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_customer"
            sql: |
              ALTER TABLE moe_business_customer
              ADD COLUMN birthday DATETIME DEFAULT NULL COMMENT '顾客生日';
