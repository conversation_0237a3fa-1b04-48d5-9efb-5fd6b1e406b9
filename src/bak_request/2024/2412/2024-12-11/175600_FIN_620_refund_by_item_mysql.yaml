name: "New Invoice 支持 Refund By Item"
description: "New Invoice 支持 Refund By Item"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              ALTER TABLE `moe_payment`.`payment`
                ADD COLUMN `order_payment_id` bigint NOT NULL DEFAULT 0 COMMENT 'Primary key reference to moego_order.order_payment';