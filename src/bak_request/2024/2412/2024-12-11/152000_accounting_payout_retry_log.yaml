name: "Accounting payout 数据不重试"
description: "Accounting payout 数据不重试"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_accounting"
            sql: |
              DELETE FROM public.accounting_sync_retry_log WHERE id in (3372,3373,3374,3375,3376);