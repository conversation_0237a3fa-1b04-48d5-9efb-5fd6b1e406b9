name: "Update pricing rule apply log"
description: "update pricing rule apply log"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_appointment"
            sql: |
              alter table pricing_rule_apply_log
              add is_using_rule boolean default true not null;
            
              alter table pricing_rule_apply_log
              add source_id bigint default 0 not null;
              
              alter table pricing_rule_apply_log
              add source_type integer default 1 not null;
              
              comment on column public.pricing_rule_apply_log.source_type is 'source type, 1-appointment, 2-booking request';
              
              create index idx_company_id_source_id_source_type
              on pricing_rule_apply_log (company_id, source_id, source_type);
              
              update pricing_rule_apply_log
              set source_id = appointment_id
              where source_id = 0;
