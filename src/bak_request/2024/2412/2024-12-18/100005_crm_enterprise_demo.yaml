name: "dml for enterprise demo"
description: "fix email template"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moego_message"
            username: "${db.user.admin}"
            sql: |
              GRANT UPDATE ON moe_message.* TO 'developer_ark'@'%';
