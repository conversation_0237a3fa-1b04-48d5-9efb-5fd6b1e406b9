name: "dml for enterprise demo"
description: "pg moego_enterprise 新增 campaign & price book"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_enterprise"
            username: "${db.user.admin}"
            sql: |
              CREATE EXTENSION IF NOT EXISTS pg_trgm;
        - execute_sql:
            database: "moego_enterprise"
            sql: |
              create table if not exists service
              (
              id                bigint generated always as identity primary key,
              enterprise_id     bigint         default 0                                not null,
              price_book_id     bigint         default 0                                not null,
              name              text           default ''                               not null,
              service_item_type text           default 'SERVICE_ITEM_TYPE_UNSPECIFIED'  not null,
              category_id       bigint         default 0                                not null,
              description       text           default ''                               not null,
              inactive          boolean        default false                            not null,
              color             text           default ''                               not null,
              sort              integer        default 0                                not null,
              price             numeric(20, 2) default 0.00                             not null,
              price_unit        text           default 'SERVICE_PRICE_UNIT_UNSPECIFIED' not null,
              tax_rate          bigint         default 0                                not null,
              duration          text           default '0s'                             not null,
              max_duration      text           default '0s'                             not null,
              limitation        text           default '{}'                             not null,
              created_at        timestamp      default now()                            not null,
              updated_at        timestamp      default now()                            not null,
              deleted_at        timestamp      default null
              );
              
              CREATE TRIGGER update_at
              BEFORE UPDATE
              ON service
              FOR EACH ROW
              EXECUTE PROCEDURE update_at();

              create table if not exists service_category
              (
              id                bigint generated always as identity primary key,
              enterprise_id     bigint    default 0                               not null,
              price_book_id     bigint         default 0                                not null,
              service_item_type text      default 'SERVICE_ITEM_TYPE_UNSPECIFIED' not null,
              name              text      default ''                              not null,
              sort              integer   default 0                               not null,
              created_at        timestamp default now()                           not null,
              updated_at        timestamp default now()                           not null,
              deleted_at        timestamp default null
              );
              
              CREATE TRIGGER update_at
              BEFORE UPDATE
              ON service_category
              FOR EACH ROW
              EXECUTE PROCEDURE update_at();

              create table if not exists service_change
              (
              id            bigint generated always as identity primary key,
              enterprise_id bigint    default 0                  not null,
              history_id    bigint    default 0                  not null,
              target_id     bigint    default 0                  not null,
              target_type   text      default 'TYPE_UNSPECIFIED' not null,
              target_name   text      default ''                 not null,
              service_id    bigint    default 0                  not null,
              service_name  text      default ''                 not null,
              created_at    timestamp default now()              not null,
              updated_at    timestamp default now()              not null,
              deleted_at    timestamp default null
              );
              
              CREATE TRIGGER update_at
              BEFORE UPDATE
              ON service_change
              FOR EACH ROW
              EXECUTE PROCEDURE update_at();
              
              create table if not exists service_change_history
              (
              id               bigint generated always as identity primary key,
              enterprise_id    bigint    default 0     not null,
              service_id       bigint    default 0     not null,
              impacted_tenants bigint    default 0     not null,
              roll_out_at      timestamp default null,
              created_at       timestamp default now() not null,
              updated_at       timestamp default now() not null,
              deleted_at       timestamp default null
              );
              
              CREATE TRIGGER update_at
              BEFORE UPDATE
              ON service_change_history
              FOR EACH ROW
              EXECUTE PROCEDURE update_at();

              create table if not exists campaign_template
              (
              id                   bigint generated always as identity primary key,
              enterprise_id        bigint    default 0                    not null,
              staff_id             bigint    default 0                    not null,
              internal_template_id bigint    default 0                    not null,
              name                 text      default ''                   not null,
              cover                text      default ''                   not null,
              type                 text      default 'TYPE_UNSPECIFIED'   not null,
              status               text      default 'STATUS_UNSPECIFIED' not null,
              created_at           timestamp default now()                not null,
              updated_at           timestamp default now()                not null,
              deleted_at           timestamp default null
              );
              
              create index if not exists idx_campaign_template_enterprise on campaign_template (enterprise_id);
              create index if not exists idx_campaign_template_name_gin_trgm on campaign_template using gin (name gin_trgm_ops);
              
              CREATE TRIGGER update_at
              BEFORE UPDATE
              ON campaign_template
              FOR EACH ROW
              EXECUTE PROCEDURE update_at();
              
              create table if not exists campaign_template_push_record
              (
              id                   bigint generated always as identity primary key,
              enterprise_id        bigint    default 0                  not null,
              template_id          bigint    default 0                  not null,
              type                 text      default 'TYPE_UNSPECIFIED' not null,
              internal_template_id bigint    default 0                  not null,
              target_id            bigint    default 0                  not null,
              target_type          text      default 'TYPE_UNSPECIFIED' not null,
              staff_id             bigint    default 0                  not null,
              created_at           timestamp default now()              not null,
              updated_at           timestamp default now()              not null,
              deleted_at           timestamp default null
              );
              
              create unique index if not exists uniq_idx_campaign_template_push_record_template_target on campaign_template_push_record (internal_template_id, target_id, target_type, type) where deleted_at is null;
              create index if not exists idx_campaign_template_push_record_enterprise on campaign_template_push_record (enterprise_id);
              
              CREATE TRIGGER update_at
              BEFORE UPDATE
              ON campaign_template_push_record
              FOR EACH ROW
              EXECUTE PROCEDURE update_at();
