name: "dml for enterprise demo"
description: "drop unique index include where"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_enterprise"
            sql: |
              drop index if exists uniq_idx_campaign_template_push_record_template_target;  
              create unique index uniq_idx_campaign_template_push_record_template_target
                on campaign_template_push_record (internal_template_id, target_id, target_type, type);
