name: "dml for enterprise demo"
description: "mysql moe_message template 表 新增 enterprise id 列"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_message"
            sql: |
              ALTER TABLE moe_message_template
              ADD COLUMN enterprise_id bigint DEFAULT 0 NOT NULL COMMENT 'enterprise id';
              
              ALTER TABLE moe_marketing_email_template
              ADD COLUMN enterprise_id bigint DEFAULT 0 NOT NULL COMMENT 'enterprise id';
              
              ALTER TABLE moe_message_template
              ALTER COLUMN company_id SET DEFAULT 0;
              
              ALTER TABLE moe_message_template
              ALTER COLUMN business_id SET DEFAULT 0;
