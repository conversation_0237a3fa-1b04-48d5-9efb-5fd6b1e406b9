name: "dml for enterprise demo"
description: "授权 enterprise 库权限给 ark"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_enterprise"
            username: "${db.user.admin}"
            sql: |
              GRANT CONNECT ON DATABASE moego_file TO developer_ark;
              GRANT USAGE ON SCHEMA public TO developer_ark;
              GRANT SELECT, UPDATE, DELETE, INSERT ON ALL TABLES IN SCHEMA public TO developer_ark;
              ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, UPDATE, DELETE, INSERT ON TABLES TO developer_ark;
