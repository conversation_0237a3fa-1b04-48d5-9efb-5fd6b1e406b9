name: "dml for enterprise demo"
description: "pg moego_enterprise 新增 campaign"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_enterprise"
            sql: |
              create table if not exists campaign_template_push_record
              (
              id                   bigint generated always as identity primary key,
              enterprise_id        bigint    default 0                  not null,
              template_id          bigint    default 0                  not null,
              type                 text      default 'TYPE_UNSPECIFIED' not null,
              internal_template_id bigint    default 0                  not null,
              target_id            bigint    default 0                  not null,
              target_type          text      default 'TYPE_UNSPECIFIED' not null,
              staff_id             bigint    default 0                  not null,
              created_at           timestamp default now()              not null,
              updated_at           timestamp default now()              not null,
              deleted_at           timestamp default null
              );
              
              create unique index if not exists uniq_idx_campaign_template_push_record_template_target on campaign_template_push_record (internal_template_id, target_id, target_type, type) where deleted_at is null;
              create index if not exists idx_campaign_template_push_record_enterprise on campaign_template_push_record (enterprise_id);
              
              CREATE TRIGGER update_at
              BEFORE UPDATE
              ON campaign_template_push_record
              FOR EACH ROW
              EXECUTE PROCEDURE update_at();
