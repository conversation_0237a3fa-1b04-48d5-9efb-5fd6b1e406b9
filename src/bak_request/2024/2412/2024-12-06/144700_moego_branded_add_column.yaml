name: "add column for moego_branded_app.branded_theme_config"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_branded_app"
            sql: |
              ALTER TABLE "public"."branded_theme_config" 
              ADD COLUMN "custom_theme" jsonb NOT NULL DEFAULT '{}'::jsonb;
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_branded_app"
            sql: |
              ALTER TABLE "public"."branded_theme_config" 
              ADD COLUMN "custom_theme" jsonb NOT NULL DEFAULT '{}'::jsonb;