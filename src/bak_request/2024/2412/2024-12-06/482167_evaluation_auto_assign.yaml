name: "add column for moego_offering.evaluation"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_offering"
            sql: |
              alter table public.evaluation add is_all_staff boolean default true not null;
              alter table public.evaluation add allowed_staff_list bigint[] default ARRAY []::bigint[] not null;
              comment on column public.evaluation.allowed_staff_list is 'allowed staff id list, only when is_all_staff is true';
              alter table public.evaluation add allow_staff_auto_assign boolean default false not null;
              comment on column public.evaluation.allow_staff_auto_assign is 'whether to support automatic allocation of staff';
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_offering"
            sql: |
              alter table public.evaluation add is_all_staff boolean default true not null;
              alter table public.evaluation add allowed_staff_list bigint[] default ARRAY []::bigint[] not null;
              comment on column public.evaluation.allowed_staff_list is 'allowed staff id list, only when is_all_staff is true';
              alter table public.evaluation add allow_staff_auto_assign boolean default false not null;
              comment on column public.evaluation.allow_staff_auto_assign is 'whether to support automatic allocation of staff';