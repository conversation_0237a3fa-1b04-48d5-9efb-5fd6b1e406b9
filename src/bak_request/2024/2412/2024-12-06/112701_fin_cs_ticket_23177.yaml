name: "修改用户支付数据"
description: "修改用户支付数据"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              UPDATE public.order
              SET payment_status='PAID',paid_amount = 62.40,remain_amount = 0.00,extra_fee_amount = 2.40,status = 2,total_amount = 62.40,update_time = NOW()
              WHERE id = 131592753 AND business_id = '107043';
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              UPDATE moe_payment.payment
              set status=3,update_time = unix_timestamp(now())
              WHERE invoice_id = '131592753' and customer_id = '13986955' and status = '2' and id = '23702679';

              UPDATE moe_payment.payment
              set status=3,update_time = unix_timestamp(now())
              WHERE invoice_id = '131592753' and customer_id = '13986955' and status = '2' and id = '23702656';