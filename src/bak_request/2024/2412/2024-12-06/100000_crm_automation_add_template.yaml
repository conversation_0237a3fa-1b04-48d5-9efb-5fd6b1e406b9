name: "pg moego_automation 库清空表数据 && 添加模版数据"
description: "pg moego_automation 库清空表数据 && 添加模版数据"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_automation"
            sql: |
              DELETE FROM workflow;
              DELETE FROM step;
              DELETE FROM workflow_record;
              DELETE FROM step_record;
              DELETE FROM workflow_category_rel;
              
              INSERT INTO "public"."workflow" ("id", "company_id", "name", "description", "status", "type", "version",
                                               "trigger_id", "trigger_type", "trigger_schedule_frequency", "trigger_schedule_daytime", "trigger_schedule_week", "trigger_schedule_date", "last_trigger_time", "image", "created_by", "updated_by", "created_at", "updated_at", "deleted_at", "recommend_type", "recommend_image") VALUES (1, 0, 'Outreach  campaign', 'Create automated reminder sequences based on timing and client actions to boost bookings.', 'DRAFT', 'TEMPLATE', 0, '01939134-e668-71bd-a441-23494ac4a3a7', 'SCHEDULED', 'DAILY', '{"hours":10}', 'DAY_OF_WEEK_UNSPECIFIED', '{}', NULL, 'https://dcgll7qxxap6x.cloudfront.net/p/0/2024/11/093733f3-44d1-4a86-9425-adcb89345959.png', 140356, 140356, '2024-12-04 10:33:00.764127', '2024-12-04 10:33:00.764127', NULL, 'HOME_PAGE', 'https://dcgll7qxxap6x.cloudfront.net/p/0/2024/11/f0c0ffe4-e025-49d2-b7b1-62dba0a9e165.png');
              INSERT INTO "public"."workflow" ("id", "company_id", "name", "description", "status", "type", "version",
                                               "trigger_id", "trigger_type", "trigger_schedule_frequency", "trigger_schedule_daytime", "trigger_schedule_week", "trigger_schedule_date", "last_trigger_time", "image", "created_by", "updated_by", "created_at", "updated_at", "deleted_at", "recommend_type", "recommend_image") VALUES (2, 0, 'Rebook reminder', 'Send reminders to clients who haven’t rebooked an appointment after 4, 6, or 8 weeks.', 'DRAFT', 'TEMPLATE', 0, '0193913b-2267-729e-9c8b-a1d409ef35b7', 'EVENT', 'FREQUENCY_UNSPECIFIED', '{}', 'DAY_OF_WEEK_UNSPECIFIED', '{}', NULL, 'https://dcgll7qxxap6x.cloudfront.net/p/0/2024/11/4eaeb06d-6c27-4a63-8127-b6354147d171.png', 140356, 140356, '2024-12-04 10:38:21.184985', '2024-12-04 10:38:21.184985', NULL, 'HOME_PAGE', 'https://dcgll7qxxap6x.cloudfront.net/p/0/2024/11/3335e2ec-1441-4838-95ce-c7031e68c982.png');
              INSERT INTO "public"."workflow" ("id", "company_id", "name", "description", "status", "type", "version",
                                               "trigger_id", "trigger_type", "trigger_schedule_frequency", "trigger_schedule_daytime", "trigger_schedule_week", "trigger_schedule_date", "last_trigger_time", "image", "created_by", "updated_by", "created_at", "updated_at", "deleted_at", "recommend_type", "recommend_image") VALUES (3, 0, 'Welcome new clients', 'Send a welcome message to new clients after their first appointment request.', 'DRAFT', 'TEMPLATE', 0, '0193913f-e971-707f-8971-2141ac04bfab', 'EVENT', 'FREQUENCY_UNSPECIFIED', '{}', 'DAY_OF_WEEK_UNSPECIFIED', '{}', NULL, 'https://dcgll7qxxap6x.cloudfront.net/p/0/2024/11/f9287908-146a-4df6-aa17-11f9c96819fc.png', 140356, 140356, '2024-12-04 10:41:54.244298', '2024-12-04 10:41:54.244298', NULL, 'HOME_PAGE', 'https://dcgll7qxxap6x.cloudfront.net/p/0/2024/11/9a2ff514-1404-45d2-af94-4390f0b35870.png');
              INSERT INTO "public"."workflow" ("id", "company_id", "name", "description", "status", "type", "version",
                                               "trigger_id", "trigger_type", "trigger_schedule_frequency", "trigger_schedule_daytime", "trigger_schedule_week", "trigger_schedule_date", "last_trigger_time", "image", "created_by", "updated_by", "created_at", "updated_at", "deleted_at", "recommend_type", "recommend_image") VALUES (4, 0, 'Behavior test info collect', 'Send an Intake form to client before their evaluation appointment.', 'DRAFT', 'TEMPLATE', 0, '01939143-4fe4-74ee-bbad-44d4b6bc7083', 'EVENT', 'FREQUENCY_UNSPECIFIED', '{}', 'DAY_OF_WEEK_UNSPECIFIED', '{}', NULL, 'https://dcgll7qxxap6x.cloudfront.net/p/0/2024/11/4eaeb06d-6c27-4a63-8127-b6354147d171.png', 140356, 140356, '2024-12-04 10:47:06.524228', '2024-12-04 10:47:06.524228', NULL, 'RECOMMEND_TYPE_UNSPECIFIED', 'https://dcgll7qxxap6x.cloudfront.net/p/0/2024/11/9a2ff514-1404-45d2-af94-4390f0b35870.png');


              INSERT INTO "public"."step" ( "id_str", "parent_id", "version", "company_id", "workflow_id", "name", "description",
                                            "type", "data", "created_by", "updated_by", "created_at", "updated_at", "deleted_at") VALUES ('01939134-e668-71bd-a441-23494ac4a3a7', '', 0, 0, 1, '', '', 'TRIGGER', '{"trigger":{"type":"SCHEDULED", "data":{"scheduled":{"timeOfDay":{"hours":10}, "date":{}, "frequency":"DAILY"}}, "filters":[{"fieldKey":"upcoming_appointments", "operator":"EQUAL", "values":[{"int64":"0"}], "label":"0"}]}}', 140356, 140356, '2024-12-04 10:33:00.764127', '2024-12-04 10:33:00.764127', NULL);
              INSERT INTO "public"."step" ("id_str", "parent_id", "version", "company_id", "workflow_id", "name", "description",
                                           "type", "data", "created_by", "updated_by", "created_at", "updated_at", "deleted_at") VALUES ('01939138-2a4f-70e5-a78b-ca7ca72a0a9a', '01939134-e668-71bd-a441-23494ac4a3a7', 0, 0, 1, '', '', 'WAIT', '{"wait":{"type":"DURATION", "timeDuration":{"unit":"DAY", "value":"2"}}}', 140356, 140356, '2024-12-04 10:33:00.764127', '2024-12-04 10:33:00.764127', NULL);
              INSERT INTO "public"."step" ( "id_str", "parent_id", "version", "company_id", "workflow_id", "name", "description",
                                            "type", "data", "created_by", "updated_by", "created_at", "updated_at", "deleted_at") VALUES ( '01939138-7c5c-718d-baaf-797a1f18acff', '01939138-2a4f-70e5-a78b-ca7ca72a0a9a', 0, 0, 1, '', '', 'MESSAGE', '{"message":{"type":"SMS", "category":"CAMPAIGN", "data":{"smsData":{"content":"Book your next service now and enjoy a limited-time discount! Don’t miss out, act now!"}}}}', 140356, 140356, '2024-12-04 10:33:00.764127', '2024-12-04 10:33:00.764127', NULL);
              INSERT INTO "public"."step" ( "id_str", "parent_id", "version", "company_id", "workflow_id", "name", "description",
                                            "type", "data", "created_by", "updated_by", "created_at", "updated_at", "deleted_at") VALUES ( '01939138-d228-7673-ae84-8a1de40e9729', '01939138-7c5c-718d-baaf-797a1f18acff', 0, 0, 1, '', '', 'WAIT', '{"wait":{"type":"DURATION", "timeDuration":{"unit":"DAY", "value":"5"}}}', 140356, 140356, '2024-12-04 10:33:00.764127', '2024-12-04 10:33:00.764127', NULL);
              INSERT INTO "public"."step" ( "id_str", "parent_id", "version", "company_id", "workflow_id", "name", "description",
                                            "type", "data", "created_by", "updated_by", "created_at", "updated_at", "deleted_at") VALUES ( '01939138-f553-7530-bd03-03953d56fa61', '01939138-d228-7673-ae84-8a1de40e9729', 0, 0, 1, '', '', 'MESSAGE', '{"message":{"type":"SMS", "category":"CAMPAIGN", "data":{"smsData":{"content":"We noticed you haven’t booked yet! Take advantage of our special offer for an even better experience!"}}}}', 140356, 140356, '2024-12-04 10:33:00.764127', '2024-12-04 10:33:00.764127', NULL);
              INSERT INTO "public"."step" ( "id_str", "parent_id", "version", "company_id", "workflow_id", "name", "description",
                                            "type", "data", "created_by", "updated_by", "created_at", "updated_at", "deleted_at") VALUES ( '01939139-240b-738c-a007-0db5aa957783', '01939138-f553-7530-bd03-03953d56fa61', 0, 0, 1, '', '', 'WAIT', '{"wait":{"type":"DURATION", "timeDuration":{"unit":"DAY", "value":"7"}}}', 140356, 140356, '2024-12-04 10:33:00.764127', '2024-12-04 10:33:00.764127', NULL);
              INSERT INTO "public"."step" ( "id_str", "parent_id", "version", "company_id", "workflow_id", "name", "description",
                                            "type", "data", "created_by", "updated_by", "created_at", "updated_at", "deleted_at") VALUES ( '01939139-5688-7454-9955-f5c1a5a2e592', '01939139-240b-738c-a007-0db5aa957783', 0, 0, 1, '', '', 'MESSAGE', '{"message":{"type":"SMS", "category":"CAMPAIGN", "data":{"smsData":{"content":"Just book now and enjoy an exclusive discount! Join us today!"}}}}', 140356, 140356, '2024-12-04 10:33:00.764127', '2024-12-04 10:33:00.764127', NULL);



              INSERT INTO "public"."step" ( "id_str", "parent_id", "version", "company_id", "workflow_id", "name", "description",
                                            "type", "data", "created_by", "updated_by", "created_at", "updated_at", "deleted_at") VALUES ( '0193913b-2267-729e-9c8b-a1d409ef35b7', '', 0, 0, 2, '', '', 'TRIGGER', '{"trigger":{"type":"EVENT", "data":{"event":{"category":"APPOINTMENT", "trigger":"APPOINTMENT_FINISHED"}}, "filters":[{"fieldKey":"upcoming_appointments", "operator":"EQUAL", "values":[{"int64":"0"}], "label":"0"}], "name":"Appointment Finished"}}', 140356, 140356, '2024-12-04 10:38:21.184985', '2024-12-04 10:38:21.184985', NULL);
              INSERT INTO "public"."step" ("id_str", "parent_id", "version", "company_id", "workflow_id", "name", "description",
                                           "type", "data", "created_by", "updated_by", "created_at", "updated_at", "deleted_at") VALUES ( '0193913d-c785-713c-9983-cdcbd1f1f212', '0193913b-2267-729e-9c8b-a1d409ef35b7', 0, 0, 2, '', '', 'WAIT', '{"wait":{"type":"DURATION", "timeDuration":{"unit":"WEEK", "value":"4"}}}', 140356, 140356, '2024-12-04 10:38:21.184985', '2024-12-04 10:38:21.184985', NULL);
              INSERT INTO "public"."step" ("id_str", "parent_id", "version", "company_id", "workflow_id", "name", "description",
                                           "type", "data", "created_by", "updated_by", "created_at", "updated_at", "deleted_at") VALUES ( '0193913d-dbe1-7228-bc7e-04b40fbe98d8', '0193913d-c785-713c-9983-cdcbd1f1f212', 0, 0, 2, '', '', 'MESSAGE', '{"message":{"type":"SMS", "category":"CAMPAIGN", "data":{"smsData":{"content":"From{{store_name}}, {{customer_name}}, your next appointment is coming at {{date_and_time}}. "}}}}', 140356, 140356, '2024-12-04 10:38:21.184985', '2024-12-04 10:38:21.184985', NULL);


              INSERT INTO "public"."step" ( "id_str", "parent_id", "version", "company_id", "workflow_id", "name", "description",
                                            "type", "data", "created_by", "updated_by", "created_at", "updated_at", "deleted_at") VALUES ( '0193913f-e971-707f-8971-2141ac04bfab', '', 0, 0, 3, '', '', 'TRIGGER', '{"trigger":{"type":"EVENT", "data":{"event":{"category":"APPOINTMENT", "trigger":"APPOINTMENT_CREATED"}}, "filters":[{"fieldKey":"client_type", "operator":"EQUAL", "values":[{"string":"Prospects"}], "label":"Prospects"}], "name":"Create Appointment"}}', 140356, 140356, '2024-12-04 10:41:54.244298', '2024-12-04 10:41:54.244298', NULL);
              INSERT INTO "public"."step" ( "id_str", "parent_id", "version", "company_id", "workflow_id", "name", "description",
                                            "type", "data", "created_by", "updated_by", "created_at", "updated_at", "deleted_at") VALUES ( '01939140-8756-761f-a3cb-c919cba25ef2', '0193913f-e971-707f-8971-2141ac04bfab', 0, 0, 3, '', '', 'WAIT', '{"wait":{"type":"DURATION", "timeDuration":{"unit":"DAY", "value":"1"}}}', 140356, 140356, '2024-12-04 10:41:54.244298', '2024-12-04 10:41:54.244298', NULL);
              INSERT INTO "public"."step" ( "id_str", "parent_id", "version", "company_id", "workflow_id", "name", "description",
                                            "type", "data", "created_by", "updated_by", "created_at", "updated_at", "deleted_at") VALUES ( '01939140-be75-725a-bde7-6eb6e8f9445e', '01939140-8756-761f-a3cb-c919cba25ef2', 0, 0, 3, '', '', 'MESSAGE', '{"message":{"type":"SMS", "category":"CAMPAIGN", "data":{"smsData":{"content":"Hi {{customer_name}}!  Welcome to {{store_name}}! We''re thrilled to help care for your furry friend(s). Looking forward to being part of your pet''s journey!"}}}}', 140356, 140356, '2024-12-04 10:41:54.244298', '2024-12-04 10:41:54.244298', NULL);
              INSERT INTO "public"."step" ( "id_str", "parent_id", "version", "company_id", "workflow_id", "name", "description",
                                            "type", "data", "created_by", "updated_by", "created_at", "updated_at", "deleted_at") VALUES ( '01939141-42c6-734f-ac8f-f29444a71390', '01939140-be75-725a-bde7-6eb6e8f9445e', 0, 0, 3, '', '', 'WAIT', '{"wait":{"type":"DURATION", "timeDuration":{"unit":"DAY", "value":"3"}}}', 140356, 140356, '2024-12-04 10:41:54.244298', '2024-12-04 10:41:54.244298', NULL);
              INSERT INTO "public"."step" ( "id_str", "parent_id", "version", "company_id", "workflow_id", "name", "description",
                                            "type", "data", "created_by", "updated_by", "created_at", "updated_at", "deleted_at") VALUES ( '01939141-6b78-77ce-9d85-62770520de6d', '01939141-42c6-734f-ac8f-f29444a71390', 0, 0, 3, '', '', 'MESSAGE', '{"message":{"type":"SMS", "category":"CAMPAIGN", "data":{"smsData":{"content":"Hi {{customer_name}}!  Welcome to {{store_name}}! We''re thrilled to help care for your furry friend(s). Looking forward to our  appointment!"}}}}', 140356, 140356, '2024-12-04 10:41:54.244298', '2024-12-04 10:41:54.244298', NULL);


              INSERT INTO "public"."step" ( "id_str", "parent_id", "version", "company_id", "workflow_id", "name", "description",
                                            "type", "data", "created_by", "updated_by", "created_at", "updated_at", "deleted_at") VALUES ( '01939143-4fe4-74ee-bbad-44d4b6bc7083', '', 0, 0, 4, '', '', 'TRIGGER', '{"trigger":{"type":"EVENT", "data":{"event":{"category":"ONLINE_BOOKINGS", "trigger":"EVALUATION_BOOKING_SUBMITTED"}}, "name":"Evaluation booking submitted"}}', 140356, 140356, '2024-12-04 10:47:06.524228', '2024-12-04 10:47:06.524228', NULL);
              INSERT INTO "public"."step" ( "id_str", "parent_id", "version", "company_id", "workflow_id", "name", "description",
                                            "type", "data", "created_by", "updated_by", "created_at", "updated_at", "deleted_at") VALUES ( '01939144-25ed-7224-8d5e-5d43bd3a8737', '01939143-4fe4-74ee-bbad-44d4b6bc7083', 0, 0, 4, '', '', 'MESSAGE', '{"message":{"type":"SMS", "category":"CAMPAIGN", "data":{"smsData":{"content":"Hi {{customer_name}}!  welcome to {{store_name}}, please fill up this form {{intakeFormLink-please select your own template}} before you attend."}}}}', 140356, 140356, '2024-12-04 10:47:06.524228', '2024-12-04 10:47:06.524228', NULL);
              INSERT INTO "public"."step" ( "id_str", "parent_id", "version", "company_id", "workflow_id", "name", "description",
                                            "type", "data", "created_by", "updated_by", "created_at", "updated_at", "deleted_at") VALUES ( '01939146-27c9-765c-88e2-f3922fb9de72', '01939147-5c1c-7432-a080-f6aef87c27a0', 0, 0, 4, '', '', 'CONDITION', '{"condition":{"nextStepIdFalse":"01939146-2d75-76ab-b144-4d06025d85f7", "type":"ACTION_FILTER", "actionResultFilter":"INTAKE_FORM_SUBMIT_SUCCESS"}}', 140356, 140356, '2024-12-04 10:47:06.524228', '2024-12-04 10:47:06.524228', NULL);
              INSERT INTO "public"."step" ( "id_str", "parent_id", "version", "company_id", "workflow_id", "name", "description",
                                            "type", "data", "created_by", "updated_by", "created_at", "updated_at", "deleted_at") VALUES ( '01939146-2d75-76ab-b144-4d06025d85f7', '01939146-27c9-765c-88e2-f3922fb9de72', 0, 0, 4, '', '', 'MESSAGE', '{"message":{"type":"SMS", "category":"CAMPAIGN", "data":{"smsData":{"content":"Hi {{customer_name}}!  welcome to {{store_name}}, please fill up this form  {intakeFormLink-please select your own template} before you attend."}}}}', 140356, 140356, '2024-12-04 10:47:06.524228', '2024-12-04 10:47:06.524228', NULL);
              INSERT INTO "public"."step" ( "id_str", "parent_id", "version", "company_id", "workflow_id", "name", "description",
                                            "type", "data", "created_by", "updated_by", "created_at", "updated_at", "deleted_at") VALUES ( '01939147-5c1c-7432-a080-f6aef87c27a0', '01939144-25ed-7224-8d5e-5d43bd3a8737', 0, 0, 4, '', '', 'WAIT', '{"wait":{"type":"DURATION", "timeDuration":{"unit":"HOUR", "value":"4"}}}', 140356, 140356, '2024-12-04 10:47:06.524228', '2024-12-04 10:47:06.524228', NULL);


              INSERT INTO public.workflow_category_rel ( workflow_id, category_id, created_at, updated_at, deleted_at) VALUES
               (1, 2, '2024-11-15 10:21:49.546934', '2024-11-15 10:21:49.546934', null),
               (2, 1, '2024-11-15 10:21:49.546934', '2024-11-15 10:21:49.546934', null),
               (3, 1, '2024-11-15 10:21:49.546934', '2024-11-15 10:21:49.546934', null),
               (4, 2, '2024-11-15 10:21:49.546934', '2024-11-15 10:21:49.546934', null);