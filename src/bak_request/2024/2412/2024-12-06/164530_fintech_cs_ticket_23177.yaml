name: "订正用户分账数据"
description: "订正用户分账数据"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_split_payment"
            sql: |
              UPDATE public.split_detail
              SET status = 'SUCCESS', updated_time = NOW()
              WHERE id = 46309;
              
              UPDATE public.split_reverse_detail
              SET status = 'SUCCESS', updated_time = NOW()
              WHERE id = 289;