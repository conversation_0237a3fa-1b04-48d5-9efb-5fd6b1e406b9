name: "Fix Order version"
description: "Fix Order version for accounting payout"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              ---- 更新order verion=1
              UPDATE public.order SET order_version = 1 WHERE id = *********;
        - execute_sql:
            database: "moego_order"
            sql: |
              ---- 更新sync status
              UPDATE message_delivery
              SET status='PENDING', payload = jsonb_set(payload::jsonb, '{orderVersion}', '1'::jsonb)::text
              WHERE reference_id='*********';