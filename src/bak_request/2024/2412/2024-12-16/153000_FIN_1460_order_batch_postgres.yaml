name: "Fix Order version"
description: "Fix Order version for accounting payout"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              ---- 更新order verion=1
              UPDATE public.order SET order_version = 1 WHERE id in (3669182, *********, *********, *********, *********, 2824101, *********, *********, *********, *********, *********, *********, 2341813, 1513649, *********, 2799400, *********, *********, *********, 2949441, *********, *********, 2789360, *********, *********, *********, 2798840, *********, 849683, *********);
        - execute_sql:
            database: "moego_order"
            sql: |
              ---- 更新sync status
              UPDATE message_delivery
              SET status='PENDING', payload = jsonb_set(payload::jsonb, '{orderVersion}', '1'::jsonb)::text
              WHERE reference_id in ('3669182', '*********', '*********', '*********', '*********', '2824101', '*********', '*********', '*********', '*********', '*********', '*********', '2341813', '1513649', '*********', '2799400', '*********', '*********', '*********', '2949441', '*********', '*********', '2789360', '*********', '*********', '*********', '2798840', '*********', '849683', '*********');