name: "Add update trigger for moego_notification"
description: "Add update trigger for moego_notification"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_notification"
            sql: |
              CREATE OR REPLACE FUNCTION updated_at() RET<PERSON>NS trigger AS
              $$
              BEGIN
                new.updated_at = STATEMENT_TIMESTAMP()::timestamp WITHOUT TIME ZONE;
                RETURN new;
              END;
              $$
                LANGUAGE plpgsql;

              CREATE TRIGGER updated_at
                BEFORE UPDATE
                ON push_token
                FOR EACH ROW
              EXECUTE PROCEDURE updated_at();

              CREATE TRIGGER updated_at
                BEFORE UPDATE
                ON push_template
                FOR EACH ROW
              EXECUTE PROCEDURE updated_at();

              CREATE TRIGGER updated_at
                BEFORE UPDATE
                ON notification_template
                FOR EACH ROW
              EXECUTE PROCEDURE updated_at();
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_notification"
            sql: |
              CREATE OR REPLACE FUNCTION updated_at() RETURNS trigger AS
              $$
              BEGIN
                new.updated_at = STATEMENT_TIMESTAMP()::timestamp WITHOUT TIME ZONE;
                RETURN new;
              END;
              $$
                LANGUAGE plpgsql;

              CREATE TRIGGER updated_at
                BEFORE UPDATE
                ON push_token
                FOR EACH ROW
              EXECUTE PROCEDURE updated_at();

              CREATE TRIGGER updated_at
                BEFORE UPDATE
                ON push_template
                FOR EACH ROW
              EXECUTE PROCEDURE updated_at();

              CREATE TRIGGER updated_at
                BEFORE UPDATE
                ON notification_template
                FOR EACH ROW
              EXECUTE PROCEDURE updated_at();
