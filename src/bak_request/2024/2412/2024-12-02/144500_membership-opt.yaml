name: "Membership opt online"
description: "membership 列表页优化需求上线"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_subscription"
            username: "${db.user.admin}"
            sql: |
              GRANT SELECT, DELETE, INSERT ON TABLE awsdms_ddl_audit TO PUBLIC;
              GRANT USAGE, SELECT ON SEQUENCE awsdms_ddl_audit_c_key_seq TO PUBLIC;
        - execute_sql:
            database: "moego_subscription"
            sql: |
              alter table subscription
              add column sell_operator_staff_id bigint not null default 0;
        - execute_sql:
            database: "moego_membership"
            username: "${db.user.admin}"
            sql: |
              GRANT SELECT, DELETE, INSERT ON TABLE awsdms_ddl_audit TO PUBLIC;
              GRANT USAGE, SELECT ON SEQUENCE awsdms_ddl_audit_c_key_seq TO PUBLIC;
        - execute_sql:
            database: "moego_membership"
            sql: |
              alter table sell_link 
              add column operator_staff_id bigint not null default 0;