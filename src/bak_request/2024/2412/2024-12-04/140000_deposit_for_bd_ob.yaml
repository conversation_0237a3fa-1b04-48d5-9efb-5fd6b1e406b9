name: "Deposit for BD OB"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              alter table moe_grooming.moe_book_online_deposit add preauth_info json default (json_object()) not null comment 'preauth info, see com.moego.server.grooming.dto.BookOnlineDepositDTO.PreAuth';
              alter table moe_grooming.moe_book_online_deposit add booking_request_id bigint default 0 not null comment 'booking request id';
              create index moe_book_online_deposit_booking_request_id_index on moe_grooming.moe_book_online_deposit (booking_request_id);
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              alter table moe_grooming.moe_book_online_deposit add preauth_info json default (json_object()) not null comment 'preauth info, see com.moego.server.grooming.dto.BookOnlineDepositDTO.PreAuth';
              alter table moe_grooming.moe_book_online_deposit add booking_request_id bigint default 0 not null comment 'booking request id';
              create index moe_book_online_deposit_booking_request_id_index on moe_grooming.moe_book_online_deposit (booking_request_id);
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_online_booking"
            sql: |
              create table if not exists public.booking_request_appointment_mapping
              (
              id                 bigserial
              constraint booking_request_appointment_mapping_pk
              primary key,
              booking_request_id bigint    default 0                 not null,
              appointment_id     bigint    default 0                 not null,
              created_at         timestamp default CURRENT_TIMESTAMP not null,
              updated_at         timestamp default CURRENT_TIMESTAMP not null
              );
              
              comment on table public.booking_request_appointment_mapping is 'Appointment BookingRequest mapping';
              
              comment on column public.booking_request_appointment_mapping.id is 'id';
              
              comment on column public.booking_request_appointment_mapping.booking_request_id is 'booking request id';
              
              comment on column public.booking_request_appointment_mapping.appointment_id is 'appointment id';
              
              comment on column public.booking_request_appointment_mapping.created_at is 'create time';
              
              comment on column public.booking_request_appointment_mapping.updated_at is 'update time';
              
              create index if not exists booking_request_appointment_mapping_booking_request_id_idx
              on public.booking_request_appointment_mapping (booking_request_id);
              
              create unique index if not exists booking_request_appointment_mapping_appointment_id_uidx
              on public.booking_request_appointment_mapping (appointment_id);
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_online_booking"
            sql: |
              create table if not exists public.booking_request_appointment_mapping
              (
              id                 bigserial
              constraint booking_request_appointment_mapping_pk
              primary key,
              booking_request_id bigint    default 0                 not null,
              appointment_id     bigint    default 0                 not null,
              created_at         timestamp default CURRENT_TIMESTAMP not null,
              updated_at         timestamp default CURRENT_TIMESTAMP not null
              );
              
              comment on table public.booking_request_appointment_mapping is 'Appointment BookingRequest mapping';
              
              comment on column public.booking_request_appointment_mapping.id is 'id';
              
              comment on column public.booking_request_appointment_mapping.booking_request_id is 'booking request id';
              
              comment on column public.booking_request_appointment_mapping.appointment_id is 'appointment id';
              
              comment on column public.booking_request_appointment_mapping.created_at is 'create time';
              
              comment on column public.booking_request_appointment_mapping.updated_at is 'update time';
              
              create index if not exists booking_request_appointment_mapping_booking_request_id_idx
              on public.booking_request_appointment_mapping (booking_request_id);
              
              create unique index if not exists booking_request_appointment_mapping_appointment_id_uidx
              on public.booking_request_appointment_mapping (appointment_id);
