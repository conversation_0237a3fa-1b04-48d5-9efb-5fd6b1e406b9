name: "pg moego_automation 库清空表数据"
description: "pg moego_automation 库清空表数据"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_automation"
            sql: |
              DELETE FROM workflow;
              DELETE FROM step;
              DELETE FROM workflow_record;
              DELETE FROM step_record;