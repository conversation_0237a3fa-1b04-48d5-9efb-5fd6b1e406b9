name: "Add pricing rule"
description: "add pricing rule for holiday"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_offering"
            username: "${db.user.admin}"
            sql: |
              INSERT INTO pricing_rule (company_id, apply_service_type, apply_service_item_type, rule_name,
              is_all_service_applicable, selected_services, rule_configuration, is_active,
              updated_by, created_at, updated_at, rule_group_type)
              VALUES (119546, 1, 2, 'Holiday Pricing Rule2', true, '[]',
              '{
                      "pricingRuleItemDefs": [
                        {
                          "peakDateDef": {
                            "itemDefs": [
                              {
                                "startDate": "2024-12-19",
                                "endDate": "2025-01-06",
                                "priceItemDef": {
                                  "priceType": "FIXED_INCREASE",
                                  "priceValue": 10
                                }
                              }
                            ]
                          }
                        }
                      ]
                    }', true, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 2);
