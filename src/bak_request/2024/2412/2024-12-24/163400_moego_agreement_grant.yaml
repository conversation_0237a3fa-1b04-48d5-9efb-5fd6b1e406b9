name: "pg moego_agreement 库给haozhi加读权限"
description: "pg moego_agreement 库给haozhi加读权限"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_agreement"
            username: "${db.user.admin}"
            sql: |
              GRANT CONNECT ON DATABASE moego_agreement TO developer_haozhi;
              GRANT USAGE ON SCHEMA public TO developer_haozhi;
              GRANT SELECT ON ALL TABLES IN SCHEMA public TO developer_haozhi;
              ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO developer_haozhi;