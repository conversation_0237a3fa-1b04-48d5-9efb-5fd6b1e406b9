name: "ddl for membership perk"
description: "add new field"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_membership"
            sql: |
              ALTER TABLE "public"."membership"
              ADD COLUMN "enable_discount_benefits" boolean NOT NULL DEFAULT false,
              ADD COLUMN "enable_quantity_benefits" boolean NOT NULL DEFAULT false,
              ADD COLUMN "billing_cycle_period" text COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'CALENDAR_PERIOD_UNSPECIFIED'::text,
              ADD COLUMN "billing_cycle_value" int4 NOT NULL DEFAULT 0;
        - execute_sql:
            database: "moego_membership"
            sql: |
              CREATE TABLE IF NOT EXISTS membership_discount_benefits (
                id BIGSERIAL PRIMARY KEY,
                company_id BIGINT NOT NULL,
                business_id BIGINT NOT NULL,
                membership_id BIGINT NOT NULL,
                target_type text COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'UNSPECIFIED'::text,
                target_ids BIGINT[] DEFAULT '{}',
                is_all BOOLEAN NOT NULL DEFAULT FALSE,
                discount_type text COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'UNSPECIFIED'::text,
                discount_value NUMERIC(10, 2) NOT NULL CHECK (discount_value >= 0),
                feature_id BIGINT NOT NULL,
                created_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
                updated_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
                deleted_at TIMESTAMP(3) DEFAULT NULL
              );
              
              CREATE UNIQUE INDEX idx_mdb_membership_target_non_del
              ON "public"."membership_discount_benefits" ("membership_id", "target_type")
              WHERE "deleted_at" IS NULL;
              
              CREATE INDEX idx_mdb_membership_id
              ON "public"."membership_discount_benefits" ("membership_id");
              
              CREATE INDEX idx_mdb_company_business_membership
              ON "public"."membership_discount_benefits" ("company_id", "business_id", "membership_id");


        - execute_sql:
            database: "moego_membership"
            sql: |
              CREATE TABLE IF NOT EXISTS "public"."membership_quantity_benefits" (
                "id" BIGSERIAL PRIMARY KEY,
                "membership_id" int8 NOT NULL,
                "target_id" int8,
                "is_limited" bool DEFAULT false,
                "limited_value" int8,
                "created_at" timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
                "updated_at" timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
                "deleted_at" timestamp(3) DEFAULT NULL,
                "feature_id" int8,
                "company_id" int8 NOT NULL DEFAULT 0,
                "business_id" int8 NOT NULL DEFAULT 0,
                "period_type" text COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'PERIOD_UNSPECIFIED'::text,
                "period_unit" text COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'CALENDAR_PERIOD_UNSPECIFIED'::text,
                "period_value" int4 NOT NULL DEFAULT 0,
                "target_type" text COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'TARGET_TYPE_UNSPECIFIED'::text,
                "target_sub_type" text COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'TARGET_SUB_TYPE_UNSPECIFIED'::text
              );
              CREATE UNIQUE INDEX idx_mqb_membership_target_non_del
              ON "public"."membership_quantity_benefits" ("membership_id", "target_id", "target_type")
              WHERE "deleted_at" IS NULL;
              
              CREATE INDEX idx_mqb_membership_id
              ON "public"."membership_quantity_benefits" ("membership_id");
              
              CREATE INDEX idx_mqb_company_business_membership
              ON "public"."membership_quantity_benefits" ("company_id", "business_id", "membership_id");
            

        - execute_sql:
            database: "moego_membership"
            sql: |
              CREATE TABLE IF NOT EXISTS "public"."recommend_benefit_usage" (
              "id" BIGSERIAL PRIMARY KEY,
              "customer_id" int8 NOT NULL,
              "order_id" int8 NOT NULL,
              "created_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
              "updated_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
              "recommend_cache" jsonb NOT NULL DEFAULT '{}'::jsonb,
              CONSTRAINT "recommend_benefit_usage_customer_id_order_id_key" UNIQUE ("customer_id", "order_id")
              );
              
              CREATE INDEX idx_recommend_benefit_usage_order_id ON "public"."recommend_benefit_usage" ("order_id");



        - execute_sql:
            database: "moego_membership"
            sql: |
              CREATE TABLE IF NOT EXISTS "public"."redeem_history" (
              "id" BIGSERIAL PRIMARY KEY,
              "company_id" int8 NOT NULL,
              "customer_id" int8 NOT NULL,
              "membership_id" int8 NOT NULL,
              "appointment_id" int8 NOT NULL DEFAULT 0,
              "created_at" timestamp(6) NOT NULL DEFAULT now(),
              "updated_at" timestamp(6) NOT NULL DEFAULT now(),
              "deleted_at" timestamp(6) DEFAULT NULL,
              "order_id" int8 NOT NULL DEFAULT 0,
              "benefit_id" int8 NOT NULL DEFAULT 0,
              "amount" int4 NOT NULL DEFAULT 0,
              "target_id" int8 NOT NULL DEFAULT 0,
              "target_type" text COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'TARGET_TYPE_UNSPECIFIED'::text
              )
              ;
              
              CREATE INDEX idx_redeem_history_membership_customer
              ON "public"."redeem_history" ("membership_id", "customer_id");


        - execute_sql:
            database: "moego_subscription"
            sql: |
              ALTER TABLE "public"."feature"
              ADD COLUMN "deleted_at" timestamp(6) DEFAULT NULL;
              alter table subscription add column paused_at timestamp default null;
              alter table subscription add column auto_resume_at timestamp default null;

