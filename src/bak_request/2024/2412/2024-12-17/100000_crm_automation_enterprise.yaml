name: "pg moego_automation 库新增enterprise"
description: "pg moego_automation 库新增enterprise"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_automation"
            sql: |
              alter table public.workflow add column enterprise_id bigint not null default 0;
              alter table public.workflow add column apply_tenants_num int not null default 0;
              alter table public.step add column enterprise_id bigint not null default 0;
              create table if not exists public.workflow_enterprise_apply
              (
                  id          bigserial primary key,
                  workflow_id bigint                                     not null,
                  apply_id    bigint                                     not null,
                  apply_type  text      default 'TYPE_UNSPECIFIED'::text not null,
                  created_by  bigint                                     not null,
                  updated_by  bigint                                     not null,
                  created_at  timestamp default CURRENT_TIMESTAMP        not null,
                  updated_at  timestamp default CURRENT_TIMESTAMP        not null,
                  deleted_at  timestamp
              );
              CREATE INDEX idx_wea_workflow_id ON workflow_enterprise_apply (workflow_id);
              CREATE INDEX idx_wea_apply_id_apply_type ON workflow_enterprise_apply (apply_id, apply_type);