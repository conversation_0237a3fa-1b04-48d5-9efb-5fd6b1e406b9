name: "Add date_type column"
description: "add date_type column to moe_grooming_pet_detail"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_grooming"
            username: "${db.user.admin}"
            sql: |
              alter table moe_grooming_pet_detail add date_type int default 3 not null comment 'date type, 1-every day except checkout day, 2-specific date, 3-date point, 4-everyday';