name: "Add date_type column"
description: "add date_type column to boarding_add_on_detail"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_online_booking"
            username: "${db.user.admin}"
            sql: |
              alter table boarding_add_on_detail add date_type integer default 3 not null;
              comment on column boarding_add_on_detail.date_type is 'date type, 1-every day except checkout day, 2-specific date, 3-date point, 4-everyday';
