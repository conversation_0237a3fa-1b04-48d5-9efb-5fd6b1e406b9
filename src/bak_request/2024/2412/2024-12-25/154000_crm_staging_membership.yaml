name: "ddl for membership perk"
description: "add new field"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_membership"
            sql: |
              ALTER TABLE "public"."subscription"
              ADD COLUMN "auto_resume_setting" json NOT NULL DEFAULT '{}'::json;