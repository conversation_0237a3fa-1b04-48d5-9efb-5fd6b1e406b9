name: "ddl for membership perk"
description: "add new field"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_membership"
            sql: |
              ALTER TABLE membership DROP CONSTRAINT membership_billing_cycle_check;
              ALTER TABLE membership_discount_benefits RENAME COLUMN discount_type TO discount_unit;







