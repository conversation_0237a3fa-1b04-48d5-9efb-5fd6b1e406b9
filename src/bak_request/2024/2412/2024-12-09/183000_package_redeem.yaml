name: "Add package_service_id to moe_grooming_package_history"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              alter table moe_grooming.moe_grooming_package_history
                add package_service_id int default 0 not null comment 'package service id, moe_grooming_package_service.id' after package_id;
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              alter table moe_grooming.moe_grooming_package_history
                add package_service_id int default 0 not null comment 'package service id, moe_grooming_package_service.id' after package_id;
