name: "add evaluation filter by pet"
description: "add evaluation filter by pet"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_offering"
            sql: |
              alter table evaluation
              add column breed_filter boolean default false not null;
            
              comment on column evaluation.breed_filter is 'If true, the evaluation is filtered by pet type and breed';
              
              create table evaluation_pet_breed_filter
              (
              id            bigserial,
              evaluation_id bigint    default 0                 not null,
              pet_type      int       default 0                 not null,
              is_all_breed  bool      default false             not null,
              breed_names   text[]    default ARRAY []::TEXT[]  not null,
              created_at    timestamp default current_timestamp not null,
              updated_at    timestamp default current_timestamp not null,
              deleted_at    timestamp default null
              );
              
              comment on table evaluation_pet_breed_filter is 'evaluation pet type and breed filter table';
              
              comment on column evaluation_pet_breed_filter.evaluation_id is 'evaluation.id';
              comment on column evaluation_pet_breed_filter.pet_type is 'Pet type enum, 1-<PERSON>, 2-<PERSON>,..., 11-Other';
              comment on column evaluation_pet_breed_filter.is_all_breed is 'If true, all breeds are selected';
              comment on column evaluation_pet_breed_filter.breed_names is 'Breed names, if is_all_breed is true, this field is empty';
              
              create index idx_evaluation_id on evaluation_pet_breed_filter (evaluation_id);
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_offering"
            sql: |
              alter table evaluation
              add column breed_filter boolean default false not null;
              
              comment on column evaluation.breed_filter is 'If true, the evaluation is filtered by pet type and breed';
              
              create table evaluation_pet_breed_filter
              (
              id            bigserial,
              evaluation_id bigint    default 0                 not null,
              pet_type      int       default 0                 not null,
              is_all_breed  bool      default false             not null,
              breed_names   text[]    default ARRAY []::TEXT[]  not null,
              created_at    timestamp default current_timestamp not null,
              updated_at    timestamp default current_timestamp not null,
              deleted_at    timestamp default null
              );
              
              comment on table evaluation_pet_breed_filter is 'evaluation pet type and breed filter table';
              
              comment on column evaluation_pet_breed_filter.evaluation_id is 'evaluation.id';
              comment on column evaluation_pet_breed_filter.pet_type is 'Pet type enum, 1-Dog, 2-Cat,..., 11-Other';
              comment on column evaluation_pet_breed_filter.is_all_breed is 'If true, all breeds are selected';
              comment on column evaluation_pet_breed_filter.breed_names is 'Breed names, if is_all_breed is true, this field is empty';
              
              create index idx_evaluation_id on evaluation_pet_breed_filter (evaluation_id);