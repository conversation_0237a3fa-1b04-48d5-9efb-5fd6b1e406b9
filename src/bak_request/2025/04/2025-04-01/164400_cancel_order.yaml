name: "CS-27336 cancel order"
description: "CS-27336 cancel order"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              UPDATE "order" SET guid = 'b8a640f3f9d94105b039ef7e7366b77d_canceled', update_time = NOW() WHERE id = 140494173 AND business_id = 115749;
