name: "delete testing topic"
description: "delete testing topic"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.MessageQueueApi/DeleteTopics"
    body:
      identifier:
        platform: AWS_MSK
        identifier: "kafka-testing"
      topics:
        - "cdc_mysql_moe_customer_moe_pet_fixed_testing"
        - "cdc_postgresql_moego_account_account_history_testing"
        - "cdc_postgresql_moego_account_account_source_testing"
        - "cdc_postgresql_moego_account_tmp_users_testing"
        - "cdc_test_topic"