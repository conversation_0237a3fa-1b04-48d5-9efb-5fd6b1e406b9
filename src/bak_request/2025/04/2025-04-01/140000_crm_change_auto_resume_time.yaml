name: "change time"
description: "related slack: https://moegoworkspace.slack.com/archives/C032UFCSH1R/p1743425603339129"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_subscription"
            sql: |
              UPDATE "public"."subscription"
              SET "auto_resume_at" = '2025-04-15 13:50:33.000000'
              WHERE "id" = 1455;
              
              UPDATE "public"."subscription"
              SET "auto_resume_at" = '2025-04-15 13:49:34.000000'
              WHERE "id" = 1454;
