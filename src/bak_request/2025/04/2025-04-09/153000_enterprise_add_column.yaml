name: "service_change_history add column"
description: "service_change_history add column"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_enterprise"
            sql: |
              ALTER TABLE "public"."service_change_history" ADD COLUMN "impacted_tenant_ids" bigint[] NOT NULL DEFAULT '{}';
