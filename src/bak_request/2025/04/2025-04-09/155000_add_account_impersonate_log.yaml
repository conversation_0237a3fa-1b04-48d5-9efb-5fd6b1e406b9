name: "add account impersonate log table"
description: "add account impersonate log table"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_account"
            sql: |
              create table account_impersonate_log
              (
                id                 bigserial primary key,
                impersonator       varchar(100)              not null,
                target_account_id  bigint                    not null,
                session_id         bigint                    not null,
                source             varchar(50) default ''    not null,
                impersonate_at     timestamp with time zone  not null
              );
              CREATE INDEX account_impersonate_log_idx_impersonator ON account_impersonate_log (impersonator);
              CREATE INDEX account_impersonate_log_idx_impersonate_time ON account_impersonate_log (impersonate_at);
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_account"
            sql: |
              create table account_impersonate_log
              (
                id                 bigserial primary key,
                impersonator       var<PERSON><PERSON>(100)              not null,
                target_account_id  bigint                    not null,
                session_id         bigint                    not null,
                source             varchar(50) default ''    not null,
                impersonate_at     timestamp with time zone  not null
              );
              CREATE INDEX account_impersonate_log_idx_impersonator ON account_impersonate_log (impersonator);
              CREATE INDEX account_impersonate_log_idx_impersonate_time ON account_impersonate_log (impersonate_at);
