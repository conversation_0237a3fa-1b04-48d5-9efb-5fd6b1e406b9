name: "service_change_history add column"
description: "service_change_history add column"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_agreement"
            username: "${db.user.admin}"
            sql: |
              GRANT SELECT, DELETE, INSERT ON TABLE awsdms_ddl_audit TO PUBLIC;
              GRANT USAGE, SELECT ON SEQUENCE awsdms_ddl_audit_c_key_seq TO PUBLIC;
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_agreement"
            sql: |
              ALTER TABLE "public"."agreement_record" ADD COLUMN "inputs" TEXT NOT NULL DEFAULT '';
