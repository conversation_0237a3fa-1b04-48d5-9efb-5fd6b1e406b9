name: "assign company  122104 pet playgroup"
description: "related slack: https://moegoworkspace.slack.com/archives/C08FY9WN9PH/p1744095600227389"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_customer"
            sql: |
              UPDATE moe_customer_pet
                  JOIN (
                      SELECT
                          id,
                          ROW_NUMBER() OVER (ORDER BY id) AS row_num
                      FROM moe_customer_pet
                      WHERE company_id = 122104 AND playgroup_id = 0
                  ) AS ranked
                  ON moe_customer_pet.id = ranked.id
              SET moe_customer_pet.playgroup_id = CASE
                                                      WHEN ranked.row_num % 6 = 1 THEN 67
                                                      WHEN ranked.row_num % 6 = 2 THEN 68
                                                      WHEN ranked.row_num % 6 = 3 THEN 69
                                                      WHEN ranked.row_num % 6 = 4 THEN 70
                                                      WHEN ranked.row_num % 6 = 5 THEN 71
                                                      ELSE                             72
                  END
              WHERE moe_customer_pet.company_id = 122104 AND moe_customer_pet.playgroup_id = 0;
