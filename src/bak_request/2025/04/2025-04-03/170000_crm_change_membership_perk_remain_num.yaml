name: "change time"
description: "related slack: https://moegoworkspace.slack.com/archives/C032UFCSH1R/p1743425603339129"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_subscription"
            sql: |
              UPDATE "public"."entitlement"
              SET "feature_setting" = '{"count":{"usedAmount":"4", "totalAmount":"4"}}'
              WHERE "id" = 69678;
              
              UPDATE "public"."entitlement"
              SET "feature_setting" = '{"count":{"usedAmount":"2", "totalAmount":"2"}}'
              WHERE "id" IN (48046, 188, 56818);



