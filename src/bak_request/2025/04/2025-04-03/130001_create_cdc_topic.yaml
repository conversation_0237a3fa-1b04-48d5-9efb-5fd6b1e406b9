name: "testing cdc topic"
description: "testing cdc topic"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.MessageQueueApi/CreateTopics"
    body:
      identifier:
        platform: AWS_MSK
        identifier: "kafka-testing"
      topics:
        - name: "cdc.mysql.moe_customer.moe_business_customer.testing"
        - name: "cdc.mysql.moe_customer.moe_customer_pet.testing"
        - name: "cdc.postgresql.moego_metadata.meta_key.testing"
  - api: "/moego.devops.api.console.v1.MessageQueueApi/DeleteTopics"
    body:
      identifier:
        platform: AWS_MSK
        identifier: "kafka-testing"
      topics:
        - "cdc_mysql_moe_business_moe_company_testing"
        - "cdc_mysql_moe_customer_tmp_users_1_testing"
        - "cdc_mysql_moe_customer_tmp_users_2_testing"
        - "cdc_mysql_moe_customer_tmp_users_testing"
        - "cdc_postgresql_moego_account_tmp_users_1_testing"
        - "cdc_postgresql_moego_account_tmp_users_2_testing"
        - "cdc_postgresql_moego_account_tmp_users_testing"
        - "cdc_postgresql_moego_metadata_meta_key_testing"