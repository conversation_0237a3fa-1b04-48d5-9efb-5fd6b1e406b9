name: "create staging cdc topic"
description: "create staging cdc topic"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.MessageQueueApi/CreateTopics"
    body:
      identifier:
        platform: AWS_MSK
        identifier: "kafka-staging"
      topics:
        - name: "cdc.mysql.moe_customer.moe_business_customer.staging"
        - name: "cdc.mysql.moe_customer.moe_customer_pet.staging"