name: "mis_role_add_permission"
description: "mis_role_add_permission"
stopWhenError: true
requests:        
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_admin_v3"
            sql: |
              INSERT INTO "public"."role_permission" ( "role_id", "permission", "filters", "operator_id",created_at) VALUES ( 20, 'PAY_OPS_ENTERPRISE_CUSTOM_FEE_LIST', '', 'system',now());
              INSERT INTO "public"."role_permission" ( "role_id", "permission", "filters", "operator_id",created_at) VALUES ( 20, 'ENTERPRISE_SEARCH', '', 'system',now());