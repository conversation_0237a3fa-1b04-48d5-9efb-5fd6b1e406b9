name: "Create moego_fulfillment database"
description: "Create moego_fulfillment database"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-postgres-cluster
      actions:
        - create_database:
            database: "moego_fulfillment"
            create_user: true
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: production-postgres-cluster
      actions:
        - create_database:
            database: "moego_fulfillment"
            create_user: true
        - execute_sql:
            database: "moego_fulfillment"
            sql: |
              GRANT CONNECT ON DATABASE moego_fulfillment TO developer_jason;
              GRANT USAGE ON SCHEMA public TO developer_jason;
              GRANT SELECT ON ALL TABLES IN SCHEMA public TO developer_jason;
              ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO developer_jason;