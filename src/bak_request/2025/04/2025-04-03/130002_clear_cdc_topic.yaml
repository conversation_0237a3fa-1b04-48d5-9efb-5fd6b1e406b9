name: "clear staging cdc topic"
description: "clear staging cdc topic"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.MessageQueueApi/DeleteTopics"
    body:
      identifier:
        platform: AWS_MSK
        identifier: "kafka-staging"
      topics:
        - "cdc.mysql.moe_customer.moe_business_customer.staging"
  - api: "/moego.devops.api.console.v1.MessageQueueApi/CreateTopics"
    body:
      identifier:
        platform: AWS_MSK
        identifier: "kafka-staging"
      topics:
        - name: "cdc.mysql.moe_customer.moe_business_customer.staging"