name: "Add allow_waitlist_signups to lodging_capacity_setting"
description: "Add allow_waitlist_signups to lodging_capacity_setting"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_online_booking"
            sql: |
              alter table public.lodging_capacity_setting
              add allow_waitlist_signups boolean default false not null;
  
              comment on column public.lodging_capacity_setting.allow_waitlist_signups is '当达到 capacity 限制时是否允许提交 waitlist';

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_online_booking"
            sql: |
              alter table public.lodging_capacity_setting
              add allow_waitlist_signups boolean default false not null;
  
              comment on column public.lodging_capacity_setting.allow_waitlist_signups is '当达到 capacity 限制时是否允许提交 waitlist';
