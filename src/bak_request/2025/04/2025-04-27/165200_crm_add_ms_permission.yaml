name: "Grant permission to Better"
description: "grant permission to Better"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_membership"
            username: "${db.user.admin}"
            sql: |
              GRANT CONNECT ON DATABASE moego_membership TO developer_better;
              GRANT USAGE ON SCHEMA public TO developer_better;
              GRANT SELECT ON ALL TABLES IN SCHEMA public TO developer_better;
              GRANT INSERT ON ALL TABLES IN SCHEMA public TO developer_better;
              GRANT UPDATE ON ALL TABLES IN SCHEMA public TO developer_better;
              GRANT DELETE ON ALL TABLES IN SCHEMA public TO developer_better;
              ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO developer_better;
              ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT INSERT ON TABLES TO developer_better;
              ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT UPDATE ON TABLES TO developer_better;
              ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT DELETE ON TABLES TO developer_better;
