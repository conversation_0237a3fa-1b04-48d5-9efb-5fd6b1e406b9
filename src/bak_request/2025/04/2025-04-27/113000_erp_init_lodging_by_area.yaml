name: "init lodging unit type by area"
description: "init lodging unit type by area"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_offering"
            sql: |
              alter table public.lodging_type alter column type set default 2;
              update public.lodging_type set type = 2 where deleted_at IS NULL;

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_offering"
            sql: |
              alter table public.lodging_type alter column type set default 2;
              update public.lodging_type set type = 2 where deleted_at IS NULL;
