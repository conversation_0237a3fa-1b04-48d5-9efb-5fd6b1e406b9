name: "Grant marketing permission to <PERSON>"
description: "Grant marketing permission to <PERSON>"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_marketing"
            username: "${db.user.admin}"
            sql: |
              GRANT CONNECT ON DATABASE moego_marketing TO developer_bob;
              GRANT USAGE ON SCHEMA public TO developer_bob;
              GRANT SELECT ON ALL TABLES IN SCHEMA public TO developer_bob;
              ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO developer_bob;
