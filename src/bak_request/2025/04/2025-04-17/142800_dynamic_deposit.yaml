name: "Dynamic deposit"
description: "Dynamic deposit"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-grooming"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              update moe_grooming.moe_business_book_online
              set payment_option_map = '{"1": {"prePay": {"prepayType": 1, "depositType": 1, "depositAmount": 0, "depositPercentage": 50}, "paymentType": 2}, "2": {"prePay": {"prepayType": 1, "depositType": 1, "depositAmount": 0, "depositPercentage": 50}, "paymentType": 2}, "3": {"paymentType": 0}}'
              where company_id = 121663;
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              update moe_grooming.moe_business_book_online
              set payment_option_map = '{"1": {"prePay": {"prepayType": 1, "depositType": 1, "depositAmount": 0, "depositPercentage": 50}, "paymentType": 2}, "2": {"prePay": {"prepayType": 1, "depositType": 1, "depositAmount": 0, "depositPercentage": 50}, "paymentType": 2}, "3": {"paymentType": 0}}'
              where company_id = 121663;
