name: "Add permission to developer_zhang<PERSON>"
description: "Add permission to developer_zhang<PERSON>"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_appointment"
            username: "${db.user.admin}"
            sql: |
              GRANT CONNECT ON DATABASE moego_appointment TO developer_zhangdong;
              GRANT USAGE ON SCHEMA public TO developer_zhangdong;
              GRANT SELECT ON ALL TABLES IN SCHEMA public TO developer_zhangdong;
              GRANT INSERT ON ALL TABLES IN SCHEMA public TO developer_zhangdong;
              GRANT UPDATE ON ALL TABLES IN SCHEMA public TO developer_zhangdong;
              GRANT DELETE ON ALL TABLES IN SCHEMA public TO developer_zhangdong;
              ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO developer_zhangdong;
              ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT INSERT ON TABLES TO developer_zhangdong;
              ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT UPDATE ON TABLES TO developer_zhangdong;
              ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT DELETE ON TABLES TO developer_zhangdong;
              