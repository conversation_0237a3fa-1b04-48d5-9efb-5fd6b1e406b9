name: "subscription add new enterprise plan"
description: "add new enterprise plan for BD"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              INSERT INTO moe_payment.moe_price_plan_conf (stripe_plan_id, level, plan_type, price, title, description, update_time,
                                             create_time, plan_name, is_by_id, is_new_pricing, business_type,
                                             business_num)
              VALUES ('price_1RGIzAIZwcIFVLGrN7iXVywo', 1302, 0, 399.00, DEFAULT, DEFAULT, DEFAULT, DEFAULT, 'Enterprise Multi-Service', 2, 3, 1,
                      1);

              INSERT INTO moe_plan_feature_relation (
                  level, code, allow_type, enable, quota, is_deleted
              )
              SELECT
                  1302 AS level, code, allow_type, enable, quota, is_deleted
              FROM moe_plan_feature_relation
              WHERE level = 1202;
