name: "删除单个订单剩余 fee"
description: "Business 添加100%折扣之后没有移除剩余fee，手动给他删除，old invoice，SQL 为脚本生成"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              INSERT INTO public.order_line_extra_fee(business_id, order_id, order_item_id, apply_type, is_deleted, fee_type, amount, collect_type, description, apply_by, create_time, update_time) VALUES (102534, 142271557, 0, 'none', false, 'convenience fee', 6.32, 'subtract', '', 0, NOW(), NOW());

              UPDATE public."order" SET 
                  total_amount = total_amount - 6.32,
                  extra_fee_amount = extra_fee_amount - 6.32,
                  remain_amount = GREATEST(0, remain_amount - 6.32),
                  update_time = NOW()
              WHERE business_id = 102534 AND id = 142271557;