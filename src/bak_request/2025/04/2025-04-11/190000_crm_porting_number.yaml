name: "porting number"
description: "https://moego.atlassian.net/browse/CS-26161"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_message"
            sql: |
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 112122;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 114353;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 113238;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 114854;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 112491;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 112266;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 112545;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 116403;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 119501;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 118898;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 116611;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 112113;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 112763;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 115715;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 115351;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 115738;
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_engagement"
            sql: |
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 111760, 112122);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 113919, 114353);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 112847, 113238);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 114415, 114854);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 112120, 112491);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 111900, 112266);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 112174, 112545);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 115896, 116403);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 119184, 119501);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 118614, 118898);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 116098, 116611);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 111751, 112113);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 112391, 112763);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 115250, 115715);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 114903, 115351);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 115272, 115738);
          
