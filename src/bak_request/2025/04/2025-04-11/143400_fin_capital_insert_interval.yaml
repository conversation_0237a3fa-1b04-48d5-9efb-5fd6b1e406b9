name: "insert capital interval"
description: "https://moegoworkspace.slack.com/archives/C07EN0YBPT3/p1744324627538709?thread_ts=17********.958499&cid=C07EN0YBPT3"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              INSERT INTO moe_payment.loan_repayment_interval (company_id, entity_id, entity_type, offer_id, channel_offer_id,
                                                 channel_name, channel_account_id, sequence, begin_at, due_at,
                                                 minimum_amount, paid_amount, version, created_time, updated_time)
              VALUES (8097, 8097, 'business', 'PLO10080971m3Hw17dd0aca49210ca356c7', 'financingoffer_1PVHUjIZwcIFVLGrM8ikm3Hw',
                      'STRIPE', 'acct_1GjxakKgzLOe6zY8', 5, '2025-03-22 00:00:00', '2025-05-22 00:00:00', 588.45, 0.00, 1, DEFAULT,
                      DEFAULT);
