name: "add evaluation config for RedDog"
description: "add evaluation config for RedDog"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_offering"
            sql: |
              -- enable breed filter
              update evaluation set breed_filter = 1 where company_id = 121663;
              -- only available for dog
              insert into evaluation_pet_breed_filter(evaluation_id, pet_type, is_all_breed) values (1589, 1, true);
              -- Boston $49
              insert into evaluation_location_override(evaluation_id, business_id, price) values (1589, 122106, 49);
              -- Northshore $42
              insert into evaluation_location_override(evaluation_id, business_id, price) values (1589, 122365, 42);
              -- North Station $0
              insert into evaluation_location_override(evaluation_id, business_id, price) values (1589, 122366, 0);