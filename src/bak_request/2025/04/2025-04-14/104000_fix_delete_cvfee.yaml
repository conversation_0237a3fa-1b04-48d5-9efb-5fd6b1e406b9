name: "删除单个订单多余的cv fee"
description: "删除单个订单多余的cv fee，只针对一个ticket"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              INSERT INTO public.order_line_extra_fee(business_id, order_id, order_item_id, apply_type, is_deleted, fee_type, amount, collect_type, description, apply_by, create_time, update_time) VALUES (117655, 141171123, 0, 'none', false, 'convenience fee', 6.58, 'subtract', '', 0, NOW(), NOW());

              UPDATE public."order" SET 
                  total_amount = total_amount - 6.58,
                  extra_fee_amount = extra_fee_amount - 6.58,
                  remain_amount = GREATEST(0, remain_amount - 6.58),
                  update_time = NOW()
              WHERE business_id = 117655 AND id = 141171123;