name: "删除一个 老 order 中 多余的 cv fee"
description: "脚本生成sql ，cs ticket: cs-28563"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              INSERT INTO public.order_line_extra_fee(business_id, order_id, order_item_id, apply_type, is_deleted, fee_type, amount, collect_type, description, apply_by, create_time, update_time) VALUES (102534, 127572375, 0, 'none', false, 'convenience fee', 6.13, 'subtract', '', 0, NOW(), NOW());
              
              UPDATE public."order" SET 
                  total_amount = total_amount - 6.13,
                  extra_fee_amount = extra_fee_amount - 6.13,
                  remain_amount = GREATEST(0, remain_amount - 6.13),
                  update_time = NOW()
              WHERE business_id = 102534 AND id = 127572375;