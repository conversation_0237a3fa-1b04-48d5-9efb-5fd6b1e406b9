name: "create table ob_request_setting"
description: "create table ob_request_setting"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_membership"
            sql: |
              CREATE TABLE "public"."ob_request_setting" (
              "id" bigserial,
              "subscription_id" int8 NOT NULL DEFAULT 0,
              "company_id" int8 NOT NULL DEFAULT 0,
              "days_of_week" int4[] NOT NULL DEFAULT '{}',
              "created_at" timestamp(6) NOT NULL DEFAULT now(),
              "updated_at" timestamp(6) NOT NULL DEFAULT now(),
              "deleted_at" timestamp(6),
              "business_id" int8 NOT NULL DEFAULT 0,
              "last_refresh_cycle" int8[] NOT NULL DEFAULT '{}'::bigint[],
              CONSTRAINT "ob_request_setting_pkey" PRIMARY KEY ("id"),
              CONSTRAINT "ob_request_setting_subscription_id" UNIQUE ("subscription_id")
              );
              COMMENT ON COLUMN "public"."ob_request_setting"."last_refresh_cycle" IS '[start,end]';

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_membership"
            sql: |
              CREATE TABLE "public"."ob_request_setting" (
              "id" bigserial,
              "subscription_id" int8 NOT NULL DEFAULT 0,
              "company_id" int8 NOT NULL DEFAULT 0,
              "days_of_week" int4[] NOT NULL DEFAULT '{}',
              "created_at" timestamp(6) NOT NULL DEFAULT now(),
              "updated_at" timestamp(6) NOT NULL DEFAULT now(),
              "deleted_at" timestamp(6),
              "business_id" int8 NOT NULL DEFAULT 0,
              "last_refresh_cycle" int8[] NOT NULL DEFAULT '{}'::bigint[],
              CONSTRAINT "ob_request_setting_pkey" PRIMARY KEY ("id"),
              CONSTRAINT "ob_request_setting_subscription_id" UNIQUE ("subscription_id")
              );
              COMMENT ON COLUMN "public"."ob_request_setting"."last_refresh_cycle" IS '[start,end]';

