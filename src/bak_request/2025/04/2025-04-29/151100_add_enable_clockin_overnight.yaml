name: "clock in out setting 添加 enable_clock_in_out_overnight 字段"
description: "clock in out setting 添加 enable_clock_in_out_overnight 字段"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_business"
            sql: |
              ALTER TABLE moe_clock_in_out_setting 
              ADD COLUMN `enable_clock_in_out_overnight` TINYINT (1) NOT NULL DEFAULT '0' COMMENT '是否开启clockInOut支持跨天';

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_business"
            sql: |
              ALTER TABLE moe_clock_in_out_setting 
              ADD COLUMN `enable_clock_in_out_overnight` TINYINT (1) NOT NULL DEFAULT '0' COMMENT '是否开启clockInOut支持跨天';