name: "Add split lodging table"
description: "Add split lodging table"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: "AWS_RDS"
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_appointment"
            sql: |
              create table boarding_split_lodging
              (
                  id              bigserial
                      primary key,
                  appointment_id  bigint    default 0                 not null,
                  pet_detail_id   bigint    default 0                 not null,
                  pet_id          bigint    default 0                 not null,
                  start_date_time timestamp                           not null,
                  end_date_time   timestamp                           not null,
                  lodging_id      bigint                              not null,
                  price           numeric(20, 2)                      not null,
                  currency        char(3)   default 'USD'::bpchar     not null,
                  created_at      timestamp default CURRENT_TIMESTAMP not null,
                  is_applicable   boolean   default true              not null
              );

              COMMENT ON TABLE boarding_split_lodging IS 'boarding lodging split';
              COMMENT ON COLUMN boarding_split_lodging.id IS 'id';
              COMMENT ON COLUMN boarding_split_lodging.appointment_id IS 'appointment id';
              COMMENT ON COLUMN boarding_split_lodging.pet_detail_id IS 'pet detail id';
              COMMENT ON COLUMN boarding_split_lodging.pet_id IS 'pet id';
              COMMENT ON COLUMN boarding_split_lodging.start_date_time IS 'start datetime';
              COMMENT ON COLUMN boarding_split_lodging.end_date_time IS 'end datetime';
              COMMENT ON COLUMN boarding_split_lodging.lodging_id IS 'lodging id';
              COMMENT ON COLUMN boarding_split_lodging.price IS 'price';
              COMMENT ON COLUMN boarding_split_lodging.currency IS 'currency';
              COMMENT ON COLUMN boarding_split_lodging.created_at IS 'created at';
              COMMENT ON COLUMN boarding_split_lodging.is_applicable IS 'lodging is applicable';

              CREATE INDEX idx_boarding_split_lodging_appointment_id ON boarding_split_lodging (appointment_id);
              CREATE INDEX idx_boarding_split_lodging_lodging_id_time_range ON boarding_split_lodging (lodging_id, start_date_time, end_date_time);

              -- GiST区间索引 - 需要安装btree_gist扩展
              CREATE EXTENSION IF NOT EXISTS btree_gist;

              -- 为时间区间创建GiST索引
              CREATE INDEX idx_boarding_time_range ON boarding_split_lodging
                  USING GIST (tsrange(start_date_time, end_date_time, '[]'));

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: "AWS_RDS"
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_appointment"
            sql: |
              create table boarding_split_lodging
              (
                  id              bigserial
                      primary key,
                  appointment_id  bigint    default 0                 not null,
                  pet_detail_id   bigint    default 0                 not null,
                  pet_id          bigint    default 0                 not null,
                  start_date_time timestamp                           not null,
                  end_date_time   timestamp                           not null,
                  lodging_id      bigint                              not null,
                  price           numeric(20, 2)                      not null,
                  currency        char(3)   default 'USD'::bpchar     not null,
                  created_at      timestamp default CURRENT_TIMESTAMP not null,
                  is_applicable   boolean   default true              not null
              );

              COMMENT ON TABLE boarding_split_lodging IS 'boarding lodging split';
              COMMENT ON COLUMN boarding_split_lodging.id IS 'id';
              COMMENT ON COLUMN boarding_split_lodging.appointment_id IS 'appointment id';
              COMMENT ON COLUMN boarding_split_lodging.pet_detail_id IS 'pet detail id';
              COMMENT ON COLUMN boarding_split_lodging.pet_id IS 'pet id';
              COMMENT ON COLUMN boarding_split_lodging.start_date_time IS 'start datetime';
              COMMENT ON COLUMN boarding_split_lodging.end_date_time IS 'end datetime';
              COMMENT ON COLUMN boarding_split_lodging.lodging_id IS 'lodging id';
              COMMENT ON COLUMN boarding_split_lodging.price IS 'price';
              COMMENT ON COLUMN boarding_split_lodging.currency IS 'currency';
              COMMENT ON COLUMN boarding_split_lodging.created_at IS 'created at';
              COMMENT ON COLUMN boarding_split_lodging.is_applicable IS 'lodging is applicable';

              CREATE INDEX idx_boarding_split_lodging_appointment_id ON boarding_split_lodging (appointment_id);
              CREATE INDEX idx_boarding_split_lodging_lodging_id_time_range ON boarding_split_lodging (lodging_id, start_date_time, end_date_time);

              -- GiST区间索引 - 需要安装btree_gist扩展
              CREATE EXTENSION IF NOT EXISTS btree_gist;

              -- 为时间区间创建GiST索引
              CREATE INDEX idx_boarding_time_range ON boarding_split_lodging
                  USING GIST (tsrange(start_date_time, end_date_time, '[]'));