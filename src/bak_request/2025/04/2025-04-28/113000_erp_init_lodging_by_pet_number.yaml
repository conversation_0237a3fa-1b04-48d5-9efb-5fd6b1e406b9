name: "init lodging unit type by pet number"
description: "init lodging unit type by pet number"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_offering"
            sql: |
              UPDATE public.lodging_type
              SET type = CASE
                            WHEN max_pet_num <= 5 THEN 1
                            ELSE 2
              END
              where deleted_at IS NULL;

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_offering"
            sql: |
              UPDATE public.lodging_type
              SET type = CASE
                            WHEN max_pet_num <= 5 THEN 1
                            ELSE 2
              END
              where deleted_at IS NULL;
