name: "simply record hardware order"
description: "simply record hardware order"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              CREATE TABLE hardware_order (
                id BIGINT NOT NULL AUTO_INCREMENT comment 'Unique identifier' primary key,
                company_id BIGINT NOT NULL DEFAULT 0 COMMENT 'Company ID',
                po_number VARCHAR(255) NOT NULL DEFAULT '' COMMENT 'po number',
                shipping_method VARCHAR(255) NOT NULL DEFAULT '' COMMENT 'Shipping method',
                shipping VARCHAR(512) NOT NULL DEFAULT '' COMMENT 'Shipping info',
                m2_quantity INT NOT NULL DEFAULT 0 COMMENT 'M2 quantity',
                smart_reader_quantity INT NOT NULL DEFAULT 0 COMMENT 'Smart reader quantity',
                coupon_code VARCHAR(255) NOT NULL DEFAULT '' COMMENT 'Coupon code',
                bundle_sale_type VARCHAR(255) NOT NULL DEFAULT '' COMMENT 'Bundle sale type',
                bundle_sale_uuid VARCHAR(255) NOT NULL DEFAULT '' COMMENT 'bundle sale uuid',
                hardware_tax DECIMAL(10, 4) NOT NULL DEFAULT 0 COMMENT 'Hardware tax',
                shipping_fee DECIMAL(10, 4) NOT NULL DEFAULT 0 COMMENT 'Shipping fee',
                m2_amount DECIMAL(10, 4) NOT NULL DEFAULT 0 COMMENT 'm2 amount',
                smart_reader_amount DECIMAL(10, 4) NOT NULL DEFAULT 0 COMMENT 'Smart reader amount',
                discount DECIMAL(10, 4) NOT NULL DEFAULT 0 COMMENT 'Discount',
                total_amount DECIMAL(10, 4) NOT NULL DEFAULT 0 COMMENT 'Total amount',
                stripe_invoice_id VARCHAR(255) NOT NULL DEFAULT '' COMMENT 'Stripe invoice id',
                stripe_hardware_order_id VARCHAR(255) NOT NULL DEFAULT '' COMMENT 'Stripe hardware order id',
                created_time      datetime       default CURRENT_TIMESTAMP not null comment 'create time',
                updated_time      datetime       default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment 'update time'
              );

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              CREATE TABLE hardware_order (
                id BIGINT NOT NULL AUTO_INCREMENT comment 'Unique identifier' primary key,
                company_id BIGINT NOT NULL DEFAULT 0 COMMENT 'Company ID',
                po_number VARCHAR(255) NOT NULL DEFAULT '' COMMENT 'po number',
                shipping_method VARCHAR(255) NOT NULL DEFAULT '' COMMENT 'Shipping method',
                shipping VARCHAR(512) NOT NULL DEFAULT '' COMMENT 'Shipping info',
                m2_quantity INT NOT NULL DEFAULT 0 COMMENT 'M2 quantity',
                smart_reader_quantity INT NOT NULL DEFAULT 0 COMMENT 'Smart reader quantity',
                coupon_code VARCHAR(255) NOT NULL DEFAULT '' COMMENT 'Coupon code',
                bundle_sale_type VARCHAR(255) NOT NULL DEFAULT '' COMMENT 'Bundle sale type',
                bundle_sale_uuid VARCHAR(255) NOT NULL DEFAULT '' COMMENT 'bundle sale uuid',
                hardware_tax DECIMAL(10, 4) NOT NULL DEFAULT 0 COMMENT 'Hardware tax',
                shipping_fee DECIMAL(10, 4) NOT NULL DEFAULT 0 COMMENT 'Shipping fee',
                m2_amount DECIMAL(10, 4) NOT NULL DEFAULT 0 COMMENT 'm2 amount',
                smart_reader_amount DECIMAL(10, 4) NOT NULL DEFAULT 0 COMMENT 'Smart reader amount',
                discount DECIMAL(10, 4) NOT NULL DEFAULT 0 COMMENT 'Discount',
                total_amount DECIMAL(10, 4) NOT NULL DEFAULT 0 COMMENT 'Total amount',
                stripe_invoice_id VARCHAR(255) NOT NULL DEFAULT '' COMMENT 'Stripe invoice id',
                stripe_hardware_order_id VARCHAR(255) NOT NULL DEFAULT '' COMMENT 'Stripe hardware order id',
                created_time      datetime       default CURRENT_TIMESTAMP not null comment 'create time',
                updated_time      datetime       default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment 'update time'
              );