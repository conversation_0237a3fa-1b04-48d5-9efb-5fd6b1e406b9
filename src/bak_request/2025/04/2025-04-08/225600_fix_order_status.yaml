name: "修复 Appt 与订单同步异常的订单的状态"
description: "修复 Appt 与订单同步异常的订单的状态 Datadog Incident #48"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              UPDATE "order" SET remain_amount = 5.33, status = 1, payment_status = 'PARTIAL_PAID', update_time = NOW() WHERE business_id = 118363 AND id IN (140174490,140896396,137870767,140783738,140592694,140771097,136539415,140647524);
              UPDATE "order" SET remain_amount = 15.99, status = 1, payment_status = 'PARTIAL_PAID', update_time = NOW() WHERE business_id = 118291 AND id = 140043353;

              -- Overpaid 只需要调整订单状态
              UPDATE "order" SET status = 1, update_time = NOW() WHERE business_id = 118830 AND id IN (139908042,139979252,138441224,140381070);
