name: "refinance term loan"
description: "refinance term loan for <PERSON><PERSON> paw"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              UPDATE loan_offer
              SET offer_status = 'REPLACED',
              version = 3
              WHERE offer_id = 'PLO2TERM_LOAN0000002182cb52943ed4af99e55' and version = 2;

              INSERT INTO moe_payment.loan_offer_update_log (offer_id, channel_offer_id, before_offer_status, cur_offer_status,
                                                            before_channel_offer_status, cur_channel_offer_status, cur_version,
                                                            before_remain_amount, cur_remain_amount, event_type, event_id,
                                                            created_time, updated_time, before_processing_amount,
                                                            after_processing_amount, before_channel_offer_id)
              VALUES ('PLO2TERM_LOAN0000002182cb52943ed4af99e55', '7cad1137-9355-48b3-aa35-921a08ac4d0d', 'PAID_OUT', 'REPLACED',
                      'CURRENT', 'REFINANCED', 3, 
                      13975, 13975, 'refinanced by chi', NOW(), 
                      DEFAULT, DEFAULT, 0.00,
                      0.00, '7cad1137-9355-48b3-aa35-921a08ac4d0d');