name: "Increase metadata value length to 4096 characters"
description: "Increase metadata value length to 4096 characters"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_metadata"
            sql: |
              alter table public.meta_value
              alter column value type text;

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_metadata"
            sql: |
              alter table public.meta_value
              alter column value type text;
