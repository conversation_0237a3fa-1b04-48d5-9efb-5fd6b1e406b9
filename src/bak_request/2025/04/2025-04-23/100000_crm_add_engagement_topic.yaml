requests:
  - api: "/moego.devops.api.console.v1.MessageQueueApi/CreateTopics"
    body:
      identifier:
        platform: AWS_MSK
        identifier: "kafka-staging"
      topics:
        - name: "moego.crm.engagement.staging"
  - api: "/moego.devops.api.console.v1.MessageQueueApi/CreateTopics"
    body:
      identifier:
        platform: AWS_MSK
        identifier: "moego-kafka"
      topics:
        - name: "moego.crm.engagement.production"