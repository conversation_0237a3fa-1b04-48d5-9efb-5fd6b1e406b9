name: "消息表补录消息触发补偿"
description: "消息表补录消息触发补偿"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              INSERT INTO moe_payment.loan_message_delivery
                  (message_type, reference_id, payload, status, retry_count, last_attempt_time)
              VALUES
                  (
                   'CAPITAL_LOAN_TRANSACTION_UPDATED',
                   'PLT11137545334000067b4d8e1905b',
                   '{"tenant":{"companyId":"113342","businessId":"113754"},"capitalLoanTransactionUpdatedEvent":{"transaction":{"transactionId":"PLT11137545334000067b4d8e1905b","channelTransactionId":"71e2bed6-eea0-4dde-adbe-8fc9ad4875fd","transactionType":"PAYMENT","transactionReason":"AUTOMATIC_WITHHOLDING","channelName":"KANMON","linkedPaymentId":"26435334","effectiveTime":"2025-02-18T19:00:51.000Z","advancePaymentAmount":29.6,"totalPaymentAmount":29.6,"status":"SUCCESS","description":"Paydown of your loan","interestPaymentAmount":{},"settledTime":"2025-02-18T19:00:51.000000000Z"},"offerType":"MCA"}}',
                   'PENDING', 0, '0000-00-00 00:00:00'
                  );
              INSERT INTO moe_payment.loan_message_delivery
                  (message_type, reference_id, payload, status, retry_count, last_attempt_time)
              VALUES
                  (
                   'CAPITAL_LOAN_TRANSACTION_UPDATED',
                   'PLT11137546158000067b50c567cab',
                   '{"tenant":{"companyId":"113342","businessId":"113754"},"capitalLoanTransactionUpdatedEvent":{"transaction":{"transactionId":"PLT11137546158000067b50c567cab","channelTransactionId":"0d8d0bd0-4fb2-4cb1-bffd-780bc34fe742","transactionType":"PAYMENT","transactionReason":"AUTOMATIC_WITHHOLDING","channelName":"KANMON","linkedPaymentId":"26446158","effectiveTime":"2025-02-18T22:40:24.000Z","advancePaymentAmount":6.68,"totalPaymentAmount":6.68,"status":"SUCCESS","description":"Paydown of your loan","interestPaymentAmount":{},"settledTime":"2025-02-18T22:40:24.000000000Z"},"offerType":"MCA"}}',
                   'PENDING', 0, '0000-00-00 00:00:00'
                  );
              INSERT INTO moe_payment.loan_message_delivery
                  (message_type, reference_id, payload, status, retry_count, last_attempt_time)
              VALUES
                  (
                   'CAPITAL_LOAN_TRANSACTION_UPDATED',
                   'PLT11137542593000067b4cbadddde',
                   '{"tenant":{"companyId":"113342","businessId":"113754"},"capitalLoanTransactionUpdatedEvent":{"transaction":{"transactionId":"PLT11137542593000067b4cbadddde","channelTransactionId":"953ba18b-a7c8-4d4f-a7d9-96848f560820","transactionType":"PAYMENT","transactionReason":"AUTOMATIC_WITHHOLDING","channelName":"KANMON","linkedPaymentId":"26432593","effectiveTime":"2025-02-18T18:04:32.000Z","advancePaymentAmount":46.34,"totalPaymentAmount":46.34,"status":"SUCCESS","description":"Paydown of your loan","interestPaymentAmount":{},"settledTime":"2025-02-18T18:04:31.000000000Z"},"offerType":"MCA"}}',
                   'PENDING', 0, '0000-00-00 00:00:00'
                  );
