name: "更新 测试环境 moego test account"
description: "更新 测试环境 moego test account"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: testing-postgres-aurora
      actions:
        - execute_sql:
            database: "moego_tools"
            sql: |
              UPDATE moego_tools.public.test_account
              SET
                  owner = 'StripePayTestSuite'
              WHERE 
                  email IN (
                      '<EMAIL>',
                      '<EMAIL>',
                      '<EMAIL>',
                      '<EMAIL>',
                      '<EMAIL>'
                  );
              
              
              UPDATE moego_tools.public.test_account
              SET 
                  owner = 'StripePayTestSuite'
              WHERE email IN (
                  '<EMAIL>',
                  '<EMAIL>',
                  '<EMAIL>',
                  '<EMAIL>',
                  '<EMAIL>'
              );
                            
                            
              UPDATE moego_tools.public.test_account
              SET 
                owner = 'SquarePayTestSuite'
              WHERE email IN (
                  '<EMAIL>',
                  '<EMAIL>',
                  '<EMAIL>',
                  '<EMAIL>',
                  '<EMAIL>'
              );


              UPDATE moego_tools.public.test_account
              SET 
                  owner = 'NewInvoiceTestSuite'
              WHERE email IN (
                  '<EMAIL>',
                  '<EMAIL>',
                  '<EMAIL>',
                  '<EMAIL>',
                  '<EMAIL>'
              );