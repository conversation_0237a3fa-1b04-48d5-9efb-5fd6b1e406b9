name: "add group_class field to moe_grooming_service table"
description: "add group_class field to moe_grooming_service table"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-grooming"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              ALTER TABLE moe_grooming.moe_grooming_service ADD COLUMN num_sessions INT NOT NULL DEFAULT 0 COMMENT 'Number of sessions, only for training';
              ALTER TABLE moe_grooming.moe_grooming_service ADD COLUMN duration_session_min INT NOT NULL DEFAULT 0 COMMENT 'Duration of each session in minutes, only for training';
              ALTER TABLE moe_grooming.moe_grooming_service ADD COLUMN capacity INT NOT NULL DEFAULT 0 COMMENT 'Capacity of group class, zero means unlimited, only for training';
              ALTER TABLE moe_grooming.moe_grooming_service ADD COLUMN is_require_prerequisite_class tinyint(1) NOT NULL DEFAULT '0' COMMENT 'whether it require a prerequisite class';
              ALTER TABLE moe_grooming.moe_grooming_service ADD COLUMN prerequisite_class_ids JSON COMMENT 'Prerequisite class ids of training group class, only valid when is_require_prerequisite_class is ture';