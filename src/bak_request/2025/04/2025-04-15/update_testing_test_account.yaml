name: "更新 测试环境 moego test account"
description: "更新 测试环境 moego test account"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: testing-postgres-aurora
      actions:
        - execute_sql:
            database: "moego_tools"
            sql: |
              UPDATE moego_tools.public.test_account
              SET 
                  attributes = '{"region_code": "US"}',
                  owner = 'wallace'
              WHERE 
                  email IN (
                      '<EMAIL>',
                      '<EMAIL>',
                      '<EMAIL>',
                      '<EMAIL>',
                      '<EMAIL>'
                  );
              
              
              UPDATE moego_tools.public.test_account
              SET 
                  attributes = '{
                      "region_code": "US",
                      "enable_boarding_daycare": true,
                      "enable_stripe": true
                  }',
                  owner = 'wallace'
              WHERE email IN (
                  '<EMAIL>',
                  '<EMAIL>',
                  '<EMAIL>',
                  '<EMAIL>',
                  '<EMAIL>'
              );
                            
                            
              UPDATE moego_tools.public.test_account
              SET 
                  attributes = '{
                      "region_code": "US",
                      "enable_boarding_daycare": true,
                      "enable_square": true
                  }',
                  owner = 'wallace'
              WHERE email IN (
                  '<EMAIL>',
                  '<EMAIL>',
                  '<EMAIL>',
                  '<EMAIL>',
                  '<EMAIL>'
              );


              UPDATE moego_tools.public.test_account
              SET 
                  attributes = '{
                      "region_code": "US",
                      "enable_boarding_daycare": true,
                      "enable_stripe": true,
                      "enable_new_invoice": true
                  }',
                  owner = 'wallace'
              WHERE email IN (
                  '<EMAIL>',
                  '<EMAIL>',
                  '<EMAIL>',
                  '<EMAIL>',
                  '<EMAIL>'
              );