name: "Grant permission to Ark"
description: "grant permission to <PERSON>"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_retail"
            username: "${db.user.admin}"
            sql: |
              GRANT USAGE ON *.* TO 'developer_ark'@'%';
              GRANT SELECT, INSERT, UPDATE, DELETE ON moe_retail.* TO 'developer_ark'@'%';
        - execute_sql:
            database: "moe_retail"
            username: "${db.user.admin}"
            sql: |
              GRANT USAGE ON *.* TO 'developer_haozi'@'%';
              GRANT SELECT, INSERT, UPDATE, DELETE ON moe_retail.* TO 'developer_haozi'@'%';
