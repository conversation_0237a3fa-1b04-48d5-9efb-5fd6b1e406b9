name: "Add payment_option_map to moe_business_book_online"
description: "Add payment_option_map to moe_business_book_online"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-mysql-grooming
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              alter table moe_grooming.moe_business_book_online
              add payment_option_map json default (json_object()) not null comment 'service item type 定制的 prepay amount 配置，数据结构参考：com.moego.server.grooming.dto.BookOnlineDTO#paymentOptionMap。这个字段是个商家定制字段，不要轻易使用！';
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              alter table moe_grooming.moe_business_book_online
              add payment_option_map json default (json_object()) not null comment 'service item type 定制的 prepay amount 配置，数据结构参考：com.moego.server.grooming.dto.BookOnlineDTO#paymentOptionMap。这个字段是个商家定制字段，不要轻易使用！';
