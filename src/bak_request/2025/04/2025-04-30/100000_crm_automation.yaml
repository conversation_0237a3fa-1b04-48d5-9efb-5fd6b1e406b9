name: "pg moego_automation 增加template ID字段"
description: "pg moego_automation 增加template ID字段"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_automation"
            sql: |
              ALTER TABLE workflow ADD COLUMN template_id BIGINT DEFAULT 0 NOT NULL;