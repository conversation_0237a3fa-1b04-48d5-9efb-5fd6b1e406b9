name: "删除 多余的 cv fee，新增cv fee 平账记录 修改 order 元数据"
description: "删除 多余的 cv fee，新增cv fee 平账记录 修改 order 元数据"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              INSERT INTO public.order_line_extra_fee(business_id, order_id, order_item_id, apply_type, is_deleted, fee_type, amount, collect_type, description, apply_by, create_time, update_time) 
              VALUES (109286, 137853891, 0, 'none', false, 'convenience fee', 4.41, 'subtract', '', 0, NOW(), NOW());
              
              UPDATE public."order" SET 
                  total_amount = total_amount - 4.41,
                  extra_fee_amount = extra_fee_amount - 4.41,
                  remain_amount = GREATEST(0, remain_amount - 4.41),
                  update_time = NOW()
              WHERE business_id = 109286 AND id = 137853891;