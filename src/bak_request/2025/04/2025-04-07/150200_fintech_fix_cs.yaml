name: "修复 Business 单个order 上多出来的 fee"
description: "修复 Business 单个order 上多出来的 fee"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              UPDATE public."order"
              SET 
                  payment_status = 'PAID',
                  extra_fee_amount = extra_fee_amount - 6.65,
                  total_amount = total_amount - 6.65,
                  remain_amount = GREATEST(0, remain_amount - 6.65),
                  update_time = NOW()
              WHERE 
                  id = 131047436 
                  AND business_id = 119577;
              
              UPDATE public.order_line_extra_fee
              SET 
                  is_deleted = true,
                  update_time = NOW()
              WHERE 
                  id = 118398612 
                  AND business_id = 119577 
                  AND order_id = 131047436;