name: "add source_id for booking_request"
description: "Increase metadata value length to 4096 characters"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_online_booking"
            sql: |
              ALTER TABLE "public"."booking_request"
              ADD COLUMN "source_id" int8 NOT NULL DEFAULT 0;

              COMMENT ON COLUMN "public"."booking_request"."source_id" IS 'see BookingRequestModel.Source';

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_online_booking"
            sql: |
              ALTER TABLE "public"."booking_request"
              ADD COLUMN "source_id" int8 NOT NULL DEFAULT 0;

              COMMENT ON COLUMN "public"."booking_request"."source_id" IS 'see BookingRequestModel.Source';
