name: "Add group class to offering and fulfillment"
description: "Add group class to offering and fulfillment"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_offering"
            sql: |
              -- auto-generated definition
              create table group_class_instance
              (
              id            bigserial
              primary key,
              created_at    timestamp with time zone default CURRENT_TIMESTAMP           not null,
              updated_at    timestamp with time zone default CURRENT_TIMESTAMP           not null,
              deleted_at    timestamp with time zone,
              company_id    bigint                   default 0                           not null,
              business_id   bigint                   default 0                           not null,
              service_id    bigint                   default 0                           not null,
              name          varchar(100)             default ''::character varying       not null,
              staff_id      bigint                   default 0                           not null,
              price         numeric(10, 2)           default 0                           not null,
              tax_id        bigint                   default 0                           not null,
              currency_code text                     default 'USD'::text                 not null,
              start_time    timestamp with time zone                                     not null,
              time_zone     text                     default 'America/Los_Angeles'::text not null,
              capacity      bigint                   default 0                           not null,
              occurrence    text                     default ''::text                    not null,
              status        text                     default 'STATUS_UNSPECIFIED'::text  not null
              );
              
              comment on column group_class_instance.company_id is 'Company ID';
              
              comment on column group_class_instance.business_id is 'Business ID';
              
              comment on column group_class_instance.service_id is 'alias group_class_id';
              
              comment on column group_class_instance.staff_id is 'Trainer ID';
              
              comment on column group_class_instance.occurrence is 'json::models/offering/v1/group_class_models/group_class_instance.occurrence';
              
              comment on column group_class_instance.status is 'enum:models/offering/v1/group_class_models/group_class_instance.status';
              
              create index idx_group_class_instance_business_id
              on group_class_instance (business_id);
              
              create index idx_group_class_instance_company_id
              on group_class_instance (company_id);
              
              create index idx_group_class_instance_status
              on group_class_instance (status);
              
              create table group_class_session
              (
              id           bigserial
              primary key,
              created_at   timestamp with time zone default CURRENT_TIMESTAMP not null,
              updated_at   timestamp with time zone default CURRENT_TIMESTAMP not null,
              deleted_at   timestamp with time zone,
              company_id   bigint                   default 0                 not null,
              business_id  bigint                   default 0                 not null,
              instance_id  bigint                   default 0                 not null,
              start_time   timestamp with time zone                           not null,
              duration_min bigint                   default 0                 not null,
              is_modified  boolean                  default false             not null
              );
              
              comment on column group_class_session.company_id is 'Company ID';
              
              comment on column group_class_session.start_time is 'Session start time';
              
              comment on column group_class_session.duration_min is 'Duration of sessions in minutes';
              
              comment on column group_class_session.is_modified is 'Manually modified flag';
              
              create index idx_group_class_session_instance_id
              on group_class_session (instance_id);
              
              create table event
              (
              id           bigserial
              primary key,
              created_at   timestamp with time zone default CURRENT_TIMESTAMP        not null,
              updated_at   timestamp with time zone default CURRENT_TIMESTAMP        not null,
              message_type text                     default 'TYPE_UNSPECIFIED'::text not null,
              reference_id text                     default ''::text                 not null,
              payload      text                     default '{}'::text               not null,
              status       text                     default ''::text                 not null,
              retry_times  bigint                   default 0                        not null
              );
              
              create index idx_event_reference_id
              on event (reference_id);
              
              create index idx_event_status
              on event (status);

        - execute_sql:
            database: "moego_fulfillment"
            sql: |
              
              create table fulfillment
              (
              id                   bigserial primary key,
              company_id           bigint      default 0                            not null,
              business_id          bigint      default 0                            not null,
              customer_id          bigint      default 0                            not null,
              booking_request_id   bigint      default 0                            not null,
              start_datetime       timestamp,
              end_datetime         timestamp,
              status               integer     default 1                            not null,
              color_code           varchar(20) default '#000000'::character varying not null,
              service_type_include integer     default 1                            not null,
              source               integer     default 22018                        not null,
              repeat_rule_id       integer     default 0                            not null,
              created_at           timestamp   default now()                        not null,
              updated_at           timestamp   default now()                        not null,
              deleted_at           timestamp,
              order_id             bigint      default 0                            not null
              );
              
              comment on table fulfillment is 'Fulfillment summary information, generated after a scheduled request has been scheduled';
              
              comment on column fulfillment.booking_request_id is 'booking_request.id, A booking request may be scheduled for multiple fulfillment';
              
              comment on column fulfillment.start_datetime is 'Minimum start date time for all service details in the current fulfillment';
              
              comment on column fulfillment.end_datetime is 'Maximum start date time for all service details in the current fulfillment';
              
              comment on column fulfillment.status is '1-unconfirmed, 2-confirmed, 3-finished, 4-canceled, 5-ready, 6-checked-in';
              
              comment on column fulfillment.service_type_include is 'The value of the bit composition of the various types of services, 1-boarding, 2-daycare, 3-grooming, 4-evaluation, 5-dog_walking';
              
              comment on column fulfillment.source is '22018-web, 22168-ob, 17216-android, 17802-ios, 23426-dm, 19826-gc, 23333-openapi';
              
              comment on column fulfillment.repeat_rule_id is 'fulfillment_repeat_rule.id';
              
              create index fulfillment_idx_tenant_customer
              on fulfillment (company_id, business_id, customer_id);
              
              create index fulfillment_idx_created_at
              on fulfillment (created_at);
  
              SELECT setval('fulfillment_id_seq', 1000000000, false);
              
              create table group_class_detail
              (
              id                      bigserial
              primary key,
              fulfillment_id          bigint    default 0     not null,
              pet_id                  bigint    default 0     not null,
              group_class_id          bigint    default 0     not null,
              group_class_instance_id bigint    default 0     not null,
              status                  integer   default 0     not null,
              created_at              timestamp default now() not null,
              updated_at              timestamp default now() not null,
              deleted_at              timestamp
              );
              
              comment on table group_class_detail is 'Details of pet training group class service';
              comment on column group_class_detail.fulfillment_id is 'fulfillment.id';
              comment on column group_class_detail.pet_id is 'moe_customer_pet.id';
              comment on column group_class_detail.status is '1-Not started, 2-In Progress, 3-Completed';
              comment on column group_class_detail.group_class_id is 'group class id, service.id';
              comment on column group_class_detail.group_class_instance_id is 'group_class_instance.id';
              create index group_class_detail_idx_fulfillment on group_class_detail (fulfillment_id);
              create unique index group_class_detail_uni_pet_instance on group_class_detail (group_class_instance_id, pet_id);
              
              
              create table staff_time_slot
              (
              id                 bigserial
              primary key,
              company_id         bigint    default 0     not null,
              business_id        bigint    default 0     not null,
              fulfillment_id     bigint    default 0     not null,
              care_type          integer   default 0     not null,
              detail_id          bigint    default 0     not null,
              order_line_item_id bigint    default 0     not null,
              staff_id           bigint    default 0     not null,
              pet_id             bigint    default 0     not null,
              customer_id        bigint    default 0     not null,
              start_datetime     timestamp               not null,
              end_datetime       timestamp               not null,
              created_at         timestamp default now() not null,
              updated_at         timestamp default now() not null,
              deleted_at         timestamp
              );
              
              comment on table staff_time_slot is 'Time slot details of staff';
              
              comment on column staff_time_slot.fulfillment_id is 'fulfillment.id';
              comment on column staff_time_slot.care_type is '1-Grooming, 2-Boarding, 3-Daycare, 4-Evaluation, 5-Dog_walking, 6-Training group class';
              comment on column staff_time_slot.detail_id is 'care_type = 1 grooming_detail.id, care_type = 4 evaluation_detail.id, care_type = 5 dog_walking_detail.id';
              comment on column staff_time_slot.order_line_item_id is 'order_line_item.id, Used to record whether the service has created an order';
              comment on column staff_time_slot.staff_id is 'The staff id who performing the service or add-on';
              comment on column staff_time_slot.start_datetime is 'The start date time of the this time slot';
              comment on column staff_time_slot.end_datetime is 'The end date time of the this time slot';
              create index staff_time_slot_idx_tenant_date
              on staff_time_slot (company_id, business_id, start_datetime);
              
              create index staff_time_slot_idx_fulfillment
              on staff_time_slot (fulfillment_id);
              
              create index staff_time_slot_idx_detail_id
              on staff_time_slot (care_type, detail_id);
              
              
              create table group_class_attendance
              (
              id                     bigserial
              primary key,
              company_id             bigint    default 0     not null,
              business_id            bigint    default 0     not null,
              fulfillment_id         bigint    default 0     not null,
              group_class_detail_id  bigint    default 0     not null,
              group_class_session_id bigint    default 0     not null,
              pet_id                 bigint    default 0     not null,
              check_in_time          timestamp               not null,
              created_at             timestamp default now() not null,
              updated_at             timestamp default now() not null,
              deleted_at             timestamp
              );
              
              comment on table group_class_attendance is 'Training attendance details of pet';
              
              comment on column group_class_attendance.fulfillment_id is 'fulfillment.id';
              comment on column group_class_attendance.group_class_detail_id is 'training_detail.id';
              comment on column group_class_attendance.group_class_session_id is 'training_session.id';
              comment on column group_class_attendance.check_in_time is 'Check in time of pet';
              
              create unique index group_class_attendance_uni_pet_check_in
              on group_class_attendance (fulfillment_id, group_class_session_id, pet_id);
              
              create index group_class_attendance_idx_session
              on group_class_attendance (group_class_session_id);

        - execute_sql:
            database: "moego_online_booking"
            sql: |
              CREATE TABLE "group_class_service_detail"
              (
                "id"                   BIGSERIAL PRIMARY KEY,
                "booking_request_id"   BIGINT       NOT NULL DEFAULT 0,
                "pet_id"               BIGINT       NOT NULL DEFAULT 0,
                "class_instance_id"    BIGINT       NOT NULL DEFAULT 0,
                "staff_id"             BIGINT       NOT NULL DEFAULT 0,
                "service_id"           BIGINT       NOT NULL default 0,
                "service_price"        NUMERIC(10, 2) NOT NULL DEFAULT 0,
                "specific_dates"       JSONB                 DEFAULT '[]'::JSONB,
                "start_time"           INTEGER,
                "end_time"             INTEGER,
                "duration_per_session" INT          NOT NULL DEFAULT 0,
                "created_at"           TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
                "updated_at"           TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
                "deleted_at"           TIMESTAMP(6)
              );
              CREATE INDEX group_class_service_detail_idx_request_id ON group_class_service_detail (booking_request_id);
              COMMENT
                  ON TABLE group_class_service_detail IS 'The group class service detail table';
              COMMENT
                  ON COLUMN group_class_service_detail.booking_request_id IS 'The id of booking request';
              COMMENT
                  ON COLUMN group_class_service_detail.pet_id IS 'The id of pet, associated with the current service';
              COMMENT
                  ON COLUMN group_class_service_detail.staff_id IS 'The id of trainer, associated with the current service';
              COMMENT
                  ON COLUMN group_class_service_detail.service_id IS 'The id of current service';
              COMMENT
                  ON COLUMN group_class_service_detail.specific_dates IS 'The date list of the group class session, ["yyyy-MM-dd"]';
              COMMENT
                  ON COLUMN group_class_service_detail.start_time IS 'The start time of the service, unit minute, 540 means 09:00';
              COMMENT
                  ON COLUMN group_class_service_detail.end_time IS 'The end time of the service, unit minute, 540 means 09:00';
              COMMENT
                  ON COLUMN group_class_service_detail.duration_per_session IS 'Duration of each session in minutes, only for training';

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_offering"
            sql: |
              -- auto-generated definition
              create table group_class_instance
              (
              id            bigserial
              primary key,
              created_at    timestamp with time zone default CURRENT_TIMESTAMP           not null,
              updated_at    timestamp with time zone default CURRENT_TIMESTAMP           not null,
              deleted_at    timestamp with time zone,
              company_id    bigint                   default 0                           not null,
              business_id   bigint                   default 0                           not null,
              service_id    bigint                   default 0                           not null,
              name          varchar(100)             default ''::character varying       not null,
              staff_id      bigint                   default 0                           not null,
              price         numeric(10, 2)           default 0                           not null,
              tax_id        bigint                   default 0                           not null,
              currency_code text                     default 'USD'::text                 not null,
              start_time    timestamp with time zone                                     not null,
              time_zone     text                     default 'America/Los_Angeles'::text not null,
              capacity      bigint                   default 0                           not null,
              occurrence    text                     default ''::text                    not null,
              status        text                     default 'STATUS_UNSPECIFIED'::text  not null
              );

              comment on column group_class_instance.company_id is 'Company ID';

              comment on column group_class_instance.business_id is 'Business ID';

              comment on column group_class_instance.service_id is 'alias group_class_id';

              comment on column group_class_instance.staff_id is 'Trainer ID';

              comment on column group_class_instance.occurrence is 'json::models/offering/v1/group_class_models/group_class_instance.occurrence';

              comment on column group_class_instance.status is 'enum:models/offering/v1/group_class_models/group_class_instance.status';

              create index idx_group_class_instance_business_id
              on group_class_instance (business_id);

              create index idx_group_class_instance_company_id
              on group_class_instance (company_id);

              create index idx_group_class_instance_status
              on group_class_instance (status);

              create table group_class_session
              (
              id           bigserial
              primary key,
              created_at   timestamp with time zone default CURRENT_TIMESTAMP not null,
              updated_at   timestamp with time zone default CURRENT_TIMESTAMP not null,
              deleted_at   timestamp with time zone,
              company_id   bigint                   default 0                 not null,
              business_id  bigint                   default 0                 not null,
              instance_id  bigint                   default 0                 not null,
              start_time   timestamp with time zone                           not null,
              duration_min bigint                   default 0                 not null,
              is_modified  boolean                  default false             not null
              );

              comment on column group_class_session.company_id is 'Company ID';

              comment on column group_class_session.start_time is 'Session start time';

              comment on column group_class_session.duration_min is 'Duration of sessions in minutes';

              comment on column group_class_session.is_modified is 'Manually modified flag';

              create index idx_group_class_session_instance_id
              on group_class_session (instance_id);

              create table event
              (
              id           bigserial
              primary key,
              created_at   timestamp with time zone default CURRENT_TIMESTAMP        not null,
              updated_at   timestamp with time zone default CURRENT_TIMESTAMP        not null,
              message_type text                     default 'TYPE_UNSPECIFIED'::text not null,
              reference_id text                     default ''::text                 not null,
              payload      text                     default '{}'::text               not null,
              status       text                     default ''::text                 not null,
              retry_times  bigint                   default 0                        not null
              );

              create index idx_event_reference_id
              on event (reference_id);

              create index idx_event_status
              on event (status);

        - execute_sql:
            database: "moego_fulfillment"
            sql: |

              create table fulfillment
              (
              id                   bigserial primary key,
              company_id           bigint      default 0                            not null,
              business_id          bigint      default 0                            not null,
              customer_id          bigint      default 0                            not null,
              booking_request_id   bigint      default 0                            not null,
              start_datetime       timestamp,
              end_datetime         timestamp,
              status               integer     default 1                            not null,
              color_code           varchar(20) default '#000000'::character varying not null,
              service_type_include integer     default 1                            not null,
              source               integer     default 22018                        not null,
              repeat_rule_id       integer     default 0                            not null,
              created_at           timestamp   default now()                        not null,
              updated_at           timestamp   default now()                        not null,
              deleted_at           timestamp,
              order_id             bigint      default 0                            not null
              );

              comment on table fulfillment is 'Fulfillment summary information, generated after a scheduled request has been scheduled';

              comment on column fulfillment.booking_request_id is 'booking_request.id, A booking request may be scheduled for multiple fulfillment';

              comment on column fulfillment.start_datetime is 'Minimum start date time for all service details in the current fulfillment';

              comment on column fulfillment.end_datetime is 'Maximum start date time for all service details in the current fulfillment';

              comment on column fulfillment.status is '1-unconfirmed, 2-confirmed, 3-finished, 4-canceled, 5-ready, 6-checked-in';

              comment on column fulfillment.service_type_include is 'The value of the bit composition of the various types of services, 1-boarding, 2-daycare, 3-grooming, 4-evaluation, 5-dog_walking';

              comment on column fulfillment.source is '22018-web, 22168-ob, 17216-android, 17802-ios, 23426-dm, 19826-gc, 23333-openapi';

              comment on column fulfillment.repeat_rule_id is 'fulfillment_repeat_rule.id';

              create index fulfillment_idx_tenant_customer
              on fulfillment (company_id, business_id, customer_id);

              create index fulfillment_idx_created_at
              on fulfillment (created_at);

              SELECT setval('fulfillment_id_seq', 1000000000, false);

              create table group_class_detail
              (
              id                      bigserial
              primary key,
              fulfillment_id          bigint    default 0     not null,
              pet_id                  bigint    default 0     not null,
              group_class_id          bigint    default 0     not null,
              group_class_instance_id bigint    default 0     not null,
              status                  integer   default 0     not null,
              created_at              timestamp default now() not null,
              updated_at              timestamp default now() not null,
              deleted_at              timestamp
              );

              comment on table group_class_detail is 'Details of pet training group class service';
              comment on column group_class_detail.fulfillment_id is 'fulfillment.id';
              comment on column group_class_detail.pet_id is 'moe_customer_pet.id';
              comment on column group_class_detail.status is '1-Not started, 2-In Progress, 3-Completed';
              comment on column group_class_detail.group_class_id is 'group class id, service.id';
              comment on column group_class_detail.group_class_instance_id is 'group_class_instance.id';
              create index group_class_detail_idx_fulfillment on group_class_detail (fulfillment_id);
              create unique index group_class_detail_uni_pet_instance on group_class_detail (group_class_instance_id, pet_id);


              create table staff_time_slot
              (
              id                 bigserial
              primary key,
              company_id         bigint    default 0     not null,
              business_id        bigint    default 0     not null,
              fulfillment_id     bigint    default 0     not null,
              care_type          integer   default 0     not null,
              detail_id          bigint    default 0     not null,
              order_line_item_id bigint    default 0     not null,
              staff_id           bigint    default 0     not null,
              pet_id             bigint    default 0     not null,
              customer_id        bigint    default 0     not null,
              start_datetime     timestamp               not null,
              end_datetime       timestamp               not null,
              created_at         timestamp default now() not null,
              updated_at         timestamp default now() not null,
              deleted_at         timestamp
              );

              comment on table staff_time_slot is 'Time slot details of staff';

              comment on column staff_time_slot.fulfillment_id is 'fulfillment.id';
              comment on column staff_time_slot.care_type is '1-Grooming, 2-Boarding, 3-Daycare, 4-Evaluation, 5-Dog_walking, 6-Training group class';
              comment on column staff_time_slot.detail_id is 'care_type = 1 grooming_detail.id, care_type = 4 evaluation_detail.id, care_type = 5 dog_walking_detail.id';
              comment on column staff_time_slot.order_line_item_id is 'order_line_item.id, Used to record whether the service has created an order';
              comment on column staff_time_slot.staff_id is 'The staff id who performing the service or add-on';
              comment on column staff_time_slot.start_datetime is 'The start date time of the this time slot';
              comment on column staff_time_slot.end_datetime is 'The end date time of the this time slot';
              create index staff_time_slot_idx_tenant_date
              on staff_time_slot (company_id, business_id, start_datetime);

              create index staff_time_slot_idx_fulfillment
              on staff_time_slot (fulfillment_id);

              create index staff_time_slot_idx_detail_id
              on staff_time_slot (care_type, detail_id);


              create table group_class_attendance
              (
              id                     bigserial
              primary key,
              company_id             bigint    default 0     not null,
              business_id            bigint    default 0     not null,
              fulfillment_id         bigint    default 0     not null,
              group_class_detail_id  bigint    default 0     not null,
              group_class_session_id bigint    default 0     not null,
              pet_id                 bigint    default 0     not null,
              check_in_time          timestamp               not null,
              created_at             timestamp default now() not null,
              updated_at             timestamp default now() not null,
              deleted_at             timestamp
              );

              comment on table group_class_attendance is 'Training attendance details of pet';

              comment on column group_class_attendance.fulfillment_id is 'fulfillment.id';
              comment on column group_class_attendance.group_class_detail_id is 'training_detail.id';
              comment on column group_class_attendance.group_class_session_id is 'training_session.id';
              comment on column group_class_attendance.check_in_time is 'Check in time of pet';

              create unique index group_class_attendance_uni_pet_check_in
              on group_class_attendance (fulfillment_id, group_class_session_id, pet_id);

              create index group_class_attendance_idx_session
              on group_class_attendance (group_class_session_id);
        

        - execute_sql:
            database: "moego_online_booking"
            sql: |
              CREATE TABLE "group_class_service_detail"
              (
              "id"                   BIGSERIAL PRIMARY KEY,
              "booking_request_id"   BIGINT       NOT NULL DEFAULT 0,
              "pet_id"               BIGINT       NOT NULL DEFAULT 0,
              "class_instance_id"    BIGINT       NOT NULL DEFAULT 0,
              "staff_id"             BIGINT       NOT NULL DEFAULT 0,
              "service_id"           BIGINT       NOT NULL default 0,
              "service_price"        NUMERIC(10, 2) NOT NULL DEFAULT 0,
              "specific_dates"       JSONB                 DEFAULT '[]'::JSONB,
              "start_time"           INTEGER,
              "end_time"             INTEGER,
              "duration_per_session" INT          NOT NULL DEFAULT 0,
              "created_at"           TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
              "updated_at"           TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
              "deleted_at"           TIMESTAMP(6)
              );
              CREATE INDEX group_class_service_detail_idx_request_id ON group_class_service_detail (booking_request_id);
              COMMENT
              ON TABLE group_class_service_detail IS 'The group class service detail table';
              COMMENT
              ON COLUMN group_class_service_detail.booking_request_id IS 'The id of booking request';
              COMMENT
              ON COLUMN group_class_service_detail.pet_id IS 'The id of pet, associated with the current service';
              COMMENT
              ON COLUMN group_class_service_detail.staff_id IS 'The id of trainer, associated with the current service';
              COMMENT
              ON COLUMN group_class_service_detail.service_id IS 'The id of current service';
              COMMENT
              ON COLUMN group_class_service_detail.specific_dates IS 'The date list of the group class session, ["yyyy-MM-dd"]';
              COMMENT
              ON COLUMN group_class_service_detail.start_time IS 'The start time of the service, unit minute, 540 means 09:00';
              COMMENT
              ON COLUMN group_class_service_detail.end_time IS 'The end time of the service, unit minute, 540 means 09:00';
              COMMENT
              ON COLUMN group_class_service_detail.duration_per_session IS 'Duration of each session in minutes, only for training';

  - api: "/moego.devops.api.console.v1.MessageQueueApi/CreateTopics"
    body:
      identifier:
        platform: AWS_MSK
        identifier: "kafka-staging"
      topics:
        - name: "moego.offering.staging"
  - api: "/moego.devops.api.console.v1.MessageQueueApi/CreateTopics"
    body:
      identifier:
        platform: AWS_MSK
        identifier: "moego-kafka"
      topics:
        - name: "moego.offering.production"