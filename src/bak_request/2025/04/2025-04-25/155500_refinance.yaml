name: "refinance"
description: "related slack: https://moegoworkspace.slack.com/archives/C07EN0YBPT3/p1745010749057209"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              UPDATE loan_offer
              SET offer_status = 'REPLACED',
              channel_offer_status = 'REFINANCED',
              version = 880,
              updated_time = CURRENT_TIMESTAMP()
              WHERE offer_id = 'PLO2MCA114361217f21cc68cd9190d5be5' and version = 879;
              INSERT INTO moe_payment.loan_offer_update_log (offer_id, channel_offer_id, before_offer_status, cur_offer_status,
                                                            before_channel_offer_status, cur_channel_offer_status, cur_version,
                                                            before_remain_amount, cur_remain_amount, event_type, event_id,
                                                            created_time, updated_time, before_processing_amount,
                                                            after_processing_amount, before_channel_offer_id)
              VALUES ('PLO2MCA114361217f21cc68cd9190d5be5', '0bc3f4dd-f31b-40a8-8605-b435fd5adce5', 'PAID_OUT', 'REPLACED',
                      'CURRENT', 'REFINANCED', 880, 1389.47, 1389.47, 'refinanced by chi', NOW(), DEFAULT, DEFAULT, 0.00, 0.00,
                      '0bc3f4dd-f31b-40a8-8605-b435fd5adce5');

              UPDATE loan_offer
              SET offer_status = 'REPLACED',
              channel_offer_status = 'REFINANCED',
              version = 172,
              updated_time = CURRENT_TIMESTAMP()
              WHERE offer_id = 'PLO2MCA110373217eda5765a7634e48196' and version = 171;
              INSERT INTO moe_payment.loan_offer_update_log (offer_id, channel_offer_id, before_offer_status, cur_offer_status,
                                                            before_channel_offer_status, cur_channel_offer_status, cur_version,
                                                            before_remain_amount, cur_remain_amount, event_type, event_id,
                                                            created_time, updated_time, before_processing_amount,
                                                            after_processing_amount, before_channel_offer_id)
              VALUES ('PLO2MCA110373217eda5765a7634e48196', '46d3d7c9-642a-4c40-992a-5ea6bc02c8c6', 'PAID_OUT', 'REPLACED',
                      'CURRENT', 'REFINANCED', 172, 214.66, 214.66, 'refinanced by chi', NOW(), DEFAULT, DEFAULT, 0.00, 0.00,
                      '46d3d7c9-642a-4c40-992a-5ea6bc02c8c6');