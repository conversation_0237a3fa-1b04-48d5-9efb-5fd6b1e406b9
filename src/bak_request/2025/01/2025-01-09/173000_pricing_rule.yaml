name: "Price rule: add rule_apply_choice_type"
description: "add rule_apply_choice_type to pricing_rule"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_offering"
            sql: |
              alter table pricing_rule
                  add rule_apply_choice_type smallint default 1 not null;

              comment on column pricing_rule.rule_apply_choice_type is 'rule apply choice type, 1-sequence, 2-choose highest';