name: "Grant permission to <PERSON>"
description: "grant permission to <PERSON>"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_business"
            username: "${db.user.admin}"
            sql: |
              GRANT SELECT ON moe_business.* TO 'developer_dennis'@'%';