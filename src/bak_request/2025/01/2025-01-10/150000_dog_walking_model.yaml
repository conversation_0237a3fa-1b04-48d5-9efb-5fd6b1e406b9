name: "Create table dog_walking_service_detail"
description: "Create table dog_walking_service_detail"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: "AWS_RDS"
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_online_booking"
            sql: |
              create table dog_walking_service_detail
              (
              id                 bigserial
              primary key,
              booking_request_id bigint         default 0                 not null,
              pet_id             bigint         default 0                 not null,
              staff_id           bigint         default 0                 not null,
              service_id         bigint         default 0                 not null,
              service_time       integer        default 0                 not null,
              service_price      numeric(10, 2) default 0.00              not null,
              tax_id             bigint         default 0                 not null,
              start_date         date,
              start_time         integer,
              end_date           date,
              end_time           integer,
              created_at         timestamp      default CURRENT_TIMESTAMP not null,
              updated_at         timestamp      default CURRENT_TIMESTAMP not null,
              deleted_at         timestamp
              );
              
              comment on table dog_walking_service_detail is 'The dog walking service detail table';
              
              comment on column dog_walking_service_detail.booking_request_id is 'The id of booking request';
              
              comment on column dog_walking_service_detail.pet_id is 'The id of pet, associated with the current service';
              
              comment on column dog_walking_service_detail.staff_id is 'The id of staff, associated with the current service';
              
              comment on column dog_walking_service_detail.service_id is 'The id of current service';
              
              comment on column dog_walking_service_detail.service_time is 'The time of current service, unit minute';
              
              comment on column dog_walking_service_detail.service_price is 'The price of current service';
              
              comment on column dog_walking_service_detail.start_date is 'The start date of the service, yyyy-MM-dd';
              
              comment on column dog_walking_service_detail.start_time is 'The start time of the service, unit minute, 540 means 09:00';
              
              comment on column dog_walking_service_detail.end_date is 'The end date of the service, yyyy-MM-dd';
              
              comment on column dog_walking_service_detail.end_time is 'The end time of the service, unit minute, 540 means 09:00';
              
              create index dog_walking_service_detail_idx_request_id
              on dog_walking_service_detail (booking_request_id);