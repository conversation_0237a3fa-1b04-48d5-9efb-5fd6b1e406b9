name: "Fix service dirty data"
description: "Fix service dirty data"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              update moe_grooming.moe_grooming_service
              set breed_filter = 0,
              update_time  = unix_timestamp()
              where id = 1302531;
