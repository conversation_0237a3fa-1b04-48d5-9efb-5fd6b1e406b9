name: "Fix duplicate invoice deposit"
description: "Fix duplicate invoice deposit"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              delete
              from moe_grooming.moe_invoice_deposit
              where invoice_id in (129724573, 130234638, 131067063, 135210581, 135509031)
              and status = 1;
