name: "Add task management feature"
description: "Add task management feature"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              INSERT INTO moe_payment.moe_feature (name, code)
              VALUES ('Task management', 'taskManagement');
              INSERT INTO moe_payment.moe_plan_feature_relation (level, code)
              VALUES (1201, 'taskManagement');
              INSERT INTO moe_payment.moe_plan_feature_relation (level, code)
              VALUES (1202, 'taskManagement');