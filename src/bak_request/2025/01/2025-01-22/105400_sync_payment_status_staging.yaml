name: "set payment_status"
description: "set payment_status"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_online_booking"
            sql: |
              UPDATE public.booking_request
              SET payment_status = CASE
                                       WHEN is_prepaid = false THEN 1
                                       WHEN is_prepaid = true THEN 3
                                       ELSE payment_status
                  END
              WHERE payment_status = 0
                and deleted_at IS NULL;