name: "授权 permission 库权限给 haozhi"
description: "授权 permission 库权限给 haozhi"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_permission"
            username: "${db.user.admin}"
            sql: |
              GRANT USAGE ON SCHEMA public TO developer_haozhi;
              GRANT SELECT, UPDATE, DELETE, INSERT ON ALL TABLES IN SCHEMA public TO developer_haozhi;
              ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, UPDATE, DELETE, INSERT ON TABLES TO developer_haozhi;
        - execute_sql:
            database: "moego_admin_v3"
            sql: |
              insert into role_binding(account_id, role_id,operator_id,created_at) select '<EMAIL>', id,'<EMAIL>',now() from role where name = '<PERSON>OpsAuditor';

