name: "set payment_status"
description: "set payment_status"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_online_booking"
            sql: |
              UPDATE public.booking_request
              SET payment_status = 3
              WHERE payment_status = 0
                AND deleted_at IS NULL
                AND is_prepaid = true;
              
              UPDATE public.booking_request
              SET payment_status = 1
              WHERE payment_status = 0
                AND deleted_at IS NULL
                AND is_prepaid = false;