name: "Fix duplicate invoice deposit"
description: "Fix duplicate invoice deposit"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_message"
            sql: |
              INSERT INTO moe_message.moe_forward_business (to_phone_number, company_id, business_id)
              VALUES 
              ('+***********', 113020, 113424),
              ('+***********', 111798, 112160),
              ('+***********', 113804, 114236),
              ('+***********', 114123, 114561);
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_engagement"
            sql: |
              insert into forward_business(to_phone_number, company_id, business_id)
              VALUES 
              ('+***********', 113020, 113424),
              ('+***********', 111798, 112160),
              ('+***********', 113804, 114236),
              ('+***********', 114123, 114561);

