name: "Fix customer create setting"
description: "Fix customer create setting"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_customer"
            sql: |
              update moe_customer.customer_creation_setting
              set enable_creation_from_call = 1,
                  enable_creation_from_sms  = 1,
                  updated_at = now()
              where company_id in (
                                  111701,
                                  9108,
                                  111047,
                                  120656
                  );
