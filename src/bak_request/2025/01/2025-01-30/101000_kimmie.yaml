name: "Fix duplicate invoice deposit"
description: "Fix duplicate invoice deposit"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_sms"
            sql: |
              UPDATE public.business_sms_setting
              SET twilio_sid    = '**********************************',
                  twilio_token  = 'd93ed64f090b788d3d1a9f0d8dfe2392',
                  twilio_number = '+***********'
              WHERE business_id = 112137;
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_message"
            sql: |
              INSERT INTO moe_message.moe_forward_business (to_phone_number, company_id, business_id)
              VALUES ('+***********', 111775, 112137);
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_engagement"
            sql: |
              insert into forward_business(to_phone_number, company_id, business_id)
              VALUES ('+***********', 111775, 112137);
