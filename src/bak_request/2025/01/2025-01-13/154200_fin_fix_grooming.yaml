name: "CS-24281: fix inconsistent status of grooming"
description: "将 grooming 数据库中的订单状态不一致的数据修复，设置为 fully_paid"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              UPDATE moe_grooming_appointment
              SET is_paid = 1, update_time = UNIX_TIMESTAMP()
              WHERE id = 62221019;