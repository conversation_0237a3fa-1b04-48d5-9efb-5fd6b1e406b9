name: "add accounting retry log"
description: "add accounting retry log"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_accounting"
            sql: |
              INSERT INTO public.accounting_sync_retry_log (created_time, retry_entity_type, retry_entity_id, channel_type) VALUES(now(), 'PAYOUT', 'po_1QUi3cI3Kh2z2ZwAgIiDgVAG', 'LAYER');