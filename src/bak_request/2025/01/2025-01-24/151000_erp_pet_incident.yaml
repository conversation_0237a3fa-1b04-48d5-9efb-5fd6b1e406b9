name: "customer 添加 pet incident 相关新字段"
description: "customer 添加 pet incident 相关新字段段"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_permission"
            sql: |
              INSERT INTO public.permission (id, name, display_name, description, parent_permission_id, category_id)
              VALUES (266, 'createOrEditIncidentReport', 'Can create/edit incident report',
              'Allow staff member to create or edit pet incident report', 10, 3);
  
              insert into public.role_permission_mapping(role_id, permission_id)
              select role.id, 266 from role, role_permission_mapping m
              where deleted_at is null and role.id = m.role_id and m.permission_id = 13;

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_permission"
            sql: |
              INSERT INTO public.permission (id, name, display_name, description, parent_permission_id, category_id)
              VALUES (266, 'createOrEditIncidentReport', 'Can create/edit incident report',
              'Allow staff member to create or edit pet incident report', 10, 3);
              
              insert into public.role_permission_mapping(role_id, permission_id)
              select role.id, 266 from role, role_permission_mapping m
              where deleted_at is null and role.id = m.role_id and m.permission_id = 13;