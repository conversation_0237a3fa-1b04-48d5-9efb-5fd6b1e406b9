name: "修改用户支付数据"
description: "修改用户支付数据"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              UPDATE public.order
              SET 
                  status = 2, 
                  extra_fee_amount = 2.16, 
                  payment_status = 'fully paid', 
                  total_amount = 57.16, 
                  remain_amount = 0.00, 
                  update_time = NOW()
              WHERE 
                  id = 132435738 
                  AND business_id = '114043';
              
              UPDATE public.order_line_extra_fee
              SET is_deleted = 'true',update_time = NOW()
              where id = 117760749 and business_id = '114043' and order_id = '132435738';