name: "membership quantity  membership 表新增字段 && 新增membership perk表"
description: "membership quantity  membership 表新增字段 && 新增membership perk表"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: "AWS_RDS"
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_membership"
            sql: |
              CREATE TABLE IF NOT EXISTS "public"."membership_perk" (
              "id" BIGSERIAL PRIMARY KEY,
              "entitlement_id" int8 NOT NULL,
              "membership_id" int8 NOT NULL,
              "validity_start_time" timestamp(6) NOT NULL,
              "validity_end_time" timestamp(6) NOT NULL,
              "created_at" timestamp(6) NOT NULL DEFAULT now(),
              "updated_at" timestamp(6) NOT NULL DEFAULT now(),
              "deleted_at" timestamp(6)
              );
              
            
              
              CREATE INDEX "idx_membership_perk_validity" ON "public"."membership_perk" USING btree (
              "membership_id" "pg_catalog"."int8_ops" ASC NULLS LAST,
              "entitlement_id" "pg_catalog"."int8_ops" ASC NULLS LAST,
              "validity_start_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
              );
              
              CREATE INDEX "idx_membership_perk_validity_start_time" ON "public"."membership_perk" USING btree (
              "validity_start_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
              );
              
              CREATE UNIQUE INDEX "membership_perk_entitlement_id_deleted_at_null_unique" ON "public"."membership_perk" USING btree (
              "entitlement_id" "pg_catalog"."int8_ops" ASC NULLS LAST
              ) WHERE deleted_at IS NULL;
              
              CREATE TRIGGER "membership_perk_update_at" BEFORE UPDATE ON "public"."membership_perk"
              FOR EACH ROW
              EXECUTE PROCEDURE "public"."update_at"();
              
              ALTER TABLE "public"."membership" ADD COLUMN "billing_cycle_day_of_week" text COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'DAY_OF_WEEK_UNSPECIFIED'::text;
              
              CREATE TRIGGER "membership_update_at"
              BEFORE UPDATE ON "public"."membership"
              FOR EACH ROW
              EXECUTE FUNCTION "public"."update_at"();
  
              CREATE TRIGGER "membership_discount_benefits_update_at"
              BEFORE UPDATE ON "public"."membership_discount_benefits"
              FOR EACH ROW
              EXECUTE FUNCTION "public"."update_at"();
  
              CREATE TRIGGER "membership_quantity_benefits_update_at"
              BEFORE UPDATE ON "public"."membership_quantity_benefits"
              FOR EACH ROW
              EXECUTE FUNCTION "public"."update_at"();
  
              CREATE TRIGGER "recommend_benefit_usage_update_at"
              BEFORE UPDATE ON "public"."recommend_benefit_usage"
              FOR EACH ROW
              EXECUTE FUNCTION "public"."update_at"();
  
              CREATE TRIGGER "redeem_history_update_at"
              BEFORE UPDATE ON "public"."redeem_history"
              FOR EACH ROW
              EXECUTE FUNCTION "public"."update_at"();
  
              CREATE TRIGGER "subscription_update_at"
              BEFORE UPDATE ON "public"."subscription"
              FOR EACH ROW
              EXECUTE FUNCTION "public"."update_at"();