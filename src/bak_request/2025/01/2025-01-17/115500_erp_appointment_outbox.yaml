name: "pg moego_appointment new table"
description: "pg moego_appointment new table"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-cluster"
      actions:
        - execute_sql:
            database: "moego_grooming"
            sql: |
              CREATE TABLE if not exists moe_grooming.appointment_outbox (
                `id` bigint NOT NULL AUTO_INCREMENT,
                `topic` varchar(255) NOT NULL COMMENT 'topic name',
                `event_id` varchar(128) NOT NULL COMMENT 'id for event record',
                `event_time` datetime NOT NULL COMMENT 'time for event record',
                `event_key` varchar(128) NOT NULL COMMENT 'key for event record',
                `event_type` int NOT NULL COMMENT 'defined in com.moego.idl.models.event_bus.v1.EventType',
                `event_detail` text NOT NULL COMMENT 'detail for event record',
                `status` tinyint NOT NULL DEFAULT '1' COMMENT 'message push status.1 pending 2 sent 3 failed',
                `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time  ',
                PRIMARY KEY (`id`),
                KEY `idx_created_at` (`created_at`)
              ) COMMENT='store appointment related messages that need to be pushed';
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moego_grooming"
            sql: |
              CREATE TABLE if not exists moe_grooming.appointment_outbox (
                `id` bigint NOT NULL AUTO_INCREMENT,
                `topic` varchar(255) NOT NULL COMMENT 'topic name',
                `event_id` varchar(128) NOT NULL COMMENT 'id for event record',
                `event_time` datetime NOT NULL COMMENT 'time for event record',
                `event_key` varchar(128) NOT NULL COMMENT 'key for event record',
                `event_type` int NOT NULL COMMENT 'defined in com.moego.idl.models.event_bus.v1.EventType',
                `event_detail` text NOT NULL COMMENT 'detail for event record',
                `status` tinyint NOT NULL DEFAULT '1' COMMENT 'message push status.1 pending 2 sent 3 failed',
                `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time  ',
                PRIMARY KEY (`id`),
                KEY `idx_created_at` (`created_at`)
              ) COMMENT='store appointment related messages that need to be pushed';