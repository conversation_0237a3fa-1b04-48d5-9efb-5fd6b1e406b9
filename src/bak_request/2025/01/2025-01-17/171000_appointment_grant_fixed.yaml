name: "Grant appointment to <PERSON>"
description: "Grant appointment to <PERSON>"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: "AWS_RDS"
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_appointment"
            sql: |
              GRANT CONNECT ON DATABASE moego_appointment TO developer_jason;
              GRANT USAGE ON SCHEMA public TO developer_jason;
              GRANT SELECT ON ALL TABLES IN SCHEMA public TO developer_jason;
              ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO developer_jason;