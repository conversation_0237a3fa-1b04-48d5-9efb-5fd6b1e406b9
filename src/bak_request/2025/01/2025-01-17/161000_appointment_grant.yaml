name: "<PERSON> moego_appointment db permission to <PERSON> & Freeman & Jason"
description: "<PERSON> moego_appointment db permission to <PERSON> & Freeman & Jason"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: "AWS_RDS"
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_appointment"
            sql: |
              GRANT USAGE ON SCHEMA public TO developer_bob;
              GRANT USAGE ON SCHEMA public TO developer_freeman;
              GRANT USAGE ON SCHEMA public TO developer_jason;

              GRANT SELECT ON ALL TABLES IN SCHEMA public TO developer_jason;
              GRANT SELECT ON ALL TABLES IN SCHEMA public TO developer_bob;
              GRANT SELECT ON ALL TABLES IN SCHEMA public TO developer_freeman;