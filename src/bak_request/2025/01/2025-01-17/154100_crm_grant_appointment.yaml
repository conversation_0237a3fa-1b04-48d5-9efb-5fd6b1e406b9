name: "Grant moego_appointment permission to hz"
description: "grant moego_appointment permission to hz"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_appointment"
            username: "${db.user.admin}"
            sql: |
              GRANT CONNECT ON DATABASE moego_appointment TO developer_haozhi;
              GRANT USAGE ON SCHEMA public TO developer_haozhi;
              GRANT SELECT ON ALL TABLES IN SCHEMA public TO developer_haozhi;
              ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO developer_haozhi;