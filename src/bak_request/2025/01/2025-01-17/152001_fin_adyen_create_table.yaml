requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_payment"
            sql: |
              -- channel recurring payment method
              CREATE TABLE moego_payment.public.channel_recurring_payment_method (
                id BIGSERIAL NOT NULL,
                created_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                updated_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                extra text NOT NULL DEFAULT '',
                alias text NOT NULL DEFAULT '',
                channel_type text NOT NULL DEFAULT '',
                channel_customer_id text NOT NULL DEFAULT '',
                entity_type text NOT NULL DEFAULT '',
                entity_id BIGINT NOT NULL DEFAULT 0,
                method_type text NOT NULL DEFAULT '',
                channel_payment_method text NOT NULL DEFAULT '',
                is_primary BOOLEAN DEFAULT FALSE,
                CONSTRAINT channel_recurring_payment_method_pk PRIMARY KEY (id)
              );
              CREATE UNIQUE INDEX uniq_channel_payment_method ON public.channel_recurring_payment_method (channel_type,channel_payment_method);
              COMMENT ON TABLE channel_recurring_payment_method IS 'channel recurring payment method,e.g. COF';
              COMMENT ON COLUMN channel_recurring_payment_method.id is 'primary key';
              COMMENT ON COLUMN channel_recurring_payment_method.created_time is 'created time';
              COMMENT ON COLUMN channel_recurring_payment_method.updated_time is 'updated time';
              COMMENT ON COLUMN channel_recurring_payment_method.extra is 'extra config, json';
              COMMENT ON COLUMN channel_recurring_payment_method.alias is 'alias of recurring payment method';
              COMMENT ON COLUMN channel_recurring_payment_method.channel_type is 'channel type';
              COMMENT ON COLUMN channel_recurring_payment_method.channel_customer_id is 'channel customer id';
              COMMENT ON COLUMN channel_recurring_payment_method.entity_type is '实体类型';
              COMMENT ON COLUMN channel_recurring_payment_method.entity_id is '实体id,如 moego customer id';
              COMMENT ON COLUMN channel_recurring_payment_method.method_type is '存储的支付方式类型,如COF';
              COMMENT ON COLUMN channel_recurring_payment_method.channel_payment_method is 'channel payment method';
              COMMENT ON COLUMN channel_recurring_payment_method.is_primary is 'is primary';

              -- channel customer
              CREATE TABLE moego_payment.public.channel_customer (
                id BIGSERIAL NOT NULL,
                created_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                updated_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                extra text NOT NULL DEFAULT '',
                entity_type text NOT NULL DEFAULT '',
                entity_id BIGINT NOT NULL DEFAULT 0,
                channel_type text NOT NULL DEFAULT '',
                channel_customer_id text NOT NULL DEFAULT '',
                CONSTRAINT channel_customer_pk PRIMARY KEY (id)
              );

              CREATE UNIQUE INDEX uniq_entity_channel_customer ON public.channel_customer (entity_id,entity_type,channel_type);
              CREATE UNIQUE INDEX uniq_channel_customer ON public.channel_customer (channel_customer_id,channel_type);
              COMMENT ON TABLE channel_customer IS 'channel customer';
              COMMENT ON COLUMN channel_customer.id is 'primary key';
              COMMENT ON COLUMN channel_customer.created_time is 'created time';
              COMMENT ON COLUMN channel_customer.updated_time is 'updated time';
              COMMENT ON COLUMN channel_customer.extra is 'extra config, json';
              COMMENT ON COLUMN channel_customer.entity_type is '实体类型';
              COMMENT ON COLUMN channel_customer.entity_id is '实体id';
              COMMENT ON COLUMN channel_customer.channel_type is '渠道类型';
              COMMENT ON COLUMN channel_customer.channel_customer_id is '渠道customer id';

              -- channel account
              CREATE TABLE moego_payment.public.channel_account (
                id BIGSERIAL NOT NULL,
                created_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                updated_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                extra text NOT NULL DEFAULT '',
                entity_type text NOT NULL DEFAULT '',
                entity_id BIGINT NOT NULL DEFAULT 0,
                email text NOT NULL DEFAULT '',
                country text NOT NULL DEFAULT '',
                default_currency text NOT NULL DEFAULT '',
                channel_type text NOT NULL DEFAULT '',
                channel_account_id text NOT NULL DEFAULT '',
                channel_onboard_status text NOT NULL DEFAULT '',

                CONSTRAINT channel_account_pk PRIMARY KEY (id)
              );

              CREATE UNIQUE INDEX uniq_entity_channel_account ON public.channel_account (entity_id,entity_type,channel_type);
              CREATE UNIQUE INDEX uniq_channel_account ON public.channel_account (channel_account_id,channel_type);
              COMMENT ON TABLE channel_account IS 'channel account';
              COMMENT ON COLUMN channel_account.id is 'primary key';
              COMMENT ON COLUMN channel_account.created_time is 'created time';
              COMMENT ON COLUMN channel_account.updated_time is 'updated time';
              COMMENT ON COLUMN channel_account.extra is 'extra config, json';
              COMMENT ON COLUMN channel_account.entity_type is '实体类型';
              COMMENT ON COLUMN channel_account.entity_id is '实体id';
              COMMENT ON COLUMN channel_account.email is '实体的email';
              COMMENT ON COLUMN channel_account.country is '国家';
              COMMENT ON COLUMN channel_account.default_currency is '默认币种';
              COMMENT ON COLUMN channel_account.channel_type is '渠道类型';
              COMMENT ON COLUMN channel_account.channel_account_id is '渠道账户id';
              COMMENT ON COLUMN channel_account.channel_onboard_status is '渠道onboard状态';

              -- payment
              CREATE TABLE moego_payment.public.payment (
                id BIGSERIAL NOT NULL,
                created_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                updated_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                extra text NOT NULL DEFAULT '',
                external_type text NOT NULL DEFAULT '',
                external_id text NOT NULL DEFAULT '',
                payer_type text NOT NULL DEFAULT '',
                payer_id BIGINT NOT NULL DEFAULT 0,
                payee_type text NOT NULL DEFAULT '',
                payee_id BIGINT NOT NULL DEFAULT 0,
                payment_type text NOT NULL DEFAULT '',
                channel_type text NOT NULL DEFAULT '',
                channel_payment_id text NOT NULL DEFAULT '',
                channel_reference_id text NOT NULL DEFAULT '',
                status text NOT NULL DEFAULT '',
                currency CHAR(3) NOT NULL,
                amount BIGINT NOT NULL DEFAULT 0,
                processing_fee BIGINT NOT NULL DEFAULT 0,
                convenience_fee BIGINT NOT NULL DEFAULT 0,
                tips BIGINT NOT NULL DEFAULT 0,
                discount BIGINT NOT NULL DEFAULT 0,
                refund_status text NOT NULL DEFAULT '',
                refund_count INTEGER NOT NULL DEFAULT 0,
                refunding_amount BIGINT NOT NULL DEFAULT 0,
                refunded_amount BIGINT NOT NULL DEFAULT 0,
                paid_by text NOT NULL DEFAULT '',
                description text NOT NULL DEFAULT '',

                CONSTRAINT payment_pk PRIMARY KEY (id)
              );

              CREATE UNIQUE INDEX uniq_external_payment ON public.payment (external_id,external_type);
              CREATE UNIQUE INDEX uniq_channel_payment_reference ON public.payment (channel_reference_id);
              -- CREATE UNIQUE INDEX uniq_channel_payment ON public.payment (channel_payment_id,channel_type);
              CREATE INDEX idx_payment_payer ON public.payment (payer_id,payer_type);
              CREATE INDEX idx_payment_payee ON public.payment (payee_id,payee_type);
              COMMENT ON TABLE payment IS 'channel account';
              COMMENT ON COLUMN payment.id is 'primary key';
              COMMENT ON COLUMN payment.created_time is 'created time';
              COMMENT ON COLUMN payment.updated_time is 'updated time';
              COMMENT ON COLUMN payment.extra is 'extra config, json';
              COMMENT ON COLUMN payment.external_type is '外部业务类型,如ORDER';
              COMMENT ON COLUMN payment.external_id is '外部业务id,如orderPaymentId';
              COMMENT ON COLUMN payment.payer_type is '买家类型,如CUSTOMER';
              COMMENT ON COLUMN payment.payer_id is '买家id,如customer id';
              COMMENT ON COLUMN payment.payee_type is '卖家类型,如BUSINESS';
              COMMENT ON COLUMN payment.payee_id is '卖家id,如business id';
              COMMENT ON COLUMN payment.payment_type is '支付类型,STANDARD/PRE_AUTH/PRE_PAY/DEPOSIT';
              COMMENT ON COLUMN payment.channel_type is '渠道类型';
              COMMENT ON COLUMN payment.channel_payment_id is '渠道支付id';
              COMMENT ON COLUMN payment.channel_reference_id is '渠道支付关联的幂等key,uuid';
              COMMENT ON COLUMN payment.status is '支付状态';
              COMMENT ON COLUMN payment.currency is '币种,三个字符';
              COMMENT ON COLUMN payment.amount is '支付总金额,单位:分';
              COMMENT ON COLUMN payment.processing_fee is '手续费,单位:分';
              COMMENT ON COLUMN payment.convenience_fee is 'fee by client,单位:分';
              COMMENT ON COLUMN payment.tips is 'payment直接关联的小费,单位:分';
              COMMENT ON COLUMN payment.discount is 'payment直接关联的优惠,单位:分';
              COMMENT ON COLUMN payment.refund_status is '退款状态,PARTIAL_REFUND/FULLY_REFUND';
              COMMENT ON COLUMN payment.refund_count is '退款次数';
              COMMENT ON COLUMN payment.refunding_amount is '退款中金额';
              COMMENT ON COLUMN payment.refunded_amount is '已退款总金额';
              COMMENT ON COLUMN payment.paid_by is 'paid by,自定义,默认customer';
              COMMENT ON COLUMN payment.description is '支付描述,自定义';

              ALTER SEQUENCE payment_id_seq RESTART WITH 5000000000;

              -- payment method
              CREATE TABLE moego_payment.public.payment_method (
                id BIGSERIAL NOT NULL,
                created_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                updated_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                payment_id BIGINT NOT NULL DEFAULT 0,
                method_type text NOT NULL DEFAULT '',
                channel_type text NOT NULL DEFAULT '',
                channel_payment_method_type text NOT NULL DEFAULT '',
                channel_payment_method_id text NOT NULL DEFAULT '',
                details text NOT NULL DEFAULT '',

                CONSTRAINT payment_method_pk PRIMARY KEY (id)
              );

              CREATE UNIQUE INDEX uniq_payment_method ON public.payment_method (payment_id);
              COMMENT ON TABLE payment_method IS 'payment method';
              COMMENT ON COLUMN payment_method.id is 'primary key';
              COMMENT ON COLUMN payment_method.created_time is 'created time';
              COMMENT ON COLUMN payment_method.updated_time is 'updated time';
              COMMENT ON COLUMN payment_method.payment_id is '支付单据id';
              COMMENT ON COLUMN payment_method.method_type is '支付方式类型,CARD/ONLINE/READER/OTHER';
              COMMENT ON COLUMN payment_method.channel_type is '渠道类型';
              COMMENT ON COLUMN payment_method.channel_payment_method_type is '渠道支付方式类型';
              COMMENT ON COLUMN payment_method.channel_payment_method_id is '渠道支付方式id';
              COMMENT ON COLUMN payment_method.details is '支付方式详情,json';

              -- refund
              CREATE TABLE moego_payment.public.refund (
                id BIGSERIAL NOT NULL,
                created_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                updated_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                extra text NOT NULL DEFAULT '',
                external_type text NOT NULL DEFAULT '',
                external_id text NOT NULL DEFAULT '',
                payer_type text NOT NULL DEFAULT '',
                payer_id BIGINT NOT NULL DEFAULT 0,
                payee_type text NOT NULL DEFAULT '',
                payee_id BIGINT NOT NULL DEFAULT 0,
                channel_type text NOT NULL DEFAULT '',
                channel_reference_id text NOT NULL DEFAULT '',
                channel_refund_id text NOT NULL DEFAULT '',
                payment_id BIGINT NOT NULL DEFAULT 0,
                currency CHAR(3) NOT NULL,
                amount BIGINT NOT NULL DEFAULT 0,
                status text NOT NULL DEFAULT '',
                reason text NOT NULL DEFAULT '',

                CONSTRAINT refund_pk PRIMARY KEY (id)
              );

              CREATE UNIQUE INDEX uniq_external_refund ON public.refund (external_id,external_type);
              CREATE UNIQUE INDEX uniq_channel_refund_reference ON public.refund (channel_reference_id);
              -- CREATE UNIQUE INDEX uniq_channel_refund ON public.refund (channel_refund_id,channel_type);
              CREATE INDEX idx_refund_payment_id ON public.refund (payment_id);
              CREATE INDEX idx_refund_payer ON public.refund (payer_id,payer_type);
              CREATE INDEX idx_refund_payee ON public.refund (payee_id,payee_type);
              COMMENT ON TABLE refund IS 'refund';
              COMMENT ON COLUMN refund.id is 'primary key';
              COMMENT ON COLUMN refund.created_time is 'created time';
              COMMENT ON COLUMN refund.updated_time is 'updated time';
              COMMENT ON COLUMN refund.extra is 'extra config, json';
              COMMENT ON COLUMN refund.external_type is '外部业务类型,如ORDER';
              COMMENT ON COLUMN refund.external_id is '外部业务id,如refundOrderId';
              COMMENT ON COLUMN refund.payer_type is '买家类型,如CUSTOMER';
              COMMENT ON COLUMN refund.payer_id is '买家id,如customer id';
              COMMENT ON COLUMN refund.payee_type is '卖家类型,如BUSINESS';
              COMMENT ON COLUMN refund.payee_id is '卖家id,如business id';
              COMMENT ON COLUMN refund.channel_type is '渠道类型';
              COMMENT ON COLUMN refund.channel_reference_id is '渠道退款关联的幂等key,uuid';
              COMMENT ON COLUMN refund.channel_refund_id is '渠道退款id';
              COMMENT ON COLUMN refund.payment_id is '支付单据id';
              COMMENT ON COLUMN refund.currency is '币种';
              COMMENT ON COLUMN refund.amount is '退款金额';
              COMMENT ON COLUMN refund.status is '退款状态';
              COMMENT ON COLUMN refund.reason is '退款原因';

              ALTER SEQUENCE refund_id_seq RESTART WITH 5000000000;

              -- exception log 
              CREATE TABLE moego_payment.public.exception_log (
                id BIGSERIAL NOT NULL,
                created_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                updated_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                extra text NOT NULL DEFAULT '',
                origin_type text NOT NULL DEFAULT '',
                origin_id BIGINT NOT NULL DEFAULT 0,
                reason text NOT NULL DEFAULT '',
                channel_type text NOT NULL DEFAULT '',
                channel_origin_id text NOT NULL DEFAULT '',

                CONSTRAINT exception_log_pk PRIMARY KEY (id)
              );

              COMMENT ON TABLE exception_log IS 'exception log';
              COMMENT ON COLUMN exception_log.id is 'primary key';
              COMMENT ON COLUMN exception_log.created_time is 'created time';
              COMMENT ON COLUMN exception_log.updated_time is 'updated time';
              COMMENT ON COLUMN exception_log.extra is 'extra config, json';
              COMMENT ON COLUMN exception_log.origin_type is '原始单据类型，如PAYMENT 和 REFUND';
              COMMENT ON COLUMN exception_log.origin_id is '原始单据id，如payment_id 和 refund_id';
              COMMENT ON COLUMN exception_log.reason is '异常原因';
              COMMENT ON COLUMN exception_log.channel_type is '渠道类型';
              COMMENT ON COLUMN exception_log.channel_origin_id is '渠道关联id';

              -- dispute
              CREATE TABLE moego_payment.public.dispute (
                id BIGSERIAL NOT NULL,
                created_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                updated_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                extra text NOT NULL DEFAULT '',
                status text NOT NULL DEFAULT '',
                payment_id BIGINT NOT NULL DEFAULT 0,
                channel_payment_id text NOT NULL DEFAULT '',
                payer_type text NOT NULL DEFAULT '',
                payer_id BIGINT NOT NULL DEFAULT 0,
                payee_type text NOT NULL DEFAULT '',
                payee_id BIGINT NOT NULL DEFAULT 0,
                channel_type text NOT NULL DEFAULT '',
                channel_dispute_id text NOT NULL DEFAULT '',
                channel_charge_id text NOT NULL DEFAULT '',
                channel_transfer_id text NOT NULL DEFAULT '',
                currency CHAR(3) NOT NULL,
                amount BIGINT NOT NULL DEFAULT 0,
                dispute_fee BIGINT NOT NULL DEFAULT 0,
                reason text NOT NULL DEFAULT '',

                CONSTRAINT dispute_pk PRIMARY KEY (id)
              );

              CREATE UNIQUE INDEX uniq_channel_dispute ON public.dispute (channel_type,channel_dispute_id);
              COMMENT ON TABLE dispute IS 'dispute';
              COMMENT ON COLUMN dispute.id is 'primary key';
              COMMENT ON COLUMN dispute.created_time is 'created time';
              COMMENT ON COLUMN dispute.updated_time is 'updated time';
              COMMENT ON COLUMN dispute.extra is 'extra config, json';
              COMMENT ON COLUMN dispute.status is 'dispute状态';
              COMMENT ON COLUMN dispute.payment_id is '关联的支付单据id';
              COMMENT ON COLUMN dispute.channel_payment_id is '渠道支付id';
              COMMENT ON COLUMN dispute.payer_type is '买家类型';
              COMMENT ON COLUMN dispute.payer_id is '买家id';
              COMMENT ON COLUMN dispute.payee_type is '卖家类型';
              COMMENT ON COLUMN dispute.payee_id is '卖家id';
              COMMENT ON COLUMN dispute.channel_type is '渠道类型';
              COMMENT ON COLUMN dispute.channel_dispute_id is '渠道dispute id';
              COMMENT ON COLUMN dispute.channel_charge_id is '渠道charge id,收取dispute 金额';
              COMMENT ON COLUMN dispute.channel_transfer_id is '渠道转账id,商家赢了转账的单据';
              COMMENT ON COLUMN dispute.currency is '币种';
              COMMENT ON COLUMN dispute.amount is 'dispute金额';
              COMMENT ON COLUMN dispute.dispute_fee is '额外的dispute fee';
              COMMENT ON COLUMN dispute.reason is 'dispute原因';

              -- payout summary
              CREATE TABLE moego_payment.public.payout (
                id BIGSERIAL NOT NULL,
                created_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                updated_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                extra text NOT NULL DEFAULT '',
                payout_arrive_time timestamp,
                channel_type text NOT NULL DEFAULT '',
                channel_payout_id text NOT NULL DEFAULT '',
                channel_account_id text NOT NULL DEFAULT '',
                entity_type text NOT NULL DEFAULT '',
                entity_id BIGINT NOT NULL DEFAULT 0,
                currency CHAR(3) NOT NULL,
                amount BIGINT NOT NULL DEFAULT 0,
                payment_amount BIGINT NOT NULL DEFAULT 0,
                refunded_amount BIGINT NOT NULL DEFAULT 0,
                fees BIGINT NOT NULL DEFAULT 0,
                payout_trigger_source text NOT NULL DEFAULT '',
                mode text NOT NULL DEFAULT '',
                status text NOT NULL DEFAULT '',
                destination_info text NOT NULL DEFAULT '',
                destination_type text NOT NULL DEFAULT '',
                statement_description text NOT NULL DEFAULT '',
                channel_failure_code text NOT NULL DEFAULT '',
                channel_failure_msg text NOT NULL DEFAULT '',

                CONSTRAINT payout_pk PRIMARY KEY (id)
              );

              CREATE UNIQUE INDEX uniq_channel_payout ON public.payout (channel_payout_id,channel_type);
              CREATE INDEX idx_payout_entity ON public.payout (entity_id,entity_type);
              COMMENT ON TABLE payout IS 'dispute';
              COMMENT ON COLUMN payout.id is 'primary key';
              COMMENT ON COLUMN payout.created_time is 'created time';
              COMMENT ON COLUMN payout.updated_time is 'updated time';
              COMMENT ON COLUMN payout.extra is 'extra config, json';
              COMMENT ON COLUMN payout.payout_arrive_time is 'payout到账时间';
              COMMENT ON COLUMN payout.channel_type is '渠道类型';
              COMMENT ON COLUMN payout.channel_payout_id is '渠道payout id';
              COMMENT ON COLUMN payout.channel_account_id is '渠道账户id';
              COMMENT ON COLUMN payout.entity_type is '实体类型';
              COMMENT ON COLUMN payout.entity_id is '实体id,如moego business id';
              COMMENT ON COLUMN payout.currency is '币种';
              COMMENT ON COLUMN payout.amount is 'payout 总金额';
              COMMENT ON COLUMN payout.payment_amount is '支付总金额';
              COMMENT ON COLUMN payout.refunded_amount is '退款总金额';
              COMMENT ON COLUMN payout.fees is '手续费总和';
              COMMENT ON COLUMN payout.payout_trigger_source is 'payout触发源头, moego / channel';
              COMMENT ON COLUMN payout.mode is '模式, instant / standard';
              COMMENT ON COLUMN payout.status is 'payout 状态';
              COMMENT ON COLUMN payout.destination_info is '收钱方银行账户/卡';
              COMMENT ON COLUMN payout.destination_type is '收钱方类型';
              COMMENT ON COLUMN payout.statement_description is '银行展示信息';
              COMMENT ON COLUMN payout.channel_failure_code is '三方返回的失败code';
              COMMENT ON COLUMN payout.channel_failure_msg is '三方返回的失败信息';

              -- payment setting
              CREATE TABLE moego_payment.public.payment_setting (
                id BIGSERIAL NOT NULL,
                created_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                updated_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                channel_type text NOT NULL DEFAULT '', 
                entity_type text NOT NULL DEFAULT '',
                entity_id BIGINT NOT NULL DEFAULT 0,
                processing_fee_paid_by text NOT NULL DEFAULT '',
                processing_fee_signature text NOT NULL DEFAULT '',
                processing_fee_sign_time timestamp,
                online_fee_rate  NUMERIC(20,4) NOT NULL DEFAULT 0,
                online_fee_cents BIGINT NOT NULL DEFAULT 0,
                reader_fee_rate NUMERIC(20,4) NOT NULL DEFAULT 0,
                reader_fee_cents BIGINT NOT NULL DEFAULT 0,
                customized_fee_name text NOT NULL DEFAULT '',
                auto_cancel_fee_by_client BOOLEAN DEFAULT FALSE,
                card_auth_enable BOOLEAN DEFAULT FALSE,
                pre_auth_bspd BIGINT NOT NULL DEFAULT 0,

                CONSTRAINT payment_setting_pk PRIMARY KEY (id)
              );

              CREATE UNIQUE INDEX uniq_payment_setting_entity ON public.payment_setting (entity_id,entity_type,channel_type);
              COMMENT ON TABLE payment_setting IS 'dispute';
              COMMENT ON COLUMN payment_setting.id is 'primary key';
              COMMENT ON COLUMN payment_setting.created_time is 'created time';
              COMMENT ON COLUMN payment_setting.updated_time is 'updated time';
              COMMENT ON COLUMN payment_setting.channel_type is '渠道类型';
              COMMENT ON COLUMN payment_setting.entity_type is '实体类型';
              COMMENT ON COLUMN payment_setting.entity_id is '实体id';
              COMMENT ON COLUMN payment_setting.processing_fee_paid_by is 'fee by c or b';
              COMMENT ON COLUMN payment_setting.processing_fee_signature is 'fee by c 时商家的签名';
              COMMENT ON COLUMN payment_setting.processing_fee_sign_time is 'fee by c 时商家的签名时间';
              COMMENT ON COLUMN payment_setting.online_fee_rate is '线上支付费率';
              COMMENT ON COLUMN payment_setting.online_fee_cents is '线上支付额外加的金额';
              COMMENT ON COLUMN payment_setting.reader_fee_rate is 'reader支付费率';
              COMMENT ON COLUMN payment_setting.reader_fee_cents is 'reader支付额外加的金额';
              COMMENT ON COLUMN payment_setting.customized_fee_name is '自定义fee的名称';
              COMMENT ON COLUMN payment_setting.auto_cancel_fee_by_client is '是否借记卡取消CVF';
              COMMENT ON COLUMN payment_setting.card_auth_enable is '是否开启卡的预授权';
              COMMENT ON COLUMN payment_setting.pre_auth_bspd is '提前发起 pre auth 的时间，单位秒';

              -- tip setting
              CREATE TABLE moego_payment.public.tip_setting (
                id BIGSERIAL NOT NULL,
                created_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                updated_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                extra text NOT NULL DEFAULT '',
                entity_type text NOT NULL DEFAULT '',
                entity_id BIGINT NOT NULL DEFAULT 0,
                preferred_tip text NOT NULL DEFAULT '',
                tip_type text NOT NULL DEFAULT '',
                tip_config text NOT NULL DEFAULT '',
                smart_tip_enable BOOLEAN DEFAULT FALSE,
                smart_tip_threshold BIGINT NOT NULL DEFAULT 0,
                smart_tip_type text NOT NULL DEFAULT '',
                smart_tip_config text NOT NULL DEFAULT '',
                smart_tip_preferred_tip text NOT NULL DEFAULT '',

                CONSTRAINT tip_setting_pk PRIMARY KEY (id)
              );

              CREATE UNIQUE INDEX uniq_entity_tip_setting ON public.tip_setting (entity_id,entity_type);
              COMMENT ON TABLE tip_setting IS 'tip setting';
              COMMENT ON COLUMN tip_setting.id is 'primary key';
              COMMENT ON COLUMN tip_setting.created_time is 'created time';
              COMMENT ON COLUMN tip_setting.updated_time is 'updated time';
              COMMENT ON COLUMN tip_setting.extra is 'extra config, json';
              COMMENT ON COLUMN tip_setting.entity_type is '实体类型';
              COMMENT ON COLUMN tip_setting.entity_id is '实体id';
              COMMENT ON COLUMN tip_setting.preferred_tip is '默认选择的tips';
              COMMENT ON COLUMN tip_setting.tip_type is 'tip类型,百分比/固定金额';
              COMMENT ON COLUMN tip_setting.tip_config is 'tips的配置,json';
              COMMENT ON COLUMN tip_setting.smart_tip_enable is '是否开启smart tip';
              COMMENT ON COLUMN tip_setting.smart_tip_threshold is 'smart tip的金额阈值';
              COMMENT ON COLUMN tip_setting.smart_tip_type is 'smart tip的计算类型';
              COMMENT ON COLUMN tip_setting.smart_tip_config is 'smart tip的配置';
              COMMENT ON COLUMN tip_setting.smart_tip_preferred_tip is 'smart tip的默认选择';

              -- tap to pay setting
              CREATE TABLE moego_payment.public.tap_to_pay_setting (
                id BIGSERIAL NOT NULL,
                created_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                updated_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                extra text NOT NULL DEFAULT '',
                channel_type text NOT NULL DEFAULT '',
                entity_type text NOT NULL DEFAULT '',
                entity_id BIGINT NOT NULL DEFAULT 0,
                ttp_linked_enable BOOLEAN DEFAULT FALSE,
                ttp_enable BOOLEAN DEFAULT FALSE,
                device_type text NOT NULL DEFAULT '',
                extra_fee BIGINT NOT NULL DEFAULT 0,
                discount_times INTEGER NOT NULL DEFAULT 0,
                discount_start_time timestamp,
                discount_end_time timestamp,
                discount_count_per_month INTEGER NOT NULL DEFAULT 0,

                CONSTRAINT tap_to_pay_setting_pk PRIMARY KEY (id)
              );

              CREATE UNIQUE INDEX uniq_entity_ttp_setting ON public.tap_to_pay_setting (entity_id,entity_type,channel_type);
              COMMENT ON TABLE tap_to_pay_setting IS 'tip setting';
              COMMENT ON COLUMN tap_to_pay_setting.id is 'primary key';
              COMMENT ON COLUMN tap_to_pay_setting.created_time is 'created time';
              COMMENT ON COLUMN tap_to_pay_setting.updated_time is 'updated time';
              COMMENT ON COLUMN tap_to_pay_setting.extra is 'extra config, json';
              COMMENT ON COLUMN tap_to_pay_setting.channel_type is '渠道类型';
              COMMENT ON COLUMN tap_to_pay_setting.entity_type is '实体类型';
              COMMENT ON COLUMN tap_to_pay_setting.entity_id is '实体id';
              COMMENT ON COLUMN tap_to_pay_setting.ttp_linked_enable is 'ttp是否connect apple id';
              COMMENT ON COLUMN tap_to_pay_setting.ttp_enable is '是否开通ttp';
              COMMENT ON COLUMN tap_to_pay_setting.device_type is '设备类型';
              COMMENT ON COLUMN tap_to_pay_setting.extra_fee is '额外费用';
              COMMENT ON COLUMN tap_to_pay_setting.discount_times is '优惠次数';
              COMMENT ON COLUMN tap_to_pay_setting.discount_start_time is '优惠开始时间';
              COMMENT ON COLUMN tap_to_pay_setting.discount_end_time is '优惠结束时间';
              COMMENT ON COLUMN tap_to_pay_setting.discount_count_per_month is '每月折扣数';

              -- 状态流转流水表
              CREATE TABLE moego_payment.public.status_flow_record (
                id BIGSERIAL NOT NULL,
                created_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                updated_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                source_record_id text NOT NULL DEFAULT '',
                source_record_type text NOT NULL DEFAULT '',
                source_status text NOT NULL DEFAULT '',
                target_status text NOT NULL DEFAULT '',

                CONSTRAINT status_flow_record_pk PRIMARY KEY (id)
              );

              CREATE INDEX idx_source_record ON public.status_flow_record (source_record_id,source_record_type);

              COMMENT ON TABLE status_flow_record IS 'status flow record';
              COMMENT ON COLUMN status_flow_record.id is 'primary key';
              COMMENT ON COLUMN status_flow_record.created_time is 'create time';
              COMMENT ON COLUMN status_flow_record.updated_time is 'update time';
              COMMENT ON COLUMN status_flow_record.source_record_id is '单据id';
              COMMENT ON COLUMN status_flow_record.source_record_type is '单据类型';
              COMMENT ON COLUMN status_flow_record.source_status is 'source status';
              COMMENT ON COLUMN status_flow_record.target_status is 'target status';

              -- message delivery
              CREATE TABLE moego_payment.public.message_delivery (
                  id BIGSERIAL NOT NULL,
                  created_time TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC') NOT NULL,
                  updated_time TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC') NOT NULL,
                  message_type text NOT NULL DEFAULT '',
                  reference_id text NOT NULL DEFAULT '',
                  payload TEXT NOT NULL DEFAULT '',
                  status text NOT NULL DEFAULT '',
                  retry_times INT DEFAULT 0,

                  CONSTRAINT message_delivery_pk PRIMARY KEY (id)
              );

              CREATE INDEX idx_message_delivery_status ON public.message_delivery (status);
              CREATE INDEX idx_message_reference ON public.message_delivery (reference_id, message_type);

              COMMENT ON TABLE message_delivery IS 'order tips split detail';
              COMMENT ON COLUMN message_delivery.id is 'primary key';
              COMMENT ON COLUMN message_delivery.created_time is 'Creation time';
              COMMENT ON COLUMN message_delivery.updated_time is 'Last update time';
              COMMENT ON COLUMN message_delivery.message_type is 'Type of message (e.g., order, refund, etc.)';
              COMMENT ON COLUMN message_delivery.reference_id is 'Foreign key to the relevant entity (e.g., order ID, refund ID)';
              COMMENT ON COLUMN message_delivery.payload is 'Actual message content stored as JSON';
              COMMENT ON COLUMN message_delivery.status is 'Status of the message (PENDING, SENT, FAILED, etc.)';
              COMMENT ON COLUMN message_delivery.retry_times is 'Number of retry attempts';

              -- split sync record
              CREATE TABLE moego_payment.public.split_sync_record (
                  id BIGSERIAL NOT NULL,
                  created_time TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC') NOT NULL,
                  updated_time TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC') NOT NULL,
                  entity_type text NOT NULL DEFAULT '',
                  entity_id BIGINT NOT NULL DEFAULT 0,
                  is_reverse BOOLEAN DEFAULT FALSE,
                  status text NOT NULL DEFAULT '',
                  retry_times INT DEFAULT 0,
                  transfer_group text NOT NULL DEFAULT '',
                  sync_mode text NOT NULL DEFAULT '',
                  sync_channel text NOT NULL DEFAULT '',

                  CONSTRAINT split_sync_record_pk PRIMARY KEY (id)
              );

              CREATE UNIQUE INDEX uniq_split_sync_entity ON public.split_sync_record (entity_id,entity_type);

              COMMENT ON TABLE split_sync_record IS 'order tips split detail';
              COMMENT ON COLUMN split_sync_record.id is 'primary key';
              COMMENT ON COLUMN split_sync_record.created_time is 'Creation time';
              COMMENT ON COLUMN split_sync_record.updated_time is 'Last update time';
              COMMENT ON COLUMN split_sync_record.entity_type is '实体类型';
              COMMENT ON COLUMN split_sync_record.entity_id is '实体id';
              COMMENT ON COLUMN split_sync_record.is_reverse is '是否逆向';
              COMMENT ON COLUMN split_sync_record.status is '同步状态';
              COMMENT ON COLUMN split_sync_record.retry_times is '重试次数';
              COMMENT ON COLUMN split_sync_record.transfer_group is '转账分组';
              COMMENT ON COLUMN split_sync_record.sync_mode is '同步模式';
              COMMENT ON COLUMN split_sync_record.sync_channel is '同步的渠道';
