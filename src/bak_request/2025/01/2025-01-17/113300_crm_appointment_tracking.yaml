name: "pg moego_appointment new table"
description: "pg moego_appointment new table"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_appointment"
            username: "${db.user.admin}"
            sql: |
              drop table if exists appointment_tracking;
              drop table if exists appointment_tracking_notification_log;
        - execute_sql:
            database: "moego_appointment"
            sql: |
              CREATE TABLE IF NOT EXISTS "public"."appointment_tracking" (
                  "id" bigserial,
                  "company_id" int8 NOT NULL DEFAULT 0,
                  "appointment_id" int8 NOT NULL DEFAULT 0,
                  "staff_location_status" int4 NOT NULL DEFAULT 1,
                  "location_sharing_staff_id" int8 NOT NULL DEFAULT 0,
                  "location_sharing_device_id" text NOT NULL DEFAULT ''::text,
                  "last_in_transit_at" int8 NOT NULL DEFAULT 0,
                  "estimated_travel_seconds" int8 NOT NULL DEFAULT 0,
                  "last_estimate_at" int8 NOT NULL DEFAULT 0,
                  "travel_distance" int8 NOT NULL DEFAULT 0,
                  "delayed_status" int4 NOT NULL DEFAULT 1,
                  "estimated_travel_seconds_from_last_in_transit" int8 NOT NULL DEFAULT 0,
                  "from_last_in_transit_last_estimate_at" int8 NOT NULL DEFAULT 0,
                  "last_in_transit_appointment_id" int8 NOT NULL DEFAULT 0,
                  "travel_distance_from_last_in_transit" int8 NOT NULL DEFAULT 0,
                  "delayed_status_from_last_in_transit" int4 NOT NULL DEFAULT 1,
                  "customer_address" jsonb ,
                  "staff_address" jsonb ,
                  "created_at" timestamp NOT NULL DEFAULT now(),
                  "updated_at" timestamp NOT NULL DEFAULT now(),
                  PRIMARY KEY ("id"),
                  CONSTRAINT "uniq_appointment_tracking_appt_id" UNIQUE ("appointment_id")
              );
              CREATE INDEX "idx_tracking_staff_id" ON "public"."appointment_tracking" USING btree (
                "location_sharing_staff_id","last_in_transit_at"
                );
              CREATE INDEX "idx_tracking_company_id_appointment_id" ON "public"."appointment_tracking" USING btree (
                "company_id","appointment_id"
                );
              CREATE TRIGGER updated_at
                BEFORE UPDATE
                ON appointment_tracking
                FOR EACH ROW
                EXECUTE PROCEDURE updated_at();
              COMMENT ON COLUMN "public"."appointment_tracking"."customer_address" IS 'json str,see api/moego/models/appointment/v1/appointment_tracking.proto.Address';
              COMMENT ON COLUMN "public"."appointment_tracking"."staff_address" IS 'json str,see api/moego/models/appointment/v1/appointment_tracking.proto.Address';
              COMMENT ON COLUMN "public"."appointment_tracking"."location_sharing_staff_id" IS 'staff who share location, 0 means no staff share location';
              COMMENT ON COLUMN "public"."appointment_tracking"."staff_location_status" IS 'staff location status,see api/moego/models/appointment/v1/appointment_tracking.proto.StaffLocationStatus';
              COMMENT ON COLUMN "public"."appointment_tracking"."estimated_travel_seconds" IS 'from staff current location to customer location';
              COMMENT ON COLUMN "public"."appointment_tracking"."last_estimate_at" IS 'last time we estimate the estimated_travel_seconds';
              COMMENT ON COLUMN "public"."appointment_tracking"."travel_distance" IS 'driving distance in meters from staff current location to customer location';
              COMMENT ON COLUMN "public"."appointment_tracking"."delayed_status" IS 'delayed status from staff current location to customer location see api/moego/models/appointment/v1/appointment_tracking.proto.DelayedStatus';
              COMMENT ON COLUMN "public"."appointment_tracking"."estimated_travel_seconds_from_last_in_transit" IS 'from staff last in transit location to customer location';
              COMMENT ON COLUMN "public"."appointment_tracking"."from_last_in_transit_last_estimate_at" IS 'last time we estimate the estimated_travel_seconds_from_last_in_transit';
              COMMENT ON COLUMN "public"."appointment_tracking"."last_in_transit_appointment_id" IS 'staff last in transit appointment id of today';
              COMMENT ON COLUMN "public"."appointment_tracking"."travel_distance_from_last_in_transit" IS 'driving distance in meters from staff last in transit location to customer location';
              COMMENT ON COLUMN "public"."appointment_tracking"."delayed_status_from_last_in_transit" IS 'delayed status from staff last in transit location to customer location see api/moego/models/appointment/v1/appointment_tracking.proto.DelayedStatus';
        - execute_sql:
            database: "moego_appointment"
            sql: |
              CREATE TABLE IF NOT EXISTS "public"."appointment_tracking_notification_log" (
                  "id" bigserial,
                  "company_id" int8 NOT NULL DEFAULT 0,
                  "appointment_id" int8 NOT NULL DEFAULT 0,
                  "notification_type" int4 NOT NULL DEFAULT 0,
                  "notification_id" int8 NOT NULL DEFAULT 0,
                  "channel" int4 NOT NULL DEFAULT 0,
                  "created_at" timestamp NOT NULL DEFAULT now(),
                  "updated_at" timestamp NOT NULL DEFAULT now(),
                  "deleted_at" timestamp,
                  PRIMARY KEY ("id")
              );
              CREATE INDEX "idx_tracking_log_appointment_id" ON "public"."appointment_tracking_notification_log" USING btree (
                "appointment_id"
                );
              CREATE INDEX "idx_tracking_log_company_id_appointment_id" ON "public"."appointment_tracking_notification_log" USING btree (
                "company_id","appointment_id"
                );
              CREATE TRIGGER updated_at
                BEFORE UPDATE
                ON appointment_tracking_notification_log
                FOR EACH ROW
                EXECUTE PROCEDURE updated_at();
              COMMENT ON COLUMN "public"."appointment_tracking_notification_log"."notification_type" IS 'see api/moego/models/notification/v1/notification_enums.proto.NotificationType';
              COMMENT ON COLUMN "public"."appointment_tracking_notification_log"."channel" IS '1-notification 2-sms';
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_appointment"
            username: "${db.user.admin}"
            sql: |
              drop table if exists appointment_tracking;
              drop table if exists appointment_tracking_notification_log;
        - execute_sql:
            database: "moego_appointment"
            sql: |
              CREATE TABLE IF NOT EXISTS "public"."appointment_tracking" (
                  "id" bigserial,
                  "company_id" int8 NOT NULL DEFAULT 0,
                  "appointment_id" int8 NOT NULL DEFAULT 0,
                  "staff_location_status" int4 NOT NULL DEFAULT 1,
                  "location_sharing_staff_id" int8 NOT NULL DEFAULT 0,
                  "location_sharing_device_id" text NOT NULL DEFAULT ''::text,
                  "last_in_transit_at" int8 NOT NULL DEFAULT 0,
                  "estimated_travel_seconds" int8 NOT NULL DEFAULT 0,
                  "last_estimate_at" int8 NOT NULL DEFAULT 0,
                  "travel_distance" int8 NOT NULL DEFAULT 0,
                  "delayed_status" int4 NOT NULL DEFAULT 1,
                  "estimated_travel_seconds_from_last_in_transit" int8 NOT NULL DEFAULT 0,
                  "from_last_in_transit_last_estimate_at" int8 NOT NULL DEFAULT 0,
                  "last_in_transit_appointment_id" int8 NOT NULL DEFAULT 0,
                  "travel_distance_from_last_in_transit" int8 NOT NULL DEFAULT 0,
                  "delayed_status_from_last_in_transit" int4 NOT NULL DEFAULT 1,
                  "customer_address" jsonb ,
                  "staff_address" jsonb ,
                  "created_at" timestamp NOT NULL DEFAULT now(),
                  "updated_at" timestamp NOT NULL DEFAULT now(),
                  PRIMARY KEY ("id"),
                  CONSTRAINT "uniq_appointment_tracking_appt_id" UNIQUE ("appointment_id")
              );
              CREATE INDEX "idx_tracking_staff_id" ON "public"."appointment_tracking" USING btree (
                "location_sharing_staff_id","last_in_transit_at"
                );
              CREATE INDEX "idx_tracking_company_id_appointment_id" ON "public"."appointment_tracking" USING btree (
                "company_id","appointment_id"
                );
              CREATE TRIGGER updated_at
                BEFORE UPDATE
                ON appointment_tracking
                FOR EACH ROW
                EXECUTE PROCEDURE updated_at();
              COMMENT ON COLUMN "public"."appointment_tracking"."customer_address" IS 'json str,see api/moego/models/appointment/v1/appointment_tracking.proto.Address';
              COMMENT ON COLUMN "public"."appointment_tracking"."staff_address" IS 'json str,see api/moego/models/appointment/v1/appointment_tracking.proto.Address';
              COMMENT ON COLUMN "public"."appointment_tracking"."location_sharing_staff_id" IS 'staff who share location, 0 means no staff share location';
              COMMENT ON COLUMN "public"."appointment_tracking"."staff_location_status" IS 'staff location status,see api/moego/models/appointment/v1/appointment_tracking.proto.StaffLocationStatus';
              COMMENT ON COLUMN "public"."appointment_tracking"."estimated_travel_seconds" IS 'from staff current location to customer location';
              COMMENT ON COLUMN "public"."appointment_tracking"."last_estimate_at" IS 'last time we estimate the estimated_travel_seconds';
              COMMENT ON COLUMN "public"."appointment_tracking"."travel_distance" IS 'driving distance in meters from staff current location to customer location';
              COMMENT ON COLUMN "public"."appointment_tracking"."delayed_status" IS 'delayed status from staff current location to customer location see api/moego/models/appointment/v1/appointment_tracking.proto.DelayedStatus';
              COMMENT ON COLUMN "public"."appointment_tracking"."estimated_travel_seconds_from_last_in_transit" IS 'from staff last in transit location to customer location';
              COMMENT ON COLUMN "public"."appointment_tracking"."from_last_in_transit_last_estimate_at" IS 'last time we estimate the estimated_travel_seconds_from_last_in_transit';
              COMMENT ON COLUMN "public"."appointment_tracking"."last_in_transit_appointment_id" IS 'staff last in transit appointment id of today';
              COMMENT ON COLUMN "public"."appointment_tracking"."travel_distance_from_last_in_transit" IS 'driving distance in meters from staff last in transit location to customer location';
              COMMENT ON COLUMN "public"."appointment_tracking"."delayed_status_from_last_in_transit" IS 'delayed status from staff last in transit location to customer location see api/moego/models/appointment/v1/appointment_tracking.proto.DelayedStatus';
        - execute_sql:
            database: "moego_appointment"
            sql: |
              CREATE TABLE IF NOT EXISTS "public"."appointment_tracking_notification_log" (
                  "id" bigserial,
                  "company_id" int8 NOT NULL DEFAULT 0,
                  "appointment_id" int8 NOT NULL DEFAULT 0,
                  "notification_type" int4 NOT NULL DEFAULT 0,
                  "notification_id" int8 NOT NULL DEFAULT 0,
                  "channel" int4 NOT NULL DEFAULT 0,
                  "created_at" timestamp NOT NULL DEFAULT now(),
                  "updated_at" timestamp NOT NULL DEFAULT now(),
                  "deleted_at" timestamp,
                  PRIMARY KEY ("id")
              );
              CREATE INDEX "idx_tracking_log_appointment_id" ON "public"."appointment_tracking_notification_log" USING btree (
                "appointment_id"
                );
              CREATE INDEX "idx_tacking_log_company_id_appointment_id" ON "public"."appointment_tracking_notification_log" USING btree (
                "company_id","appointment_id"
                );
              CREATE TRIGGER updated_at
                BEFORE UPDATE
                ON appointment_tracking_notification_log
                FOR EACH ROW
                EXECUTE PROCEDURE updated_at();
              COMMENT ON COLUMN "public"."appointment_tracking_notification_log"."notification_type" IS 'see api/moego/models/notification/v1/notification_enums.proto.NotificationType';
              COMMENT ON COLUMN "public"."appointment_tracking_notification_log"."channel" IS '1-notification 2-sms';