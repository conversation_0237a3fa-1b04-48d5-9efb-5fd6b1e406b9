name: "Add payment_status column to booking_request"
description: "Add payment_status column to booking_request"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: "AWS_RDS"
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_online_booking"
            sql: |
              alter table public.booking_request
                add payment_status smallint default 0 not null;

              comment on column public.booking_request.payment_status is 'payment status, see BookingRequestModel.PaymentStatus';

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: "AWS_RDS"
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_online_booking"
            sql: |
              alter table public.booking_request
                add payment_status smallint default 0 not null;

              comment on column public.booking_request.payment_status is 'payment status, see BookingRequestModel.PaymentStatus';
