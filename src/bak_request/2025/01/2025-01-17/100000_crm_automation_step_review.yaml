name: "pg moego_automation step add preview data"
description: "pg moego_automation step add preview data"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_automation"
            sql: |
              ALTER TABLE step ADD COLUMN preview_data text default '{}';