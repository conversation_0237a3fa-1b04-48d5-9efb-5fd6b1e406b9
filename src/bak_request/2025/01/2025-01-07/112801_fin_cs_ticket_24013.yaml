name: "修改用户支付数据"
description: "修改用户支付数据"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              UPDATE public.order 
              SET status = 2, 
              total_amount = total_amount - 3, 
              extra_fee_amount = 0.0, 
              remain_amount = 0.0, 
              update_time = NOW() 
              WHERE id = '133061448' AND business_id = '116393';
              
              INSERT INTO public.order_line_extra_fee(business_id, order_id, order_item_id, apply_type, is_deleted, fee_type, amount, collect_type, description, apply_by, create_time, update_time) VALUES (116393, 133061448, 0, 'none', false, 'convenience fee', 3.00, 'subtract', '', 0,NOW(), NOW());