name: "添加 forward 配置表"
description: "添加 forward 配置表"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_customer"
            sql: |
              alter table moe_customer.moe_customer_contact
              add column e164_phone_number varchar(20) not null default '';
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_customer"
            sql: |
              alter table moe_customer.moe_customer_contact
              add column e164_phone_number varchar(20) not null default '';
