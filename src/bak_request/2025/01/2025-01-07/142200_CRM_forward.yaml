name: "添加 forward 配置表"
description: "添加 forward 配置表"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_engagement"
            sql: |
              create table forward_business
              (
                  id              serial
                      primary key,
                  to_phone_number varchar(20) default ''::character varying not null
                      constraint unique_pn
                          unique,
                  business_id     bigint      default 0                     not null,
                  company_id      bigint      default 0                     not null
              );
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_engagement"
            sql: |
              create table forward_business
              (
                  id              serial
                      primary key,
                  to_phone_number varchar(20) default ''::character varying not null
                      constraint unique_pn
                          unique,
                  business_id     bigint      default 0                     not null,
                  company_id      bigint      default 0                     not null
              );
