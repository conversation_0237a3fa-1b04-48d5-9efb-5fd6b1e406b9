name: "Add attr column to booking_request"
description: "Add attr column to booking_request"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: "AWS_RDS"
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_online_booking"
            sql: |
              alter table public.booking_request
                add attr jsonb default jsonb_object('{}') not null;
  
              comment on column public.booking_request.attr is '存储一些通用信息，结构为 BookingRequestModel.Attr';
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: "AWS_RDS"
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_online_booking"
            sql: |
              alter table public.booking_request
                add attr jsonb default jsonb_object('{}') not null;
              
              comment on column public.booking_request.attr is '存储一些通用信息，结构为 BookingRequestModel.Attr';
