name: "Fix wrong Tap-to-Pay discount"
description: "Fix wrong Tap-to-Pay discount"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              UPDATE moe_payment.moe_tap_to_pay_setting
                SET discount_count_per_month = 0, update_time = NOW()
                WHERE discount_times = 0 AND discount_count_per_month > 0;
