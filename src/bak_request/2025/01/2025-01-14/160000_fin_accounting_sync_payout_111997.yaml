name: "compensate payout for business 111997"
description: "compensate payout for business 111997"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_accounting"
            sql: |
              INSERT INTO public.accounting_sync_retry_log (created_time, retry_entity_type, retry_entity_id, channel_type) VALUES(now(), 'PAYOUT', 'po_1QULZUIARuRMeaUaQELTvAQ4', 'LAYER');
              INSERT INTO public.accounting_sync_retry_log (created_time, retry_entity_type, retry_entity_id, channel_type) VALUES(now(), 'PAYOUT', 'po_1QW9wLIARuRMeaUaJhzoIYKf', 'LAYER');
              INSERT INTO public.accounting_sync_retry_log (created_time, retry_entity_type, retry_entity_id, channel_type) VALUES(now(), 'PAYOUT', 'po_1QTz5YIARuRMeaUavVdMXVUF', 'LAYER');
              INSERT INTO public.accounting_sync_retry_log (created_time, retry_entity_type, retry_entity_id, channel_type) VALUES(now(), 'PAYOUT', 'po_1QXFOPIARuRMeaUadKY0SLt1', 'LAYER');
              INSERT INTO public.accounting_sync_retry_log (created_time, retry_entity_type, retry_entity_id, channel_type) VALUES(now(), 'PAYOUT', 'po_1QYhHbIARuRMeaUadEoPzLc7', 'LAYER');
              INSERT INTO public.accounting_sync_retry_log (created_time, retry_entity_type, retry_entity_id, channel_type) VALUES(now(), 'PAYOUT', 'po_1QZ3mdIARuRMeaUaQAe6gRR1', 'LAYER');
              INSERT INTO public.accounting_sync_retry_log (created_time, retry_entity_type, retry_entity_id, channel_type) VALUES(now(), 'PAYOUT', 'po_1QbEc9IARuRMeaUatT4x6pAd', 'LAYER');
              INSERT INTO public.accounting_sync_retry_log (created_time, retry_entity_type, retry_entity_id, channel_type) VALUES(now(), 'PAYOUT', 'po_1QUi3GIARuRMeaUaqxxcwI6J', 'LAYER');
              INSERT INTO public.accounting_sync_retry_log (created_time, retry_entity_type, retry_entity_id, channel_type) VALUES(now(), 'PAYOUT', 'po_1QWWQhIARuRMeaUaTLLP22Fy', 'LAYER');
              INSERT INTO public.accounting_sync_retry_log (created_time, retry_entity_type, retry_entity_id, channel_type) VALUES(now(), 'PAYOUT', 'po_1Qa9C2IARuRMeaUaKVY7dS5K', 'LAYER');
              INSERT INTO public.accounting_sync_retry_log (created_time, retry_entity_type, retry_entity_id, channel_type) VALUES(now(), 'PAYOUT', 'po_1QXbqnIARuRMeaUaq7oe3neC', 'LAYER');
              
              INSERT INTO public.accounting_sync_log (created_time, updated_time, sync_entity_type, sync_entity_id, sync_entity_status, channel_type) VALUES(now(), now(), 'PAYOUT', 'po_1QULZUIARuRMeaUaQELTvAQ4', 'PROCESSING', 'LAYER');
              INSERT INTO public.accounting_sync_log (created_time, updated_time, sync_entity_type, sync_entity_id, sync_entity_status, channel_type) VALUES(now(), now(), 'PAYOUT', 'po_1QW9wLIARuRMeaUaJhzoIYKf', 'PROCESSING', 'LAYER');
              INSERT INTO public.accounting_sync_log (created_time, updated_time, sync_entity_type, sync_entity_id, sync_entity_status, channel_type) VALUES(now(), now(), 'PAYOUT', 'po_1QTz5YIARuRMeaUavVdMXVUF', 'PROCESSING', 'LAYER');
              INSERT INTO public.accounting_sync_log (created_time, updated_time, sync_entity_type, sync_entity_id, sync_entity_status, channel_type) VALUES(now(), now(), 'PAYOUT', 'po_1QXFOPIARuRMeaUadKY0SLt1', 'PROCESSING', 'LAYER');
              INSERT INTO public.accounting_sync_log (created_time, updated_time, sync_entity_type, sync_entity_id, sync_entity_status, channel_type) VALUES(now(), now(), 'PAYOUT', 'po_1QYhHbIARuRMeaUadEoPzLc7', 'PROCESSING', 'LAYER');
              INSERT INTO public.accounting_sync_log (created_time, updated_time, sync_entity_type, sync_entity_id, sync_entity_status, channel_type) VALUES(now(), now(), 'PAYOUT', 'po_1QZ3mdIARuRMeaUaQAe6gRR1', 'PROCESSING', 'LAYER');
              INSERT INTO public.accounting_sync_log (created_time, updated_time, sync_entity_type, sync_entity_id, sync_entity_status, channel_type) VALUES(now(), now(), 'PAYOUT', 'po_1QbEc9IARuRMeaUatT4x6pAd', 'PROCESSING', 'LAYER');
              INSERT INTO public.accounting_sync_log (created_time, updated_time, sync_entity_type, sync_entity_id, sync_entity_status, channel_type) VALUES(now(), now(), 'PAYOUT', 'po_1QUi3GIARuRMeaUaqxxcwI6J', 'PROCESSING', 'LAYER');
              INSERT INTO public.accounting_sync_log (created_time, updated_time, sync_entity_type, sync_entity_id, sync_entity_status, channel_type) VALUES(now(), now(), 'PAYOUT', 'po_1QWWQhIARuRMeaUaTLLP22Fy', 'PROCESSING', 'LAYER');
              INSERT INTO public.accounting_sync_log (created_time, updated_time, sync_entity_type, sync_entity_id, sync_entity_status, channel_type) VALUES(now(), now(), 'PAYOUT', 'po_1Qa9C2IARuRMeaUaKVY7dS5K', 'PROCESSING', 'LAYER');
              INSERT INTO public.accounting_sync_log (created_time, updated_time, sync_entity_type, sync_entity_id, sync_entity_status, channel_type) VALUES(now(), now(), 'PAYOUT', 'po_1QXbqnIARuRMeaUaq7oe3neC', 'PROCESSING', 'LAYER');
              
              INSERT INTO public.biz_payout (created_time, updated_time, sync_status, "snapshot", extra, company_id, business_id, payout_id, channel_payout_id, payout_channel_type, channel_type, channel_journal_entry_id) VALUES(now(), now(), 'PROCESSING', '{"id":"po_1QULZUIARuRMeaUaQELTvAQ4","object":"payout"}', '', 111648, 111997, 'po_1QULZUIARuRMeaUaQELTvAQ4', '', '', 'LAYER', '');
              INSERT INTO public.biz_payout (created_time, updated_time, sync_status, "snapshot", extra, company_id, business_id, payout_id, channel_payout_id, payout_channel_type, channel_type, channel_journal_entry_id) VALUES(now(), now(), 'PROCESSING', '{"id":"po_1QW9wLIARuRMeaUaJhzoIYKf","object":"payout"}', '', 111648, 111997, 'po_1QW9wLIARuRMeaUaJhzoIYKf', '', '', 'LAYER', '');
              INSERT INTO public.biz_payout (created_time, updated_time, sync_status, "snapshot", extra, company_id, business_id, payout_id, channel_payout_id, payout_channel_type, channel_type, channel_journal_entry_id) VALUES(now(), now(), 'PROCESSING', '{"id":"po_1QTz5YIARuRMeaUavVdMXVUF","object":"payout"}', '', 111648, 111997, 'po_1QTz5YIARuRMeaUavVdMXVUF', '', '', 'LAYER', '');
              INSERT INTO public.biz_payout (created_time, updated_time, sync_status, "snapshot", extra, company_id, business_id, payout_id, channel_payout_id, payout_channel_type, channel_type, channel_journal_entry_id) VALUES(now(), now(), 'PROCESSING', '{"id":"po_1QXFOPIARuRMeaUadKY0SLt1","object":"payout"}', '', 111648, 111997, 'po_1QXFOPIARuRMeaUadKY0SLt1', '', '', 'LAYER', '');
              INSERT INTO public.biz_payout (created_time, updated_time, sync_status, "snapshot", extra, company_id, business_id, payout_id, channel_payout_id, payout_channel_type, channel_type, channel_journal_entry_id) VALUES(now(), now(), 'PROCESSING', '{"id":"po_1QYhHbIARuRMeaUadEoPzLc7","object":"payout"}', '', 111648, 111997, 'po_1QYhHbIARuRMeaUadEoPzLc7', '', '', 'LAYER', '');
              INSERT INTO public.biz_payout (created_time, updated_time, sync_status, "snapshot", extra, company_id, business_id, payout_id, channel_payout_id, payout_channel_type, channel_type, channel_journal_entry_id) VALUES(now(), now(), 'PROCESSING', '{"id":"po_1QZ3mdIARuRMeaUaQAe6gRR1","object":"payout"}', '', 111648, 111997, 'po_1QZ3mdIARuRMeaUaQAe6gRR1', '', '', 'LAYER', '');
              INSERT INTO public.biz_payout (created_time, updated_time, sync_status, "snapshot", extra, company_id, business_id, payout_id, channel_payout_id, payout_channel_type, channel_type, channel_journal_entry_id) VALUES(now(), now(), 'PROCESSING', '{"id":"po_1QbEc9IARuRMeaUatT4x6pAd","object":"payout"}', '', 111648, 111997, 'po_1QbEc9IARuRMeaUatT4x6pAd', '', '', 'LAYER', '');
              INSERT INTO public.biz_payout (created_time, updated_time, sync_status, "snapshot", extra, company_id, business_id, payout_id, channel_payout_id, payout_channel_type, channel_type, channel_journal_entry_id) VALUES(now(), now(), 'PROCESSING', '{"id":"po_1QUi3GIARuRMeaUaqxxcwI6J","object":"payout"}', '', 111648, 111997, 'po_1QUi3GIARuRMeaUaqxxcwI6J', '', '', 'LAYER', '');
              INSERT INTO public.biz_payout (created_time, updated_time, sync_status, "snapshot", extra, company_id, business_id, payout_id, channel_payout_id, payout_channel_type, channel_type, channel_journal_entry_id) VALUES(now(), now(), 'PROCESSING', '{"id":"po_1QWWQhIARuRMeaUaTLLP22Fy","object":"payout"}', '', 111648, 111997, 'po_1QWWQhIARuRMeaUaTLLP22Fy', '', '', 'LAYER', '');
              INSERT INTO public.biz_payout (created_time, updated_time, sync_status, "snapshot", extra, company_id, business_id, payout_id, channel_payout_id, payout_channel_type, channel_type, channel_journal_entry_id) VALUES(now(), now(), 'PROCESSING', '{"id":"po_1Qa9C2IARuRMeaUaKVY7dS5K","object":"payout"}', '', 111648, 111997, 'po_1Qa9C2IARuRMeaUaKVY7dS5K', '', '', 'LAYER', '');
              INSERT INTO public.biz_payout (created_time, updated_time, sync_status, "snapshot", extra, company_id, business_id, payout_id, channel_payout_id, payout_channel_type, channel_type, channel_journal_entry_id) VALUES(now(), now(), 'PROCESSING', '{"id":"po_1QXbqnIARuRMeaUaq7oe3neC","object":"payout"}', '', 111648, 111997, 'po_1QXbqnIARuRMeaUaq7oe3neC', '', '', 'LAYER', '');
