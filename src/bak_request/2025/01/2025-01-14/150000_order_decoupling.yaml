name: "Create table moe_grooming.order_decoupling_flow_marker"
description: "Create table moe_grooming.order_decoupling_flow_marker"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: "AWS_RDS"
        identifier: "staging-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              create table if not exists moe_grooming.order_decoupling_flow_marker
              (
                  id         bigint auto_increment comment 'id'
                      primary key,
                  order_id   bigint                             not null comment 'order.id',
                  created_at datetime default CURRENT_TIMESTAMP not null,
                  updated_at datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
                  constraint order_decoupling_flow_marker_pk
                      unique (order_id)
              )
                  comment '这个表用于迁移过渡，记录使用新 flow（先 submit 后 pay）创建的 order，等全部（OBC，CAPP）使用新 flow 之后，这个表可以删除';
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: "AWS_RDS"
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              create table if not exists moe_grooming.order_decoupling_flow_marker
              (
                  id         bigint auto_increment comment 'id'
                      primary key,
                  order_id   bigint                             not null comment 'order.id',
                  created_at datetime default CURRENT_TIMESTAMP not null,
                  updated_at datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
                  constraint order_decoupling_flow_marker_pk
                      unique (order_id)
              )
                  comment '这个表用于迁移过渡，记录使用新 flow（先 submit 后 pay）创建的 order，等全部（OBC，CAPP）使用新 flow 之后，这个表可以删除';
