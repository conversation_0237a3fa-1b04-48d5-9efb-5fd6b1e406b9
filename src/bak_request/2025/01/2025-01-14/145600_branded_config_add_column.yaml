name: "moego_branded_app branded_app_config add column is_allow_cancel"
description: "moego_branded_app branded_app_config add column is_allow_cancel"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: "AWS_RDS"
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_branded_app"
            sql: |
              ALTER TABLE "public"."branded_app_config" 
              ADD COLUMN "is_allow_cancel" bool NOT NULL DEFAULT true;
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: "AWS_RDS"
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_branded_app"
            sql: |
              ALTER TABLE "public"."branded_app_config" 
              ADD COLUMN "is_allow_cancel" bool NOT NULL DEFAULT true;