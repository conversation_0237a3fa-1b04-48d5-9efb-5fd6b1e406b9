name: "Create task management permission"
description: "Create task management permission, grant database to jason"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_permission"
            sql: |
              GRANT CONNECT ON DATABASE moego_permission TO developer_jason;
              GRANT USAGE ON SCHEMA public TO developer_jason;
              GRANT SELECT ON ALL TABLES IN SCHEMA public TO developer_jason;
              ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO developer_jason;
              
              INSERT INTO public.permission_category (id, name, display_name, description, type)
              VALUES (23, 'taskManagement', 'Task management', null, 'MOEGO_PLATFORM');
              
              INSERT INTO public.permission (id, name, display_name, description, parent_permission_id, category_id)
              VALUES (180, 'accessTasks', 'Access tasks', '', 0, 23);
              INSERT INTO public.permission_scope (id, permission_id, name, display_name, scope_index, available_rule,
              parent_permission_scope_id, extra_param)
              VALUES (80, 180, 'taskManagementAllTasks', 'All tasks', 1, 0, 0, null);
              INSERT INTO public.permission_scope (id, permission_id, name, display_name, scope_index, available_rule,
              parent_permission_scope_id, extra_param)
              VALUES (81, 180, 'taskManagementOnlyAssignedTasks', 'Only assigned tasks', 2, 0, 0, null);
              
              INSERT INTO public.permission (id, name, display_name, description, parent_permission_id, category_id)
              VALUES (181, 'assignStaffToTask', 'Assign staff to task', '', 180, 23);
              
              insert into role_permission_mapping(role_id, permission_id, selected_permission_scope_index) select id, 180, 2 from role where deleted_at is null;