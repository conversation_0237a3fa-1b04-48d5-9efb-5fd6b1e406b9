name: "调整 order 表 order_ref_id prod 索引 && 新增assign item表"
description: "调整 order 表 order_ref_id prod 索引 && 新增assign item表"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              create index order_idx_order_ref_id
                on "order" (order_ref_id);
              drop index idx_order_ref_id;
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: "AWS_RDS"
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              CREATE TABLE IF NOT EXISTS public.order_item_assigned_amount (
              id           BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
              order_id     BIGINT DEFAULT 0 NOT NULL,
              item_id      BIGINT DEFAULT 0 NOT NULL,
              amount       NUMERIC(20, 2) DEFAULT 0.00 NOT NULL,
              currency_code TEXT DEFAULT '' NOT NULL,
              business_id   BIGINT DEFAULT 0 NOT NULL,
              created_at    TIMESTAMP DEFAULT NOW(),
              updated_at    TIMESTAMP DEFAULT NOW()
              );
              CREATE INDEX order_item_assigned_amounts_idx_order_id ON order_item_assigned_amount (order_id);
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: "AWS_RDS"
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              CREATE TABLE IF NOT EXISTS public.order_item_assigned_amount (
              id           BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
              order_id     BIGINT DEFAULT 0 NOT NULL,
              item_id      BIGINT DEFAULT 0 NOT NULL,
              amount       NUMERIC(20, 2) DEFAULT 0.00 NOT NULL,
              currency_code TEXT DEFAULT '' NOT NULL,
              business_id   BIGINT DEFAULT 0 NOT NULL,
              created_at    TIMESTAMP DEFAULT NOW(),
              updated_at    TIMESTAMP DEFAULT NOW()
              );
              CREATE INDEX order_item_assigned_amounts_idx_order_id ON order_item_assigned_amount (order_id);

