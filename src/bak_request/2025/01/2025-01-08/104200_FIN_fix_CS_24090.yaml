name: "Fix CS-24090"
description: "老订单退款 Convenience Fee 没有扣，需要人工处理"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              INSERT INTO public.order_line_extra_fee(business_id, order_id, order_item_id, apply_type, is_deleted, fee_type, amount, collect_type, description, apply_by, create_time, update_time) VALUES (115782, 133905748, 0, 'none', false, 'convenience fee', 6.77, 'subtract', '', 0, NOW(), NOW());

              UPDATE public."order" SET 
                  total_amount = total_amount - 6.77,
                  extra_fee_amount = extra_fee_amount - 6.77,
                  remain_amount = GREATEST(0, remain_amount - 6.77),
                  update_time = NOW()
              WHERE business_id = 115782 AND id = 133905748;
