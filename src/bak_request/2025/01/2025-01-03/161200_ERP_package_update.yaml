name: "grooming 添加 package update 相关新字段"
description: "grooming 添加 package update 相关新字段"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_grooming"
            username: "${db.user.admin}"
            sql: |
              alter table moe_grooming_package_history
              add activity_type smallint default 1 not null comment '1-Redeem package; 2-Extend package; 3-Manual change';
              
              alter table moe_grooming_package_history
              add after_extend_expire_date varchar(50) default '' not null comment '延长有效期后的截止日期';
              
              alter table moe_grooming_package_history
              add staff_id int default 0 not null comment '延长有效期后的截止日期';
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_grooming"
            username: "${db.user.admin}"
            sql: |
              alter table moe_grooming_package_history
              add activity_type smallint default 1 not null comment '1-Redeem package; 2-Extend package; 3-Manual change';
              
              alter table moe_grooming_package_history
              add after_extend_expire_date varchar(50) default '' not null comment '延长有效期后的截止日期';
              
              alter table moe_grooming_package_history
              add staff_id int default 0 not null comment '延长有效期后的截止日期';