name: "修改用户支付数据"
description: "修改用户支付数据"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              UPDATE public.order
              SET status = 2, payment_status = 'PAID',tips_amount = 10.00,total_amount = 91.19,update_time = NOW()
              where id = 116638489 and business_id = '104603';