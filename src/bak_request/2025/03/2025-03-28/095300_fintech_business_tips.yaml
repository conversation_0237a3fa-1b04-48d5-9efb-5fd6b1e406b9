name: "FIN-2601 Support split tips between business and staff"
description: "FIN-2601 Support split tips between business and staff"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              alter table public.order_tip_split_record
                add business_tip_amount numeric(20, 2) default 0.00 not null;

              alter table public.order_tip_split_record
                add is_business_tip_amount_effective boolean default false not null;

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              alter table public.order_tip_split_record
                add business_tip_amount numeric(20, 2) default 0.00 not null;

              alter table public.order_tip_split_record
                add is_business_tip_amount_effective boolean default false not null;
