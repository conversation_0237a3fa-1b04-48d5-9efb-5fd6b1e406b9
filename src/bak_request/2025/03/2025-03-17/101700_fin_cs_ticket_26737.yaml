name: "修改用户支付数据"
description: "修改用户支付数据"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              UPDATE public."order" SET 
                total_amount = total_amount - 5.7,
                extra_fee_amount = extra_fee_amount - 5.7,
                remain_amount = GREATEST(0, remain_amount - 5.7),
                update_time = NOW()
              WHERE business_id = 5836 AND id = 134070434;
              
              INSERT INTO public.order_line_extra_fee(business_id, order_id, order_item_id, apply_type, is_deleted, fee_type, amount, collect_type, description, apply_by, create_time, update_time) 
              VALUES (5836, 134070434, 0, 'none', false, 'convenience fee', 5.7, 'subtract', '', 0, NOW(), NOW());
