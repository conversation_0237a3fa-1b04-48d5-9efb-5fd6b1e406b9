name: "extend membership description length limitation"
description: "related jira: https://moego.atlassian.net/browse/MER-3661"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_billing"
            sql: |
              ALTER TABLE "public"."product_relation"
              ALTER COLUMN "description" TYPE text;

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_billing"
            sql: |
              ALTER TABLE "public"."product_relation"
              ALTER COLUMN "description" TYPE text;



