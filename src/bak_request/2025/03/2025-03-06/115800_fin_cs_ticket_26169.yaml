name: "修改用户支付数据"
description: "修改用户支付数据"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              UPDATE moe_payment.payment
                SET amount = 110.41, update_time = UNIX_TIMESTAMP()
                WHERE id = 25607271;

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              UPDATE public.order
                SET paid_amount = 110.41 ,update_time = NOW()
                WHERE id = 135916401;