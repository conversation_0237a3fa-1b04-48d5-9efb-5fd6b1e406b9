name: "add pet color status column"
description: "add pet color status column"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_customer"
            sql: |
                alter table moe_customer.moe_pet_pet_color_binding add column `status` tinyint(1) NOT NULL DEFAULT 1 comment '1:有效 2:无效' AFTER `pet_color_id`;

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_customer"
            sql: |
                alter table moe_customer.moe_pet_pet_color_binding add column `status` tinyint(1) NOT NULL DEFAULT 1 comment '1:有效 2:无效' AFTER `pet_color_id`;