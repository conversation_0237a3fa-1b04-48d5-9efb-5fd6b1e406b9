name: "edit appointment date"
description: "edit appointment date"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              update moe_grooming.moe_grooming_appointment
              set appointment_date     = '2025-03-14',
              appointment_end_date = '2025-03-14',
              update_time          = UNIX_TIMESTAMP()
              where id = 56275322;
              
              update moe_grooming.moe_grooming_pet_detail
              set start_date = '2025-03-14',
              end_date   = '2025-03-14',
              updated_at = now()
              where grooming_id = 56275322
              and id in (189638796, 189638797);
