name: "Fix CS-27106"
description: "修复错误状态数据"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              UPDATE moe_payment.payment 
              SET status = -1 ,update_time = UNIX_TIMESTAMP()
              WHERE id = '27595140' and status = 1 and stripe_intent_id = 'pi_3R3jqiIZwcIFVLGr2TCT1mJV' and invoice_id = '139638368';
              
              UPDATE moe_payment.payment 
              SET status = -1 ,update_time = UNIX_TIMESTAMP()
              WHERE id = '26834624' and status = 1 and stripe_intent_id = 'pi_3QxLapIZwcIFVLGr1qYuDDdy' and invoice_id = '138273180';