name: "添加 moe_grooming 读写权限"
description: "添加 moe_grooming 读写权限"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_grooming"
            username: "${db.user.admin}"
            sql: |
              GRANT ALTER, CREATE, DROP ON moe_grooming.* TO 'developer_zhangdong'@'%';
        - execute_sql:
            database: "moe_retail"
            username: "${db.user.admin}"
            sql: |
              GRANT ALTER, CREATE, DROP ON moe_retail.* TO 'developer_zhangdong'@'%';       
        - execute_sql:
            database: "moe_business"
            username: "${db.user.admin}"
            sql: |
              GRANT ALTER, CREATE, DROP ON moe_business.* TO 'developer_zhangdong'@'%';
