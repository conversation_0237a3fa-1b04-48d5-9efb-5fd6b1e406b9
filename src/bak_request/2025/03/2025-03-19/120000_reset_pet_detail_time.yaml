name: "reset pet detail start time and end time to 720"
description: "reset pet detail start time and end time to 720"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              update moe_grooming.moe_grooming_pet_detail
              set start_time = 720,
              end_time   = service_time + 720
              where id in (select id from (select id
                          from moe_grooming.moe_grooming_pet_detail
                          where grooming_id in
                          (66586454, 65527438, 65527438, 64989588, 64990800, 64991947, 67060406, 64520812, 64520812, 67022155,
                          66654379, 66863467, 66432269, 64677286, 66171972, 66621049, 64930618, 67162313, 64669674, 66821372,
                          65030088, 64519829, 64520663, 67371777, 65140726, 66852130, 67020552, 65685506, 64520246, 66192946,
                          64940933, 65723389, 65723325, 67052076, 66723714, 66774195, 64669644, 67355247, 65647128, 64527171,
                          66028261, 65758888, 65759071, 65759474, 65823117, 67096845, 64521549, 67062375, 66997921, 67149461,
                          66539207, 66539207, 66022614, 66101719, 64520998, 66783271, 65711904, 66791065, 66996222, 66996349,
                          66660709, 66660789, 66994811, 66995013, 66997499, 65554906, 66612558, 66612558, 64658540, 64666789,
                          64669871, 64715105, 64715105, 64718484, 66091522, 67089409, 67089538, 64886776, 66433736, 66433813,
                          64906737, 67160140, 66034493, 65808974, 67372201, 64995411, 64995553, 64995652, 66103881, 65660291,
                          66532443, 66722494, 67053619, 66663882, 66664021, 65797466, 67363111, 65278802, 65655763, 65815856,
                          65815974, 65344184, 66135113, 64923670, 67308183, 66588314, 65762304, 66440860, 66899751, 65281846,
                          66202767, 65394612, 66775946, 66775946, 66922844)
                          and start_time < 0) t);
