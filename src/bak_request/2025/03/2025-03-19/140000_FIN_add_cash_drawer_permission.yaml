name: "Add Cash Drawer permission"
description: "Add Cash Drawer permission"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-postgres-cluster
      actions:
        execute_sql:
            database: "moego_permission"
            sql: |
                INSERT INTO public.permission
                (
                    id, name, display_name, description, parent_permission_id, category_id
                ) VALUES (
                    303, 'accessCashDrawer', 'Can process cash drawer balance', '', 0, 1);
                INSERT INTO role_permission_mapping
                (
                    role_id, permission_id
                ) SELECT id, 303 FROM role WHERE deleted_at IS NULL;
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_permission"
            sql: |
                INSERT INTO public.permission
                (
                    id, name, display_name, description, parent_permission_id, category_id
                ) VALUES (
                    303, 'accessCashDrawer', 'Can process cash drawer balance', '', 0, 1);
                INSERT INTO role_permission_mapping
                (
                    role_id, permission_id
                ) SELECT id, 303 FROM role WHERE deleted_at IS NULL;
