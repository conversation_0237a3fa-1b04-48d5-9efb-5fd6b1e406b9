name: "Rename Cash Drawer permission"
description: "Rename Cash Drawer permission"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-postgres-cluster
      actions:
        execute_sql:
            database: "moego_permission"
            sql: |
                UPDATE public.permission SET display_name = 'Process cash drawer balance' WHERE id = 303;
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_permission"
            sql: |
                UPDATE public.permission SET display_name = 'Process cash drawer balance' WHERE id = 303;
