name: "reset pet detail start time and end time to 720"
description: "reset pet detail start time and end time to 720"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_message"
            sql: |
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 120631;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 120058;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 112480;
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_engagement"
            sql: |
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 120250, 120631);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 119706, 120058);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 112109, 112480);
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_sms"
            sql: |
              update business_sms_setting set twilio_number = '+***********' where business_id = 120631;
              update business_sms_setting set twilio_number = '+***********' where business_id = 120058;
              update business_sms_setting set twilio_number = '+***********' where business_id = 112480;
