name: "Add subscription.public.subscription(status_idx)"
description: "add status idx in public.subscription"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-postgres-cluster
      actions:
        execute_sql:
            database: "moego_subscription"
            sql: |
              create index idx_subscription_status on subscription(status);
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_subscription"
            sql: |
              create index idx_subscription_status on subscription(status);
