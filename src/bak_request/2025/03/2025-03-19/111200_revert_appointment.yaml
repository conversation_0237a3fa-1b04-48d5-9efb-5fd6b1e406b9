name: "revert appointment"
description: "revert appointment"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              update moe_grooming.moe_grooming_appointment
              set is_deprecate = 0
              where id in (67519577, 68434831, 68578624, 68608549, 68699763, 68701172, 68737151, 63524982, 68042445, 68615190, 68741087, 68716863, 68438447, 68728168, 67039139);
