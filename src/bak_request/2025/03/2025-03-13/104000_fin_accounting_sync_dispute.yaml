name: "Add biz dispute fund operate table"
description: "Add biz dispute fund operate table"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_accounting"
            sql: |
              CREATE TABLE moego_accounting.public.biz_dispute_fund_operate (
                id BIGSERIAL NOT NULL,
                created_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                updated_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                sync_status varchar(32) NOT NULL DEFAULT '',
                snapshot text NOT NULL DEFAULT '',
                extra text NOT NULL DEFAULT '',
                company_id BIGINT NOT NULL DEFAULT 0,
                business_id BIGINT NOT NULL DEFAULT 0,
                fund_operate_id BIGINT NOT NULL DEFAULT 0,
                channel_type varchar(32) NOT NULL DEFAULT '',
                channel_journal_entry_id varchar(512) NOT NULL DEFAULT '',
              
                CONSTRAINT biz_dispute_fund_operate_pk PRIMARY KEY (id)
              );
              CREATE UNIQUE INDEX uniq_biz_dispute_fund_operate_id ON public.biz_dispute_fund_operate (fund_operate_id,channel_type);
              
              COMMENT ON TABLE biz_dispute_fund_operate IS 'accounting biz dispute fund operate';
              COMMENT ON COLUMN biz_dispute_fund_operate.id is 'primary key';
              COMMENT ON COLUMN biz_dispute_fund_operate.created_time is 'created time';
              COMMENT ON COLUMN biz_dispute_fund_operate.updated_time is 'updated time';
              COMMENT ON COLUMN biz_dispute_fund_operate.sync_status is 'sync status';
              COMMENT ON COLUMN biz_dispute_fund_operate.snapshot is 'dispute fund operate snapshot';
              COMMENT ON COLUMN biz_dispute_fund_operate.extra is 'extra';
              COMMENT ON COLUMN biz_dispute_fund_operate.company_id is 'moego company id';
              COMMENT ON COLUMN biz_dispute_fund_operate.business_id is 'moego business id';
              COMMENT ON COLUMN biz_dispute_fund_operate.fund_operate_id is 'moego dispute fund operate id';
              COMMENT ON COLUMN biz_dispute_fund_operate.channel_type is 'channel type';
              COMMENT ON COLUMN biz_dispute_fund_operate.channel_journal_entry_id is 'channel journal entry id';
              
              CREATE TRIGGER update_at
                BEFORE UPDATE
                ON public.biz_dispute_fund_operate
                FOR EACH ROW
              EXECUTE PROCEDURE update_at();
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_accounting"
            sql: |
              CREATE TABLE moego_accounting.public.biz_dispute_fund_operate (
                id BIGSERIAL NOT NULL,
                created_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                updated_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                sync_status varchar(32) NOT NULL DEFAULT '',
                snapshot text NOT NULL DEFAULT '',
                extra text NOT NULL DEFAULT '',
                company_id BIGINT NOT NULL DEFAULT 0,
                business_id BIGINT NOT NULL DEFAULT 0,
                fund_operate_id BIGINT NOT NULL DEFAULT 0,
                channel_type varchar(32) NOT NULL DEFAULT '',
                channel_journal_entry_id varchar(512) NOT NULL DEFAULT '',
              
                CONSTRAINT biz_dispute_fund_operate_pk PRIMARY KEY (id)
              );
              CREATE UNIQUE INDEX uniq_biz_dispute_fund_operate_id ON public.biz_dispute_fund_operate (fund_operate_id,channel_type);
              
              COMMENT ON TABLE biz_dispute_fund_operate IS 'accounting biz dispute fund operate';
              COMMENT ON COLUMN biz_dispute_fund_operate.id is 'primary key';
              COMMENT ON COLUMN biz_dispute_fund_operate.created_time is 'created time';
              COMMENT ON COLUMN biz_dispute_fund_operate.updated_time is 'updated time';
              COMMENT ON COLUMN biz_dispute_fund_operate.sync_status is 'sync status';
              COMMENT ON COLUMN biz_dispute_fund_operate.snapshot is 'dispute fund operate snapshot';
              COMMENT ON COLUMN biz_dispute_fund_operate.extra is 'extra';
              COMMENT ON COLUMN biz_dispute_fund_operate.company_id is 'moego company id';
              COMMENT ON COLUMN biz_dispute_fund_operate.business_id is 'moego business id';
              COMMENT ON COLUMN biz_dispute_fund_operate.fund_operate_id is 'moego dispute fund operate id';
              COMMENT ON COLUMN biz_dispute_fund_operate.channel_type is 'channel type';
              COMMENT ON COLUMN biz_dispute_fund_operate.channel_journal_entry_id is 'channel journal entry id';
              
              CREATE TRIGGER update_at
                BEFORE UPDATE
                ON public.biz_dispute_fund_operate
                FOR EACH ROW
              EXECUTE PROCEDURE update_at();


