name: "subscription upgrade accounting"
description: "subscription upgrade accounting"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_subscription"
            sql: |
              --- <EMAIL>,companyID=120708,sub_id=642,license_id=642,entitlement=189
              UPDATE subscription
              SET total_price = 159, billing_cycle_value = 1, plan_product_id = 145
              WHERE id = 642;

              INSERT INTO public.entitlement (license_id, product_id, feature_id, feature_key, feature_name, feature_description,
                                            feature_setting, created_at, updated_at, version)
              VALUES (642, 145, 1, 'ACCOUNTING_BASIC', DEFAULT, DEFAULT, '{"onOff":{"on":true}}', DEFAULT, DEFAULT, DEFAULT);

              UPDATE entitlement
              SET product_id = 145, feature_id = 2, feature_key = 'ACCOUNTING_FULL_SERVICE'
              WHERE id = 189;

              --- <EMAIL>,companyID=121130,sub_id=849,license_id=49173,entitlement=48565
              UPDATE subscription
              SET total_price = 99, billing_cycle_value = 1, plan_product_id = 145
              WHERE id = 849;

              INSERT INTO public.entitlement (license_id, product_id, feature_id, feature_key, feature_name, feature_description,
                                            feature_setting, created_at, updated_at, version)
              VALUES (49173, 145, 1, 'ACCOUNTING_BASIC', DEFAULT, DEFAULT, '{"onOff":{"on":true}}', DEFAULT, DEFAULT, DEFAULT);

              UPDATE entitlement
              SET product_id = 145, feature_id = 2, feature_key = 'ACCOUNTING_FULL_SERVICE'
              WHERE id = 48565;