name: "fix dispute event error"
description: "fix dispute event error"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              UPDATE mm_stripe_dispute_event_log SET status = 'SUCCEED' WHERE dispute_id = 'du_1QxgyaIZwcIFVLGr5RNvxDAN';