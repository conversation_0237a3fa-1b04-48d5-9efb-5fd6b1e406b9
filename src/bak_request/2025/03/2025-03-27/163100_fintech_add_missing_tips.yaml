name: "Add missing tips"
description: "Add missing tips"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              BEGIN;

              UPDATE order_payment
              SET payment_tips = 35.00,
                  payment_tips_before_create = 35.00,
                  update_time = NOW()
              WHERE id = 100031243 AND order_id = 140035477;

              UPDATE "order"
              SET tips_amount = 35.00,
                  total_amount = 210.00,
                  update_time = NOW()
              WHERE id = 140035477 AND business_id = 118199;

              COMMIT;
