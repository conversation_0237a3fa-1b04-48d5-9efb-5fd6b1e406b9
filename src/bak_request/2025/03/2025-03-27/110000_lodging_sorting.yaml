name: "update lodging sort fields"
description: "update lodging sort fields"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_offering"
            sql: |
              update lodging_type set sort = id where id > 0;
              update lodging_unit set sort = id where id > 0;

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_offering"
            sql: |
                update lodging_type set sort = id where id > 0;
                update lodging_unit set sort = id where id > 0;