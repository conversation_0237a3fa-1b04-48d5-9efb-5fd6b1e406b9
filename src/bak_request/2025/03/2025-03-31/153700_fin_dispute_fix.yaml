name: "fix dispute event error"
description: "fix dispute event error"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              BEGIN;

              UPDATE mm_stripe_dispute SET status = 'under_review', updated_at = CURRENT_TIMESTAMP() WHERE dispute_id IN ('du_1QmGDKIZwcIFVLGrmeCXstbu', 'du_1QmHR5IZwcIFVLGrKeZPmyC2', 'du_1QmI7ZIZwcIFVLGrb7G4f3D9');

              UPDATE mm_stripe_dispute_event_log SET status = 'SUCCEED', updated_at = CURRENT_TIMESTAMP() WHERE id IN (366, 372, 368);

              UPDATE mm_stripe_dispute_event_log SET status = 'INIT', updated_at = CURRENT_TIMESTAMP() WHERE id IN (367, 373, 370);

              COMMIT;