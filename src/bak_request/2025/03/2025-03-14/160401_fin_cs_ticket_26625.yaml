name: "修改用户支付数据"
description: "修改用户支付数据"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              UPDATE public.order
              SET 
                  status = 2, 
                  payment_status = 'PAID',
                  total_amount = 0, 
                  remain_amount = remain_amount - 6.47, 
                  extra_fee_amount = 0,
                  update_time = NOW()
              WHERE 
                  id = 138646960 
                  AND business_id = '111859';

              UPDATE public.order_line_extra_fee
              SET is_deleted = 'true',update_time = NOW()
              where id in ('119731863','119731888','119736966','119594844') and business_id = '111859' and order_id = '138646960';