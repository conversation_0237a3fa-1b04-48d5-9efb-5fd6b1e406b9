name: "playgroup add table"
description: "playgroup add table"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_offering"
            sql: |
              create table playgroup
              (
                  id               bigserial
                      constraint playgroup_pk
                          primary key,
                  company_id       bigint                   default 0                            not null,
                  name             varchar(20)              default ''::character varying        not null,
                  color_code       varchar(7)               default ''::character varying not null,
                  max_pet_capacity integer                  default 0                            not null,
                  sort             integer                  default 0                            not null,
                  description      varchar(100)             default ''::character varying        not null,
                  created_at       timestamp with time zone default CURRENT_TIMESTAMP            not null,
                  updated_at       timestamp with time zone default CURRENT_TIMESTAMP            not null,
                  created_by       bigint                   default 0                            not null,
                  updated_by       bigint                   default 0                            not null,
                  deleted_at       timestamp with time zone,
                  deleted_by       bigint                   default 0                            not null
              );

              comment on column public.playgroup.company_id is 'company id';

              comment on column public.playgroup.color_code is 'playgroup color code';

              comment on column public.playgroup.max_pet_capacity is 'max pet capacity';

              comment on column public.playgroup.sort is 'Playgroup list sort. Start with 1 and put the smallest first';

              create index playgroup_company_id_index
                  on public.playgroup (company_id);


  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              create table pet_playgroup
              (
                id             bigint auto_increment
                      primary key,
                company_id     bigint      default 0                 not null,
                business_id    bigint      default 0                 not null,
                playgroup_id   bigint      default 0                 not null,
                appointment_id bigint      default 0                 not null,
                pet_id         bigint      default 0                 not null,
                date           varchar(10) default ''                not null,
                sort           int         default 0                 not null comment 'pet playgroup list sort. start with 1 and put the smallest first',
                created_at     datetime    default CURRENT_TIMESTAMP not null,
                updated_at     datetime    default CURRENT_TIMESTAMP not null,
                constraint pet_playgroup_appointment_index
                  unique (appointment_id, pet_id, date)
              );

              create index pet_playgroup_business_daily_index
                on pet_playgroup (business_id, date);

              create index pet_playgroup_company_daily_index
                on pet_playgroup (company_id, date);


  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_customer"
            sql: |
              alter table moe_customer_pet
              add playgroup_id bigint default 0 not null comment 'pet playgroup';

  
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_offering"
            sql: |
              create table playgroup
              (
                  id               bigserial
                      constraint playgroup_pk
                          primary key,
                  company_id       bigint                   default 0                            not null,
                  name             varchar(20)              default ''::character varying        not null,
                  color_code       varchar(7)               default ''::character varying not null,
                  max_pet_capacity integer                  default 0                            not null,
                  sort             integer                  default 0                            not null,
                  description      varchar(100)             default ''::character varying        not null,
                  created_at       timestamp with time zone default CURRENT_TIMESTAMP            not null,
                  updated_at       timestamp with time zone default CURRENT_TIMESTAMP            not null,
                  created_by       bigint                   default 0                            not null,
                  updated_by       bigint                   default 0                            not null,
                  deleted_at       timestamp with time zone,
                  deleted_by       bigint                   default 0                            not null
              );

              comment on column public.playgroup.company_id is 'company id';

              comment on column public.playgroup.color_code is 'playgroup color code';

              comment on column public.playgroup.max_pet_capacity is 'max pet capacity';

              comment on column public.playgroup.sort is 'Playgroup list sort. Start with 1 and put the smallest first';

              create index playgroup_company_id_index
                  on public.playgroup (company_id);


  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              create table pet_playgroup
              (
                id             bigint auto_increment
                      primary key,
                company_id     bigint      default 0                 not null,
                business_id    bigint      default 0                 not null,
                playgroup_id   bigint      default 0                 not null,
                appointment_id bigint      default 0                 not null,
                pet_id         bigint      default 0                 not null,
                date           varchar(10) default ''                not null,
                sort           int         default 0                 not null comment 'pet playgroup list sort. start with 1 and put the smallest first',
                created_at     datetime    default CURRENT_TIMESTAMP not null,
                updated_at     datetime    default CURRENT_TIMESTAMP not null,
                constraint pet_playgroup_appointment_index
                  unique (appointment_id, pet_id, date)
              );

              create index pet_playgroup_business_daily_index
                on pet_playgroup (business_id, date);

              create index pet_playgroup_company_daily_index
                on pet_playgroup (company_id, date);


  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_customer"
            sql: |
              alter table moe_customer_pet
              add playgroup_id bigint default 0 not null comment 'pet playgroup';