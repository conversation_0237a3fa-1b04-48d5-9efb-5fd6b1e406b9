name: "porting number"
description: "https://moego.atlassian.net/browse/CS-26161"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_message"
            sql: |
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 114741;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 115841;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 113214;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 116797;
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_engagement"
            sql: |
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 114302, 114741);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 115367, 115841);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 112825, 113214);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 116277, 116797);
