name: "Lodging type support custom sort"
description: "lodging type support custom sort"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_offering"
            sql: |
                alter table lodging_type add column sort int default 0 not null;
                update lodging_type set sort = id where sort = 0;
                
                alter table lodging_unit add column sort int default 0 not null;
                update lodging_unit set sort = id where sort = 0;

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_offering"
            sql: |
                alter table lodging_type add column sort int default 0 not null;
                update lodging_type set sort = id where sort = 0;

                alter table lodging_unit add column sort int default 0 not null;
                update lodging_unit set sort = id where sort = 0;