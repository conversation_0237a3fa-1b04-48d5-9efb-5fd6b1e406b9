name: "Grant permission to better"
description: "grant permission to better"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_reporting"
            username: "${db.user.admin}"
            sql: |
              GRANT CONNECT ON DATABASE moego_reporting TO developer_better;
              GRANT USAGE ON SCHEMA public TO developer_better;
              GRANT INSERT, SELECT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO developer_better;
              ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, UPDATE ON TABLES TO developer_better;
