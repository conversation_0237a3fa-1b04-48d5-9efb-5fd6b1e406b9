name: "create table for archiving session"
description: "create table for archiving session"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_account"
            sql: |
              create table session_backup (like session);
        - execute_sql:
            database: "moego_account"
            sql: |
              create table session_archive_task
              (
              id                 bigserial primary key,
              max_date           date                                                    not null,
              start_id           bigint                                                  not null,
              end_id             bigint                                                  not null,
              step               int                                                     not null,
              current_id         bigint                                                  not null,
              total              int                       default 0                     not null,
              status             varchar(32)               default 'created'             not null,
              created_at         timestamp with time zone  default statement_timestamp() not null,
              updated_at         timestamp with time zone  default null
              );