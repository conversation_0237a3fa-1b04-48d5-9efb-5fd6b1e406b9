name: "fix membership payment status"
description: "修复membership payment数据"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              UPDATE payment SET status = 3, update_time = UNIX_TIMESTAMP() WHERE id = 25207328;
