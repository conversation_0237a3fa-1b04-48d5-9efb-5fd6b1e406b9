name: "add pet color related tables"
description: "add pet color related tables"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_customer"
            username: "${db.user.admin}"
            sql: |
                create table moe_customer.moe_pet_expand
                (
                    id                int unsigned not null AUTO_INCREMENT comment 'id',
                    pet_id int not null comment 'pet id',
                    deactivate_reason varchar(50)  not null default '' comment '标记为不活跃时的原因, 字符串枚举',
                    primary key (`id`) using btree,
                    Unique index (`pet_id`) using btree
                );

                CREATE TABLE `moe_pet_color` (
                  `id` int unsigned NOT NULL AUTO_INCREMENT,
                  `company_id` bigint NOT NULL DEFAULT '0' COMMENT 'company id',
                  `name` varchar(255) NOT NULL DEFAULT '' COMMENT 'name',
                  `status` tinyint NOT NULL DEFAULT '1' COMMENT '1正常  2删除',
                  `create_time` bigint unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
                  `update_time` bigint unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
                  PRIMARY KEY (`id`),
                  KEY `idx_company_id` (`company_id`)
                ) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

                CREATE TABLE `moe_pet_pet_color_binding`
                (
                    `id`           int unsigned NOT NULL AUTO_INCREMENT,
                    `pet_id`       int          NOT NULL DEFAULT '0',
                    `pet_color_id` int          NOT NULL DEFAULT '0',
                    `create_time` bigint not null default 0,
                    PRIMARY KEY (`id`) USING BTREE,
                    KEY `index_petid` (`pet_id`),
                    KEY `idx_pet_code_id` (`pet_color_id`)
                );

                alter table moe_customer.moe_pet_pet_code_binding add column comment varchar(512) default '';
                alter table moe_customer.moe_pet_pet_code_binding add column binding_time datetime default current_timestamp on update current_timestamp;

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_customer"
            username: "${db.user.admin}"
            sql: |
                create table moe_customer.moe_pet_expand
                (
                    id                int unsigned not null AUTO_INCREMENT comment 'id',
                    pet_id int not null comment 'pet id',
                    deactivate_reason varchar(50)  not null default '' comment '标记为不活跃时的原因, 字符串枚举',
                    primary key (`id`) using btree,
                    Unique index (`pet_id`) using btree
                );

                CREATE TABLE `moe_pet_color` (
                  `id` int unsigned NOT NULL AUTO_INCREMENT,
                  `company_id` bigint NOT NULL DEFAULT '0' COMMENT 'company id',
                  `name` varchar(255) NOT NULL DEFAULT '' COMMENT 'name',
                  `status` tinyint NOT NULL DEFAULT '1' COMMENT '1正常  2删除',
                  `create_time` bigint unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
                  `update_time` bigint unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
                  PRIMARY KEY (`id`),
                  KEY `idx_company_id` (`company_id`)
                ) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

                CREATE TABLE `moe_pet_pet_color_binding`
                (
                    `id`           int unsigned NOT NULL AUTO_INCREMENT,
                    `pet_id`       int          NOT NULL DEFAULT '0',
                    `pet_color_id` int          NOT NULL DEFAULT '0',
                    `create_time` bigint not null default 0,
                    PRIMARY KEY (`id`) USING BTREE,
                    KEY `index_petid` (`pet_id`),
                    KEY `idx_pet_code_id` (`pet_color_id`)
                );

                alter table moe_customer.moe_pet_pet_code_binding add column comment varchar(512) default '';
                alter table moe_customer.moe_pet_pet_code_binding add column binding_time datetime default current_timestamp on update current_timestamp;