name: "Add pricing rule table and indexes"
description: "Add pricing rule table and indexes"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_offering"
            sql: |
                create table discount_setting
                (
                id              bigserial
                primary key,
                company_id      bigint                              not null,
                updated_by      bigint                              not null,
                created_at      timestamp default CURRENT_TIMESTAMP not null,
                updated_at      timestamp default CURRENT_TIMESTAMP not null,
                apply_best_only boolean                             not null default false,
                apply_sequence  jsonb     default '[]'::jsonb
                );
                
                create unique index uk_company_id
                on discount_setting (company_id);
                
                
                create table pricing_rule_record
                (
                id                         bigserial
                primary key,
                company_id                 bigint                              not null,
                
                rule_type                  smallint                            not null,
                rule_name                  varchar(255)                        not null,
                is_active                  boolean                             not null,
                
                all_boarding_applicable    boolean                             not null,
                selected_boarding_services jsonb     default '[]'::jsonb,
                all_daycare_applicable     boolean                             not null,
                selected_daycare_services  jsonb     default '[]'::jsonb,
                
                rule_apply_type            smallint                            not null,
                need_in_same_lodging       boolean                             not null,
                
                rule_configuration         jsonb     default '{}'::jsonb,
                
                updated_by                 bigint                              not null,
                created_at                 timestamp default CURRENT_TIMESTAMP not null,
                updated_at                 timestamp default CURRENT_TIMESTAMP not null,
                deleted_at                 timestamp
                );
                
                comment on column pricing_rule_record.rule_type is 'rule type, 1-multi pets, 2-multi duration, 3-peak date';
                
                comment on column pricing_rule_record.rule_apply_type is 'rule apply type, 1-each pet, 2-additional pets, 3-all pets';
                
                create unique index uk_pricing_rule_record_type_rule_name
                on pricing_rule_record (company_id, rule_type, rule_name)
                where (deleted_at IS NULL);
        - execute_sql:
              database: "moego_appointment"
              sql: |
                  create table pricing_rule_record_apply_log
                  (
                      id             bigserial
                          primary key,
                      business_id    bigint                                   not null,
                      company_id     bigint                                   not null,
                      source_id      bigint         default 0                 not null,
                      source_type    integer        default 1                 not null,
                      pet_id         bigint                                   not null,
                      service_id     bigint                                   not null,
                      service_date   varchar(10),
    
                      original_price numeric(20, 4) default 0                 not null,
                      adjusted_price numeric(20, 4) default 0,
    
                      is_using_rule  boolean        default true              not null,
                      pricing_rule   jsonb          default '{}'::jsonb,
                      created_at     timestamp      default CURRENT_TIMESTAMP not null,
                      deleted_at     timestamp
                  );
    
                  comment on column pricing_rule_record_apply_log.source_type is 'source type, 1-appointment, 2-booking request';
    
                  create index idx_pricing_rule_company_id_source_id_source_type
                      on pricing_rule_record_apply_log (company_id, source_id, source_type);

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_offering"
            sql: |
                create table discount_setting
                (
                id              bigserial
                primary key,
                company_id      bigint                              not null,
                updated_by      bigint                              not null,
                created_at      timestamp default CURRENT_TIMESTAMP not null,
                updated_at      timestamp default CURRENT_TIMESTAMP not null,
                apply_best_only boolean                             not null default false,
                apply_sequence  jsonb     default '[]'::jsonb
                );
                
                create unique index uk_company_id
                on discount_setting (company_id);
                
                
                create table pricing_rule_record
                (
                id                         bigserial
                primary key,
                company_id                 bigint                              not null,
                
                rule_type                  smallint                            not null,
                rule_name                  varchar(255)                        not null,
                is_active                  boolean                             not null,
                
                all_boarding_applicable    boolean                             not null,
                selected_boarding_services jsonb     default '[]'::jsonb,
                all_daycare_applicable     boolean                             not null,
                selected_daycare_services  jsonb     default '[]'::jsonb,
                
                rule_apply_type            smallint                            not null,
                need_in_same_lodging       boolean                             not null,
                
                rule_configuration         jsonb     default '{}'::jsonb,
                
                updated_by                 bigint                              not null,
                created_at                 timestamp default CURRENT_TIMESTAMP not null,
                updated_at                 timestamp default CURRENT_TIMESTAMP not null,
                deleted_at                 timestamp
                );
                
                comment on column pricing_rule_record.rule_type is 'rule type, 1-multi pets, 2-multi duration, 3-peak date';
                
                comment on column pricing_rule_record.rule_apply_type is 'rule apply type, 1-each pet, 2-additional pets, 3-all pets';
                
                create unique index uk_pricing_rule_record_type_rule_name
                on pricing_rule_record (company_id, rule_type, rule_name)
                where (deleted_at IS NULL);
        - execute_sql:
              database: "moego_appointment"
              sql: |
                  create table pricing_rule_record_apply_log
                  (
                      id             bigserial
                          primary key,
                      business_id    bigint                                   not null,
                      company_id     bigint                                   not null,
                      source_id      bigint         default 0                 not null,
                      source_type    integer        default 1                 not null,
                      pet_id         bigint                                   not null,
                      service_id     bigint                                   not null,
                      service_date   varchar(10),
    
                      original_price numeric(20, 4) default 0                 not null,
                      adjusted_price numeric(20, 4) default 0,
    
                      is_using_rule  boolean        default true              not null,
                      pricing_rule   jsonb          default '{}'::jsonb,
                      created_at     timestamp      default CURRENT_TIMESTAMP not null,
                      deleted_at     timestamp
                  );
    
                  comment on column pricing_rule_record_apply_log.source_type is 'source type, 1-appointment, 2-booking request';
    
                  create index idx_pricing_rule_company_id_source_id_source_type
                      on pricing_rule_record_apply_log (company_id, source_id, source_type);