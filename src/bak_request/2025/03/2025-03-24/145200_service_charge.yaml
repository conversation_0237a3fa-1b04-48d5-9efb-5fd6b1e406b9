name: "Add service charge type field and indexes"
description: "Add service charge type field and indexes"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
            
                update service_charge set auto_apply_status = 2 where is_mandatory = true and auto_apply_status = 0; 
                update service_charge set auto_apply_status = 1 where is_mandatory = false;
              
                update service_charge
                set surcharge_type = case
                when auto_apply_status = 1 then 2
                when auto_apply_status = 2 then 2
                when auto_apply_status = 3 then 1
                end
                where id > 0;
                
                create unique index uniq_charge_24_hour
                on service_charge (company_id, surcharge_type)
                where service_charge.surcharge_type = 3;

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
                alter table service_charge add surcharge_type smallint default 0 not null;
                
                comment on column service_charge.surcharge_type is 'surcharge type, 1-off hours fee, 2-custome fee, 3-charge 24-hour';
              
                update service_charge set auto_apply_status = 2 where is_mandatory = true and auto_apply_status = 0; 
                update service_charge set auto_apply_status = 1 where is_mandatory = false;
              
                update service_charge
                set surcharge_type = case
                when auto_apply_status = 1 then 2
                when auto_apply_status = 2 then 2
                when auto_apply_status = 3 then 1
                end
                where id > 0;
                
                create unique index uniq_charge_24_hour
                on service_charge (company_id, surcharge_type)
                where service_charge.surcharge_type = 3;
