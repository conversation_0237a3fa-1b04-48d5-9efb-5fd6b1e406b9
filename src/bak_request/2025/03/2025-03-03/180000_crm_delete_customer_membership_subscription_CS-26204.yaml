name: "delete customer subscription https://moego.atlassian.net/browse/CS-26204"
description: "related slack: https://moegoworkspace.slack.com/archives/C04BP8LLKM3/p1740780445733639"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_subscription"
            sql: |
              UPDATE public.subscription
              SET status = 'INCOMPLETE',
              auto_resume_at = NULL
              WHERE id = 1003;



