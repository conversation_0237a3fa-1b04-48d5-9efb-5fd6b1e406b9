name: "Fixes date_type data"
description: "Fixes date_type data"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_online_booking"
            sql: |
              update public.boarding_add_on_detail
              set date_type = 3 and start_date = (specific_dates ->> 0)::date
              where add_on_id in (68240,
                                  68246,
                                  1394329,
                                  1425541,
                                  1425545,
                                  1445213,
                                  1522773,
                                  1524195,
                                  1542181,
                                  1543493)
                and date_type = 2
                and start_date is null;

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_online_booking"
            sql: |
              update public.boarding_add_on_detail
              set date_type = 3 and start_date = (specific_dates ->> 0)::date
              where add_on_id in (68240,
                                  68246,
                                  1394329,
                                  1425541,
                                  1425545,
                                  1445213,
                                  1522773,
                                  1524195,
                                  1542181,
                                  1543493)
                and date_type = 2
                and start_date is null;
