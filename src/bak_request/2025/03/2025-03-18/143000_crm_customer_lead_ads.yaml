name: "customer 库新增 lead ads 表"
description: "customer 库新增 lead ads 表"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_customer"
            sql: |
              CREATE TABLE moe_customer_lead_ads
              (
                  id            INT AUTO_INCREMENT PRIMARY KEY,
                  company_id    BIGINT                             NOT NULL,
                  business_id   BIGINT                             NOT NULL,

                  item_type     INTEGER                            NOT NULL,
                  item_id       VARCHAR(255)                       NOT NULL,

                  ads_data_type INTEGER                            NOT NULL,
                  ads_data      TEXT                               NOT NULL,


                  create_time   DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT 'create time',
                  update_time   DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT 'update time'
              );