name: "Fix CS-26740"
description: "Membership 续费失败 bug，需要人工处理"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              UPDATE moe_payment.payment
              set status=3,
              update_time = unix_timestamp()
              WHERE invoice_id = 138296409 and id = 26848898;
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |

              UPDATE public."order" SET 
                  remain_amount = 0,
                  paid_amount = 105,
                  status = 2,
                  payment_status = 'PAID'
                  update_time = NOW()
              WHERE business_id = 121065 AND id = 138296409;
