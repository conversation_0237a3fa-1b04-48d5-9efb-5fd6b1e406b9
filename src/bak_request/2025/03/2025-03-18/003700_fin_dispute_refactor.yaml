name: "dispute refactor"
description: "dispute refactor"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              CREATE TABLE `mm_stripe_dispute_event_log` (
                `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '流水表自增ID',
                `event_id` VARCHAR(255) NOT NULL COMMENT 'com.stripe.model.Event#id',
                `event_type` VARCHAR(255) NOT NULL COMMENT 'com.stripe.model.Event#type',
                `webhook_created_time` BIGINT UNSIGNED NOT NULL COMMENT 'com.stripe.model.Event#created',
                `webhook_body` TEXT NOT NULL COMMENT 'com.stripe.model.Event#data',
                `dispute_id` VARCHAR(255) NOT NULL COMMENT 'com.stripe.model.Dispute#id',
                `dispute_status` VARCHAR(256) NOT NULL COMMENT 'com.stripe.model.Dispute#status',
                `status` VARCHAR(256) NOT NULL COMMENT '流水表状态: init（待处理），success（处理成功）',
                `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '流水表记录创建时间',
                `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '流水表记录更新时间',
                PRIMARY KEY (`id`),
                UNIQUE KEY `
                uniq_event_id` (`event_id`),
                KEY `idx_status` (`status`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Stripe Dispute Webhook 事件流水表';

              ALTER TABLE `mm_stripe_dispute`
              ADD COLUMN `fund_withdraw_status` VARCHAR(256) NOT NULL DEFAULT 'FUND_WITHDRAW_STATUS_UNSPECIFIED' COMMENT 'enum:payment.v1.DisputeModel.FundWithdrawStatus',
              ADD COLUMN `last_handled_time` BIGINT NOT NULL DEFAULT 0 COMMENT 'The last processed webhook_create_time',
              ADD COLUMN `dispute_fee` BIGINT NOT NULL DEFAULT 0 COMMENT 'Dispute 手续费',
              ADD UNIQUE INDEX `mm_stripe_dispute_uniq_idx_dispute_id` (`dispute_id`),
              DROP INDEX `mm_stripe_dispute_dispute_id_index`;

              CREATE TABLE `mm_stripe_dispute_fund_flow` (
                `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
                `dispute_id` VARCHAR(256) NOT NULL COMMENT 'com.stripe.model.Dispute#id',
                `business_id` BIGINT default 0                 not null COMMENT 'business_id',
                `company_id` BIGINT DEFAULT 0 not null comment 'company_id',
                `currency` varchar(16) NOT NULL COMMENT 'com.stripe.model.Dispute#currency',
                `dispute_amount` BIGINT NOT NULL COMMENT 'com.stripe.model.Dispute#amount',
                `dispute_fee` BIGINT NOT NULL COMMENT 'Dispute 手续费',
                `total_amount` BIGINT NOT NULL COMMENT '总金额',
                `fund_type` VARCHAR(32) NOT NULL COMMENT '资金操作类型: reinstated - 撤回, withdrawn - 扣除',
                `transaction_id` VARCHAR(256) NOT NULL DEFAULT '' COMMENT '在 stripe 侧的 trx_id，如果是 withdraw 则为 charge id，如果是reinstate 则为 transfer id',
                `operated_time` TIMESTAMP COMMENT '资金完成操作的时间',
                `status` VARCHAR(32) NOT NULL DEFAULT 'init' COMMENT '任务状态: init - 待处理, success - 处理完成',
                `connected_account` VARCHAR(256) NOT NULL COMMENT '需要charge的connected_account',
                `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
                `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
                PRIMARY KEY (`id`),
                KEY `idx_status` (`status`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Stripe Dispute fund flow 资金表';


  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              CREATE TABLE `mm_stripe_dispute_event_log` (
                `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '流水表自增ID',
                `event_id` VARCHAR(255) NOT NULL COMMENT 'com.stripe.model.Event#id',
                `event_type` VARCHAR(255) NOT NULL COMMENT 'com.stripe.model.Event#type',
                `webhook_created_time` BIGINT UNSIGNED NOT NULL COMMENT 'com.stripe.model.Event#created',
                `webhook_body` TEXT NOT NULL COMMENT 'com.stripe.model.Event#data',
                `dispute_id` VARCHAR(255) NOT NULL COMMENT 'com.stripe.model.Dispute#id',
                `dispute_status` VARCHAR(256) NOT NULL COMMENT 'com.stripe.model.Dispute#status',
                `status` VARCHAR(256) NOT NULL COMMENT '流水表状态: init（待处理），success（处理成功）',
                `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '流水表记录创建时间',
                `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '流水表记录更新时间',
                PRIMARY KEY (`id`),
                UNIQUE KEY `
                uniq_event_id` (`event_id`),
                KEY `idx_status` (`status`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Stripe Dispute Webhook 事件流水表';

              ALTER TABLE `mm_stripe_dispute`
              ADD COLUMN `fund_withdraw_status` VARCHAR(256) NOT NULL DEFAULT 'FUND_WITHDRAW_STATUS_UNSPECIFIED' COMMENT 'enum:payment.v1.DisputeModel.FundWithdrawStatus',
              ADD COLUMN `last_handled_time` BIGINT NOT NULL DEFAULT 0 COMMENT 'The last processed webhook_create_time',
              ADD COLUMN `dispute_fee` BIGINT NOT NULL DEFAULT 0 COMMENT 'Dispute 手续费',
              ADD UNIQUE INDEX `mm_stripe_dispute_uniq_idx_dispute_id` (`dispute_id`),
              DROP INDEX `mm_stripe_dispute_dispute_id_index`;

              CREATE TABLE `mm_stripe_dispute_fund_flow` (
                `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '自增ID',
                `dispute_id` VARCHAR(256) NOT NULL COMMENT 'com.stripe.model.Dispute#id',
                `business_id` BIGINT default 0                 not null COMMENT 'business_id',
                `company_id` BIGINT DEFAULT 0 not null comment 'company_id',
                `currency` varchar(16) NOT NULL COMMENT 'com.stripe.model.Dispute#currency',
                `dispute_amount` BIGINT NOT NULL COMMENT 'com.stripe.model.Dispute#amount',
                `dispute_fee` BIGINT NOT NULL COMMENT 'Dispute 手续费',
                `total_amount` BIGINT NOT NULL COMMENT '总金额',
                `fund_type` VARCHAR(32) NOT NULL COMMENT '资金操作类型: reinstated - 撤回, withdrawn - 扣除',
                `transaction_id` VARCHAR(256) NOT NULL DEFAULT '' COMMENT '在 stripe 侧的 trx_id，如果是 withdraw 则为 charge id，如果是reinstate 则为 transfer id',
                `operated_time` TIMESTAMP COMMENT '资金完成操作的时间',
                `status` VARCHAR(32) NOT NULL DEFAULT 'init' COMMENT '任务状态: init - 待处理, success - 处理完成',
                `connected_account` VARCHAR(256) NOT NULL COMMENT '需要charge的connected_account',
                `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
                `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
                PRIMARY KEY (`id`),
                KEY `idx_status` (`status`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Stripe Dispute fund flow 资金表';

