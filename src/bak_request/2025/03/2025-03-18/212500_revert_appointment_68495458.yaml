name: "revert appointment 68495458"
description: "revert appointment 68495458"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              update moe_grooming.moe_grooming_appointment
              set is_deprecate = 0
              where id = 68495458;
