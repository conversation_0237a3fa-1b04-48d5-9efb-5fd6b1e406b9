name: "New Invoice 支持 Refund By Item"
description: "New Invoice 支持 Refund By Item"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              ---- tips split table 
              DROP TABLE IF EXISTS tips_split;

              CREATE TABLE IF NOT EXISTS tips_split
              (
                  id                     BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
                  company_id             BIGINT         DEFAULT 0                     NOT NULL,
                  business_id            BIGINT         DEFAULT 0                     NOT NULL,
                  source_id              BIGINT         DEFAULT 0                     NOT NULL, 
                  source_type            TEXT           DEFAULT ''                    NOT NULL,
                  split_config           JSONB          DEFAULT '{}'                  NOT NULL,
                  collected_order_ids    JSONB          DEFAULT '[]'                  NOT NULL,
                  apply_by               BIGINT         DEFAULT 0                     NOT NULL,
                  collected_tips         NUMERIC(20, 2) DEFAULT 0.00                  NOT NULL, 
                  currency_code          TEXT           DEFAULT ''                    NOT NULL,
                  create_time            TIMESTAMP      DEFAULT now()                 NOT NULL,
                  update_time            TIMESTAMP      DEFAULT now()                 NOT NULL
              );

              CREATE INDEX tips_split_idx_source_id_type ON tips_split (source_id, source_type);

              CREATE TRIGGER auto_update_tips_split_update_time
                  AFTER UPDATE
                  ON public.tips_split
                  FOR EACH ROW
              EXECUTE PROCEDURE update_modified_column();

              DROP TABLE IF EXISTS tips_split_detail;


              CREATE TABLE IF NOT EXISTS tips_split_detail
              (
                  id                     BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
                  business_id            BIGINT         DEFAULT 0                     NOT NULL,
                  company_id             BIGINT         DEFAULT 0                     NOT NULL,
                  staff_id               BIGINT         DEFAULT 0                     NOT NULL,
                  tips_split_id          BIGINT         DEFAULT 0                     NOT NULL,
                  split_amount           NUMERIC(20, 2) DEFAULT 0.00                  NOT NULL, 
                  currency_code          TEXT           DEFAULT ''                    NOT NULL,
                  create_time            TIMESTAMP      DEFAULT now()                 NOT NULL,
                  update_time            TIMESTAMP      DEFAULT now()                 NOT NULL
              );

              -- create index from gtip split id
              CREATE INDEX tips_split_detail_idx_tips_split_id ON tips_split_detail (tips_split_id);


              CREATE TRIGGER auto_update_tips_split_detail_update_time
                  AFTER UPDATE
                  ON public.tips_split_detail
                  FOR EACH ROW
              EXECUTE PROCEDURE update_modified_column();        
        - execute_sql:
            database: "moego_permission"
            sql: |
              -- database: moego_permission 新增权限
              INSERT INTO public."permission" (ID, "name",display_name,description,parent_permission_id,category_id) VALUES
              (301,'addExtraTips','Add extra tips','',102,1),
              (302,'addExtraItems','Add extra items','',102,1);

              -- 新权限初始化：
              insert into public."role_permission_mapping" (role_id, permission_id) 
              select id, 301 from role where deleted_at is null;
              insert into public."role_permission_mapping" (role_id, permission_id) 
              select id, 302 from role where deleted_at is null;
