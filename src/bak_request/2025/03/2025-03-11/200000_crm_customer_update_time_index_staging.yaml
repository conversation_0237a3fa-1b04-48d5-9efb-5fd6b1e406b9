name: "add update time index for customer table"
description: "https://moego.atlassian.net/browse/CRM-2894"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_customer"
            sql: |
              ALTER TABLE moe_customer.moe_business_customer ADD INDEX customer_update_time (company_id, update_time);
