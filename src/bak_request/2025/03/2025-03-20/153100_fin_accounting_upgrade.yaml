name: "subscription upgrade accounting"
description: "subscription upgrade accounting"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_subscription"
            sql: |
              UPDATE subscription
              SET total_price = 99, billing_cycle_value = 1, plan_product_id = 145
              WHERE id = 681;

              INSERT INTO public.entitlement (license_id, product_id, feature_id, feature_key, feature_name, feature_description,
                                            feature_setting, created_at, updated_at, version)
              VALUES (681, 145, 1, 'ACCOUNTING_BASIC', DEFAULT, DEFAULT, '{"onOff":{"on":true}}', DEFAULT, DEFAULT, DEFAULT);

              UPDATE entitlement
              SET product_id = 145, feature_id = 2, feature_key = 'ACCOUNTING_FULL_SERVICE'
              WHERE id = 212;