name: "add central demo subscription"
description: "related slack: https://moegoworkspace.slack.com/archives/C08EA8YC2AV/p1740722093879519"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_membership"
            sql: |
              INSERT INTO "public"."membership_perk" (entitlement_id, membership_id, validity_start_time, validity_end_time, created_at, updated_at)
              VALUES
              (54779, 984, now(), now() + interval '30 days', now(), now()),
              (54780, 984, now(), now() + interval '30 days', now(), now()),
              (54781, 984, now(), now() + interval '30 days', now(), now()),
              (54785, 984, now(), now() + interval '30 days', now(), now()),
              (54786, 984, now(), now() + interval '30 days', now(), now()),
              (54788, 984, now(), now() + interval '30 days', now(), now()),
              (54793, 984, now(), now() + interval '30 days', now(), now()),
              (54795, 984, now(), now() + interval '30 days', now(), now()),
              (54783, 984, now(), now() + interval '30 days', now(), now()),
              (54784, 984, now(), now() + interval '30 days', now(), now()),
              (54790, 984, now(), now() + interval '30 days', now(), now()),
              (54778, 984, now(), now() + interval '30 days', now(), now()),
              (54787, 984, now(), now() + interval '30 days', now(), now()),
              (54791, 984, now(), now() + interval '30 days', now(), now()),
              (54794, 984, now(), now() + interval '30 days', now(), now()),
              (54782, 984, now(), now() + interval '30 days', now(), now()),
              (54792, 984, now(), now() + interval '30 days', now(), now()),
              (54789, 984, now(), now() + interval '30 days', now(), now()),
              (54796, 984, now(), now() + interval '30 days', now(), now());
  
  
  

              INSERT INTO "public"."membership_perk" (entitlement_id, membership_id, validity_start_time, validity_end_time, created_at, updated_at)
              VALUES
              (54800, 983, now(), now() + interval '30 days', now(), now()),
              (54801, 983, now(), now() + interval '30 days', now(), now()),
              (54803, 983, now(), now() + interval '30 days', now(), now()),
              (54806, 983, now(), now() + interval '30 days', now(), now()),
              (54797, 983, now(), now() + interval '30 days', now(), now()),
              (54808, 983, now(), now() + interval '30 days', now(), now()),
              (54810, 983, now(), now() + interval '30 days', now(), now()),
              (54812, 983, now(), now() + interval '30 days', now(), now()),
              (54802, 983, now(), now() + interval '30 days', now(), now()),
              (54804, 983, now(), now() + interval '30 days', now(), now()),
              (54805, 983, now(), now() + interval '30 days', now(), now()),
              (54815, 983, now(), now() + interval '30 days', now(), now()),
              (54811, 983, now(), now() + interval '30 days', now(), now()),
              (54798, 983, now(), now() + interval '30 days', now(), now()),
              (54809, 983, now(), now() + interval '30 days', now(), now()),
              (54799, 983, now(), now() + interval '30 days', now(), now()),
              (54807, 983, now(), now() + interval '30 days', now(), now()),
              (54813, 983, now(), now() + interval '30 days', now(), now()),
              (54814, 983, now(), now() + interval '30 days', now(), now());






