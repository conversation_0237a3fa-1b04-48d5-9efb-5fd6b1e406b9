name: "add central demo subscription"
description: "related slack: https://moegoworkspace.slack.com/archives/C08EA8YC2AV/p1740722093879519"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_subscription"
            sql: |
              INSERT INTO "public"."license" (subscription_id, owner_id, inherit_path, owner_type, created_at, updated_at, status)
              VALUES 
                (1048, 18792117, ''::ltree, 'CUSTOMER', now(), now(), 'VALID'),
                (1064, 18792498, ''::ltree, 'CUSTOMER', now(), now(), 'VALID'),
                (1063, 18792465, ''::ltree, 'CUSTOMER', now(), now(), 'VALID'),
                (1058, 18792288, ''::ltree, '<PERSON>USTOME<PERSON>', now(), now(), 'VALID'),
                (1054, 18792192, ''::ltree, 'CUSTOMER', now(), now(), 'VALID'),
                (1049, 18792118, ''::ltree, 'CUSTOMER', now(), now(), 'VALID'),
                (1061, 18792431, ''::ltree, 'CUSTOMER', now(), now(), 'VALID'),
                (1062, 18792438, ''::ltree, 'CUSTOMER', now(), now(), 'VALID'),
                (1050, 18792128, ''::ltree, 'CUSTOMER', now(), now(), 'VALID'),
                (1065, 18792507, ''::ltree, 'CUSTOMER', now(), now(), 'VALID'),
                (1051, 18792147, ''::ltree, 'CUSTOMER', now(), now(), 'VALID'),
                (1052, 18792150, ''::ltree, 'CUSTOMER', now(), now(), 'VALID'),
                (1057, 18792286, ''::ltree, 'CUSTOMER', now(), now(), 'VALID'),
                (1059, 18792291, ''::ltree, 'CUSTOMER', now(), now(), 'VALID'),
                (1053, 18792171, ''::ltree, 'CUSTOMER', now(), now(), 'VALID'),
                (1060, 18792312, ''::ltree, 'CUSTOMER', now(), now(), 'VALID'),
                (1056, 18792275, ''::ltree, 'CUSTOMER', now(), now(), 'VALID'),
                (1066, 18792511, ''::ltree, 'CUSTOMER', now(), now(), 'VALID'),
                (1055, 18792197, ''::ltree, 'CUSTOMER', now(), now(), 'VALID');
              
              INSERT INTO "public"."license" (subscription_id, owner_id, inherit_path, owner_type, created_at, updated_at, status)
              VALUES
              (1045, 18792905, ''::ltree, 'CUSTOMER', now(), now(), 'VALID'),
              (1034, 18792704, ''::ltree, 'CUSTOMER', now(), now(), 'VALID'),
              (1042, 18792785, ''::ltree, 'CUSTOMER', now(), now(), 'VALID'),
              (1040, 18792755, ''::ltree, 'CUSTOMER', now(), now(), 'VALID'),
              (1035, 18792732, ''::ltree, 'CUSTOMER', now(), now(), 'VALID'),
              (1036, 18792727, ''::ltree, 'CUSTOMER', now(), now(), 'VALID'),
              (1032, 18792686, ''::ltree, 'CUSTOMER', now(), now(), 'VALID'),
              (1037, 18792737, ''::ltree, 'CUSTOMER', now(), now(), 'VALID'),
              (1046, 18792913, ''::ltree, 'CUSTOMER', now(), now(), 'VALID'),
              (1043, 18792853, ''::ltree, 'CUSTOMER', now(), now(), 'VALID'),
              (1039, 18792740, ''::ltree, 'CUSTOMER', now(), now(), 'VALID'),
              (1044, 18792867, ''::ltree, 'CUSTOMER', now(), now(), 'VALID'),
              (1041, 18792778, ''::ltree, 'CUSTOMER', now(), now(), 'VALID'),
              (1033, 18792696, ''::ltree, 'CUSTOMER', now(), now(), 'VALID'),
              (1030, 18792625, ''::ltree, 'CUSTOMER', now(), now(), 'VALID'),
              (1038, 18792738, ''::ltree, 'CUSTOMER', now(), now(), 'VALID'),
              (1031, 18792652, ''::ltree, 'CUSTOMER', now(), now(), 'VALID'),
              (1047, 18792918, ''::ltree, 'CUSTOMER', now(), now(), 'VALID'),
              (1029, 18792621, ''::ltree, 'CUSTOMER', now(), now(), 'VALID');

