name: "add central demo subscription"
description: "related slack: https://moegoworkspace.slack.com/archives/C08EA8YC2AV/p1740722093879519"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_subscription"
            sql: |
              INSERT INTO "public"."entitlement" (license_id, product_id, feature_id, feature_key, feature_name, feature_description, feature_setting, created_at, updated_at, version)
              VALUES
              (55606, 986, 888, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"5"}}'::json, now(), now(), 1),
              (55595, 986, 888, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"5"}}'::json, now(), now(), 1),
              (55605, 986, 888, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"5"}}'::json, now(), now(), 1),
              (55604, 986, 888, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"5"}}'::json, now(), now(), 1),
              (55600, 986, 888, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"5"}}'::json, now(), now(), 1),
              (55603, 986, 888, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"5"}}'::json, now(), now(), 1),
              (55594, 986, 888, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"5"}}'::json, now(), now(), 1),
              (55593, 986, 888, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"5"}}'::json, now(), now(), 1),
              (55608, 986, 888, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"5"}}'::json, now(), now(), 1),
              (55602, 986, 888, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"5"}}'::json, now(), now(), 1),
              (55592, 986, 888, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"5"}}'::json, now(), now(), 1),
              (55610, 986, 888, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"5"}}'::json, now(), now(), 1),
              (55607, 986, 888, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"5"}}'::json, now(), now(), 1),
              (55601, 986, 888, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"5"}}'::json, now(), now(), 1),
              (55598, 986, 888, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"5"}}'::json, now(), now(), 1),
              (55596, 986, 888, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"5"}}'::json, now(), now(), 1),
              (55609, 986, 888, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"5"}}'::json, now(), now(), 1),
              (55599, 986, 888, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"5"}}'::json, now(), now(), 1),
              (55597, 986, 888, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"5"}}'::json, now(), now(), 1);

              INSERT INTO "public"."entitlement" (license_id, product_id, feature_id, feature_key, feature_name, feature_description, feature_setting, created_at, updated_at, version)
              VALUES
              (55587, 985, 887, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"4"}}'::json, now(), now(), 1),
              (55582, 985, 887, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"4"}}'::json, now(), now(), 1),
              (55589, 985, 887, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"4"}}'::json, now(), now(), 1),
              (55590, 985, 887, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"4"}}'::json, now(), now(), 1),
              (55575, 985, 887, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"4"}}'::json, now(), now(), 1),
              (55585, 985, 887, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"4"}}'::json, now(), now(), 1),
              (55581, 985, 887, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"4"}}'::json, now(), now(), 1),
              (55574, 985, 887, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"4"}}'::json, now(), now(), 1),
              (55579, 985, 887, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"4"}}'::json, now(), now(), 1),
              (55584, 985, 887, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"4"}}'::json, now(), now(), 1),
              (55591, 985, 887, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"4"}}'::json, now(), now(), 1),
              (55583, 985, 887, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"4"}}'::json, now(), now(), 1),
              (55586, 985, 887, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"4"}}'::json, now(), now(), 1),
              (55580, 985, 887, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"4"}}'::json, now(), now(), 1),
              (55578, 985, 887, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"4"}}'::json, now(), now(), 1),
              (55577, 985, 887, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"4"}}'::json, now(), now(), 1),
              (55588, 985, 887, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"4"}}'::json, now(), now(), 1),
              (55573, 985, 887, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"4"}}'::json, now(), now(), 1),
              (55576, 985, 887, 'MEMBERSHIP_SERVICE_ADDON_QUANTITY', '121391-1563563-SERVICE', '121391-1563563-SERVICE', '{"count":{"totalAmount":"4"}}'::json, now(), now(), 1);
