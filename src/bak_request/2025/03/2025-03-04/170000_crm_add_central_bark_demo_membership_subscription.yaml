name: "add central demo subscription"
description: "related slack: https://moegoworkspace.slack.com/archives/C08EA8YC2AV/p1740722093879519"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_membership"
            sql: |
              INSERT INTO "public"."subscription" (internal_subscription_id, company_id, business_id, customer_id, membership_id, membership_revision, created_at, updated_at, auto_resume_setting)
              VALUES
              (1041, 121391, 986, 18792778, 984, 1, now(), now(), '{}'::json),
              (1033, 121391, 986, 18792696, 984, 1, now(), now(), '{}'::json),
              (1030, 121391, 986, 18792625, 984, 1, now(), now(), '{}'::json),
              (1038, 121391, 986, 18792738, 984, 1, now(), now(), '{}'::json),
              (1043, 121391, 986, 18792853, 984, 1, now(), now(), '{}'::json),
              (1039, 121391, 986, 18792740, 984, 1, now(), now(), '{}'::json),
              (1044, 121391, 986, 18792867, 984, 1, now(), now(), '{}'::json),
              (1032, 121391, 986, 18792686, 984, 1, now(), now(), '{}'::json),
              (1037, 121391, 986, 18792737, 984, 1, now(), now(), '{}'::json),
              (1046, 121391, 986, 18792913, 984, 1, now(), now(), '{}'::json),
              (1031, 121391, 986, 18792652, 984, 1, now(), now(), '{}'::json),
              (1047, 121391, 986, 18792918, 984, 1, now(), now(), '{}'::json),
              (1029, 121391, 986, 18792621, 984, 1, now(), now(), '{}'::json),
              (1045, 121391, 986, 18792905, 984, 1, now(), now(), '{}'::json),
              (1034, 121391, 986, 18792704, 984, 1, now(), now(), '{}'::json),
              (1042, 121391, 986, 18792785, 984, 1, now(), now(), '{}'::json),
              (1040, 121391, 986, 18792755, 984, 1, now(), now(), '{}'::json),
              (1035, 121391, 986, 18792732, 984, 1, now(), now(), '{}'::json),
              (1036, 121391, 986, 18792727, 984, 1, now(), now(), '{}'::json);

              INSERT INTO "public"."subscription" (internal_subscription_id, company_id, business_id, customer_id, membership_id, membership_revision, created_at, updated_at, auto_resume_setting)
              VALUES
              (1049, 121391, 985, 18792118, 983, 1, now(), now(), '{}'::json),
              (1061, 121391, 985, 18792431, 983, 1, now(), now(), '{}'::json),
              (1053, 121391, 985, 18792171, 983, 1, now(), now(), '{}'::json),
              (1060, 121391, 985, 18792312, 983, 1, now(), now(), '{}'::json),
              (1056, 121391, 985, 18792275, 983, 1, now(), now(), '{}'::json),
              (1066, 121391, 985, 18792511, 983, 1, now(), now(), '{}'::json),
              (1055, 121391, 985, 18792197, 983, 1, now(), now(), '{}'::json),
              (1063, 121391, 985, 18792465, 983, 1, now(), now(), '{}'::json),
              (1058, 121391, 985, 18792288, 983, 1, now(), now(), '{}'::json),
              (1054, 121391, 985, 18792192, 983, 1, now(), now(), '{}'::json),
              (1062, 121391, 985, 18792438, 983, 1, now(), now(), '{}'::json),
              (1050, 121391, 985, 18792128, 983, 1, now(), now(), '{}'::json),
              (1065, 121391, 985, 18792507, 983, 1, now(), now(), '{}'::json),
              (1051, 121391, 985, 18792147, 983, 1, now(), now(), '{}'::json),
              (1052, 121391, 985, 18792150, 983, 1, now(), now(), '{}'::json),
              (1057, 121391, 985, 18792286, 983, 1, now(), now(), '{}'::json),
              (1059, 121391, 985, 18792291, 983, 1, now(), now(), '{}'::json),
              (1048, 121391, 985, 18792117, 983, 1, now(), now(), '{}'::json),
              (1064, 121391, 985, 18792498, 983, 1, now(), now(), '{}'::json);
  
  
  
  
  


