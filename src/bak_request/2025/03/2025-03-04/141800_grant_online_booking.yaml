name: "Grant permission to <PERSON>"
description: "grant permission to <PERSON>"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_online_booking"
            username: "${db.user.admin}"
            sql: |
              GRANT CONNECT ON DATABASE moego_online_booking TO developer_freeman;
              GRANT USAGE ON SCHEMA public TO developer_freeman;
              GRANT SELECT, UPDATE ON ALL TABLES IN SCHEMA public TO developer_freeman;
              ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, UPDATE ON TABLES TO developer_freeman;
