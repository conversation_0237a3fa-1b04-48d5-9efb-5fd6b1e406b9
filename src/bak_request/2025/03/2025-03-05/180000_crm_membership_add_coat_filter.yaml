name: "membership add pet filter"
description: "related slack: https://moegoworkspace.slack.com/archives/C04BP8LLKM3/p1740780445733639"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_membership"
            sql: |
              ALTER TABLE "public"."membership"
              ADD COLUMN "coat_filter" bool NOT NULL DEFAULT false,
              ADD COLUMN "pet_size_filter" bool NOT NULL DEFAULT false;

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_membership"
            sql: |
              ALTER TABLE "public"."membership"
              ADD COLUMN "coat_filter" bool NOT NULL DEFAULT false,
              ADD COLUMN "pet_size_filter" bool NOT NULL DEFAULT false;



