name: "membership add pet filter"
description: "related slack: https://moegoworkspace.slack.com/archives/C04BP8LLKM3/p1740780445733639"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_membership"
            sql: |
              CREATE TABLE membership_breed_binding (
                id BIGSERIAL PRIMARY KEY,
                company_id BIGINT NOT NULL,
                membership_id BIGINT NOT NULL,
                pet_type_id BIGINT NOT NULL,
                is_all BOOLEAN NOT NULL DEFAULT FALSE,
                breed_name_list TEXT[],
                created_at TIMESTAMP NOT NULL DEFAULT now(),
                updated_at TIMESTAMP NOT NULL DEFAULT now(),
                deleted_at TIMESTAMP NULL
              );
              
              CREATE INDEX idx_cid_mid ON public.membership_breed_binding (company_id, membership_id);
              
              ALTER TABLE "public"."membership"
              ADD COLUMN "breed_filter" bool NOT NULL DEFAULT false,
              ADD COLUMN "allowed_pet_size_list" int8[] DEFAULT ARRAY[]::int8[],
              ADD COLUMN "allowed_pet_coat_list" int8[] DEFAULT ARRAY[]::int8[],
              ADD COLUMN "coat_filter" bool NOT NULL DEFAULT false,
              ADD COLUMN "pet_size_filter" bool NOT NULL DEFAULT false;





  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_membership"
            sql: |
              CREATE TABLE membership_breed_binding (
                id BIGSERIAL PRIMARY KEY,
                company_id BIGINT NOT NULL,
                membership_id BIGINT NOT NULL,
                pet_type_id BIGINT NOT NULL,
                is_all BOOLEAN NOT NULL DEFAULT FALSE,
                breed_name_list TEXT[],
                created_at TIMESTAMP NOT NULL DEFAULT now(),
                updated_at TIMESTAMP NOT NULL DEFAULT now(),
                deleted_at TIMESTAMP NULL
              );
              
              CREATE INDEX idx_cid_mid ON public.membership_breed_binding (company_id, membership_id);
              
              ALTER TABLE "public"."membership"
              ADD COLUMN "breed_filter" bool NOT NULL DEFAULT false,
              ADD COLUMN "allowed_pet_size_list" int8[] DEFAULT ARRAY[]::int8[],
              ADD COLUMN "allowed_pet_coat_list" int8[] DEFAULT ARRAY[]::int8[],
              ADD COLUMN "coat_filter" bool NOT NULL DEFAULT false,
              ADD COLUMN "pet_size_filter" bool NOT NULL DEFAULT false;



