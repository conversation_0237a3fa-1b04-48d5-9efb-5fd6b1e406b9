name: "accounting bundle sale: add show_accounting"
description: "add field 'show_accounting', FIN-2352"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              ALTER TABLE moe_platform_sales
              ADD COLUMN show_accounting tinyint  default 0 not null comment '是否显示accounting入口 0:不显示 1:显示';

              ALTER TABLE moe_platform_care
              ADD COLUMN show_accounting tinyint default 0 not null comment '是否显示accounting入口 0:不显示 1:显示';

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              ALTER TABLE moe_platform_sales
              ADD COLUMN show_accounting tinyint  default 0 not null comment '是否显示accounting入口 0:不显示 1:显示';

              ALTER TABLE moe_platform_care
              ADD COLUMN show_accounting tinyint default 0 not null comment '是否显示accounting入口 0:不显示 1:显示';



