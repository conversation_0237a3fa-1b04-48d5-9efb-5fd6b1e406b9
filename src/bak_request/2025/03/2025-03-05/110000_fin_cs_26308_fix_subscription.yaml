name: "CS 26308 fix subscription"
description: "CS 26308 fix subscription"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              -- updatePermissionForPlan (begin_date & expire_date comes from Stripe Subscription)
              UPDATE moe_payment.moe_company_permission_state SET
                update_time = UNIX_TIMESTAMP(), business_num = 1, package_msg_num = 900, package_msg_used_num = 2
                WHERE id = 4821;
              -- addSwitchOrderLog
              INSERT INTO moe_payment.moe_company_plan_order (
                company_id, stripe_subscriptions_id, plan_id, price, create_time, update_time, expire_date, buy_type, operator_id
              )
              VALUES (
                106042, 'sub_1MgEjhIZwcIFVLGrKIPOuu3H', 102, 99, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1743112797, 1, 1
              );
              -- savePricingChangeRecord
              INSERT INTO moe_payment.moe_pricing_change_record (
                company_id, stripe_subscriptions_id, plan_id, record_type, external_id, create_time, change_time
              ) VALUES (
                106042, 'sub_1MgEjhIZwcIFVLGrKIPOuu3H', 102, 2, 4821, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()
              );

