name: "给furry land 一批角色授权"
description: "给furry land 一批角色授权"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_permission"
            username: "${db.user.admin}"
            sql: |
              GRANT USAGE, SELECT ON SEQUENCE role_permission_mapping_id_seq TO developer_haozhi;
        - execute_sql:
            database: "moego_permission"
            sql: |
              INSERT INTO "public"."role_permission_mapping" 
              ("role_id", "permission_id", "selected_permission_scope_index", "scope_extra_param", "selected_sub_permission_scope_index")
              SELECT role_id, perm_id, 0, NULL, 0
              FROM (
                  SELECT id as role_id, 299 AS perm_id FROM "public"."role" WHERE id in (64358,64344,67569,64368,64388,64357,64407,64392,64394,64380,64345,64403,64386,64365,64369,64348,64373,64390,64375,64396,64342,64401,64354,67571,64364,64405,64360,64367,64338,64379,64372,64352,64393,64400,64339,64366,64384,64395,64399,64340,64362,64410,64353,64355,64349,70907,64397,64398,64341,64404,64356,64408,64377,64382,64374,64383,64347,64389,64361,64378,64363,64391,64385,64359,64343,64387,64370,64409,64350,64371,64402,64381,64351,67570,64346)
              ) AS new_data
              ON CONFLICT ("role_id", "permission_id") DO NOTHING;

              INSERT INTO "public"."role_permission_mapping" 
              ("role_id", "permission_id", "selected_permission_scope_index", "scope_extra_param", "selected_sub_permission_scope_index")
              SELECT role_id, perm_id, 0, NULL, 0
              FROM (
                  SELECT id as role_id, 300 AS perm_id FROM "public"."role" WHERE id in (64358,64344,67569,64368,64388,64357,64407,64392,64394,64380,64345,64403,64386,64365,64369,64348,64373,64390,64375,64396,64342,64401,64354,67571,64364,64405,64360,64367,64338,64379,64372,64352,64393,64400,64339,64366,64384,64395,64399,64340,64362,64410,64353,64355,64349,70907,64397,64398,64341,64404,64356,64408,64377,64382,64374,64383,64347,64389,64361,64378,64363,64391,64385,64359,64343,64387,64370,64409,64350,64371,64402,64381,64351,67570,64346)
              ) AS new_data
              ON CONFLICT ("role_id", "permission_id") DO NOTHING;


