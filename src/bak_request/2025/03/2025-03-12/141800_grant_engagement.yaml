name: "Grant marketing to ark"
description: "grant marketing to ark"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_marketing"
            username: "${db.user.admin}"
            sql: |
              GRANT CONNECT ON DATABASE moego_marketing TO developer_ark;
              GRANT USAGE ON SCHEMA public TO developer_ark;
              GRANT INSERT, SELECT, UPDATE ON ALL TABLES IN SCHEMA public TO developer_ark;
              ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, UPDATE ON TABLES TO developer_ark;
