name: "price book"
description: "price book"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              	ALTER TABLE `moe_grooming`.`moe_grooming_service` 
                ADD COLUMN `source`  tinyint NOT NULL DEFAULT 1 COMMENT '1-MoeGo Platform 2-Enterprise Hub';
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_enterprise"
            sql: |
              	ALTER TABLE service 
                ADD COLUMN service_type text default 'SERVICE_TYPE_UNSPECIFIED' not null;

                ALTER TABLE service 
                ADD COLUMN images jsonb default '[]' not null;

                ALTER TABLE service_category 
                ADD COLUMN service_type text default 'SERVICE_TYPE_UNSPECIFIED' not null;
                
                ALTER TABLE service_change 
                ADD COLUMN service_before text NOT NULL DEFAULT '{}'::text;
                
                ALTER TABLE service_change 
                ADD COLUMN service_after text NOT NULL DEFAULT '{}'::text;

                CREATE INDEX idx_service_enterprise_id ON service (enterprise_id);
                CREATE INDEX idx_service_change_enterprise_id ON service_change (enterprise_id);
                CREATE INDEX idx_service_category_enterprise_id ON service_category (enterprise_id);
                CREATE INDEX idx_service_category_id ON service (category_id);

                CREATE TABLE "public"."service_template_mapping" (
                  "id" bigserial,
                  "template_type" varchar(255) NOT NULL DEFAULT 'TYPE_UNSPECIFIED'::text,
                  "template_id" int8 NOT NULL DEFAULT 0,
                  "target_company_id" int8 NOT NULL DEFAULT 0,
                  "target_id" int8 NOT NULL DEFAULT 0,
                  "created_at" timestamp NOT NULL DEFAULT now(),
                  "updated_at" timestamp NOT NULL DEFAULT now(),
                  PRIMARY KEY ("id")
                );
                CREATE TRIGGER update_at
                  BEFORE UPDATE
                  ON service_template_mapping
                  FOR EACH ROW
                EXECUTE PROCEDURE update_at();

                CREATE UNIQUE INDEX "idx_template_type_template_id" ON "public"."service_template_mapping" USING btree (
                  "template_type",
                  "template_id",
                  "target_company_id"
                );

                COMMENT ON COLUMN "public"."service_template_mapping"."template_type" IS 'api/moego/models/offering/v1/service_enum.proto:ServiceItemType';

                
 


                
                
