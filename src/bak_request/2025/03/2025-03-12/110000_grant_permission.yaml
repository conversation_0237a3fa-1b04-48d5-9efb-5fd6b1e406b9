name: "grant permission"
description: "grant permission"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_enterprise"
            sql: |
              ALTER DEFAULT PRIVILEGES IN SCHEMA public
              GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO developer_haozhi;

              ALTER DEFAULT PRIVILEGES IN SCHEMA public
              GRANT USAGE, SELECT, UPDATE ON SEQUENCES TO developer_haozhi;

              GRANT USAGE, SELECT, UPDATE ON SEQUENCE service_category_id_seq,service_change_history_id_seq,
              service_change_id_seq,service_id_seq TO developer_haozhi;
              
              GRANT SELECT, INSERT, UPDATE, DELETE ON service,service_change_history,service_change,service_category TO developer_haozhi;
