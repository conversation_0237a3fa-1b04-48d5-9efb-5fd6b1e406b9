name: "Add service charge detail to appointment"
description: "Add service charge detail to appointment"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              create table service_charge_detail
              (
              id                  bigint auto_increment
              primary key,
              appointment_id      bigint     default 0                 not null,
              service_charge_id   bigint     default 0                 not null comment 'foreign key to service_charge table',
              price               numeric(20, 2)                       not null comment 'price of the service charge',
              currency            varchar(3) default 'USD'             not null comment 'currency of the price',
              price_override_type tinyint    default 0                 not null comment '0-no override, 1-override by business',
              tax_id              integer    default 0                 not null comment 'foreign key to moe_business_tax table',
              created_at          timestamp  default CURRENT_TIMESTAMP null,
              updated_at          timestamp  default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP,
              deleted_at          timestamp  default null
              );

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              create table service_charge_detail
              (
              id                  bigint auto_increment
              primary key,
              appointment_id      bigint     default 0                 not null,
              service_charge_id   bigint     default 0                 not null comment 'foreign key to service_charge table',
              price               numeric(20, 2)                       not null comment 'price of the service charge',
              currency            varchar(3) default 'USD'             not null comment 'currency of the price',
              price_override_type tinyint    default 0                 not null comment '0-no override, 1-override by business',
              tax_id              integer    default 0                 not null comment 'foreign key to moe_business_tax table',
              created_at          timestamp  default CURRENT_TIMESTAMP null,
              updated_at          timestamp  default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP,
              deleted_at          timestamp  default null
              );
