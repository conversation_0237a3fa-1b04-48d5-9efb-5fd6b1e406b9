name: "porting number"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_message"
            sql: |
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 117882;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 117245;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 117875;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 117886;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 118278;
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_engagement"
            sql: |
              update forward_business set to_phone_number = '+***********' where business_id = 117882;
              update forward_business set to_phone_number = '+***********' where business_id = 117245;
              update forward_business set to_phone_number = '+***********' where business_id = 117875;
              update forward_business set to_phone_number = '+***********' where business_id = 117886;
              update forward_business set to_phone_number = '+***********' where business_id = 118278;
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_sms"
            sql: |
              update business_sms_setting set twilio_number = '+***********' where business_id = 117882;
              update business_sms_setting set twilio_number = '+***********' where business_id = 117245;
              update business_sms_setting set twilio_number = '+***********' where business_id = 117875;
              update business_sms_setting set twilio_number = '+***********' where business_id = 117886;
              update business_sms_setting set twilio_number = '+***********' where business_id = 118278;
