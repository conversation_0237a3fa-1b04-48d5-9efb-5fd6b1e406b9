name: "porting number"
description: "https://moego.atlassian.net/browse/CS-26161"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_message"
            sql: |
              INSERT INTO moe_message.moe_forward_business (to_phone_number, company_id, business_id)
              VALUES ('+***********', 111950, 112316);