name: "refinance"
description: "related slack: https://moegoworkspace.slack.com/archives/C07EN0YBPT3/p1745604587807039"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              UPDATE loan_offer
              SET offer_status = 'REPLACED',
              channel_offer_status = 'REFINANCED',
              version = 3,
              updated_time = CURRENT_TIMESTAMP()
              WHERE offer_id = 'PLO2MCA0000002183ad955737d005b1c4' and version = 2;
              INSERT INTO moe_payment.loan_offer_update_log (offer_id, channel_offer_id, before_offer_status, cur_offer_status,
                                                            before_channel_offer_status, cur_channel_offer_status, cur_version,
                                                            before_remain_amount, cur_remain_amount, event_type, event_id,
                                                            created_time, updated_time, before_processing_amount,
                                                            after_processing_amount, before_channel_offer_id)
              VALUES ('PLO2MCA0000002183ad955737d005b1c4', '55eb92fa-6ac0-4b35-8b4c-69b1f8df265c', 'PAID_OUT', 'REPLACED',
                      'CURRENT', 'REFINANCED', 3, 41495.21, 41495.21, 'refinanced by chi', NOW(), DEFAULT, DEFAULT, 0.00, 0.00,
                      '55eb92fa-6ac0-4b35-8b4c-69b1f8df265c');