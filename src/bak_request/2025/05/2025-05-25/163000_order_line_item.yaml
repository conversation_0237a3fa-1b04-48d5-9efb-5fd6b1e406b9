name: "Add order_line_item_id column to service_charge_detail table"
description: "Add order_line_item_id column to service_charge_detail table"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-grooming"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              alter table moe_grooming.service_charge_detail
              add column order_line_item_id bigint NOT NULL DEFAULT '0' COMMENT 'Order line item id, 0 if not created from order';

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-grooming"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              alter table moe_grooming.service_charge_detail
              add column order_line_item_id bigint NOT NULL DEFAULT '0' COMMENT 'Order line item id, 0 if not created from order';
