stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              update
                moe_grooming.moe_business_book_online
              set payment_option_map = '{
                "2": {
                  "prePay": {
                    "prepayType": 1,
                    "depositType": 1,
                    "depositAmount": 0,
                    "depositPercentage": 50
                  },
                  "paymentType": 2
                }
              }'
              where business_id in (122772, 123084, 123085);
