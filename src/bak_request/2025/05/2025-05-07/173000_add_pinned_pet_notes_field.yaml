name: "add pinned pet notes field"
description: "add pinned pet notes field"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_customer"
            sql: |
              ALTER TABLE moe_pet_note ADD COLUMN is_pinned BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否置顶';
              ALTER TABLE moe_pet_note ADD COLUMN pinned_at BIGINT NOT NULL DEFAULT '0' COMMENT '置顶时间';

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_customer"
            sql: |
              ALTER TABLE moe_pet_note ADD COLUMN is_pinned BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否置顶';
              ALTER TABLE moe_pet_note ADD COLUMN pinned_at BIGINT NOT NULL DEFAULT '0' COMMENT '置顶时间';