name: "fix membership data"
description: "fix membership data"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_subscription"
            sql: |
              update entitlement set feature_setting= '{"count":{"usedAmount":"0", "totalAmount":"1"}}' where id = 80655 and license_id = 81558;
