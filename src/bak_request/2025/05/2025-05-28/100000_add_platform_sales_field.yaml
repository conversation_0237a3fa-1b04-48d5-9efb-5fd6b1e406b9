name: "add opportunity_id for platform sales"
description: "add opportunity_id for platform sales"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              ALTER TABLE `moe_platform_sales` 
              ADD COLUMN `opportunity_id` varchar(32) not null DEFAULT ''; 
              
              show create table `moe_platform_sales`;

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: "AWS_RDS"
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              ALTER TABLE `moe_platform_sales` 
              ADD COLUMN `opportunity_id` varchar(32) not null DEFAULT ''; 
              
              show create table `moe_platform_sales`;