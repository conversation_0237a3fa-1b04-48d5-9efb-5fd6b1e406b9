name: "add custom_rate_approval_status for platform sales"
description: "add custom_rate_approval_status for platform sales"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              ALTER TABLE `moe_platform_sales` 
              ADD COLUMN `custom_rate_approval_status` int not null DEFAULT 1 
              comment '1: ignored, 2: pending, 3: approved, 4: rejected';
              
              show create table `moe_platform_sales`;

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: "AWS_RDS"
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              ALTER TABLE `moe_platform_sales` 
              ADD COLUMN `custom_rate_approval_status` int not null DEFAULT 1 
              comment '1: ignored, 2: pending, 3: approved, 4: rejected';
              
              show create table `moe_platform_sales`;