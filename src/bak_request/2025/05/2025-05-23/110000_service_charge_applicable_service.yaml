name: "新增 service_charge 表 service_filter 字段"
description: "新增 service_charge 表 service_filter 字段"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              ALTER TABLE service_charge
              ADD COLUMN enable_service_filter boolean NOT NULL DEFAULT false;
              
              COMMENT ON COLUMN service_charge.enable_service_filter IS 'Indicates if service filtering is enabled. When true, refer to service_filter_rules for details';
              
              ALTER TABLE service_charge
              ADD COLUMN service_filter_rules JSONB DEFAULT '[]'::JSONB;
            
              COMMENT ON COLUMN service_charge.service_filter_rules IS 'Filter rules for service types and service IDs. Contains: service_item_type, available_for_all_services, available_service_id_list';

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: "AWS_RDS"
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              ALTER TABLE service_charge
              ADD COLUMN enable_service_filter boolean NOT NULL DEFAULT false;
              
              COMMENT ON COLUMN service_charge.enable_service_filter IS 'Indicates if service filtering is enabled. When true, refer to service_filter_rules for details';
              
              ALTER TABLE service_charge
              ADD COLUMN service_filter_rules JSONB DEFAULT '[]'::JSONB;
              
              COMMENT ON COLUMN service_charge.service_filter_rules IS 'Filter rules for service types and service IDs. Contains: service_item_type, available_for_all_services, available_service_id_list';
