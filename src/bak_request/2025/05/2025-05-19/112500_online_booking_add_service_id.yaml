name: "Add service_id column to evaluation_test_detail"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_online_booking"
            sql: |
              alter table public.evaluation_test_detail
                add service_id bigint default 0 not null;

              comment on column public.evaluation_test_detail.service_id is 'evaluation 绑定的 service id';

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_online_booking"
            sql: |
              alter table public.evaluation_test_detail
                add service_id bigint default 0 not null;
              
              comment on column public.evaluation_test_detail.service_id is 'evaluation 绑定的 service id';
