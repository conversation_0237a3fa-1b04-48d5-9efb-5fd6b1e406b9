name: "refinance"
description: "related slack: https://moegoworkspace.slack.com/archives/C07EN0YBPT3/p1747434557630969"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_message"
            sql: |
              insert into moe_message_template_placeholder
                (placeholder_group, placeholder_name, placeholder_text, use_case, sort_order)
              values
                ('Appointment','Next appointment pet & service category','{nextAppointmentPet&ServiceCategory}',90,70),
                ('Appointment','Last appointment pet & service category','{lastAppointmentPet&ServiceCategory}',90,90),
                ('Appointment','Pet & service category','{pet&ServiceCategory}',1,80),
                ('Appointment','Pet & service category','{pet&ServiceCategory}',2,80),
                ('Appointment','Pet & service category','{pet&ServiceCategory}',3,80),
                ('Appointment','Pet & service category','{pet&ServiceCategory}',4,80),
                ('Appointment','Pet & service category','{pet&ServiceCategory}',5,80),
                ('Appointment','Pet & service category','{pet&ServiceCategory}',6,80),
                ('Appointment','Pet & service category','{pet&ServiceCategory}',7,80),
                ('Appointment','Pet & service category','{pet&ServiceCategory}',8,80),
                ('Appointment','Pet & service category','{pet&ServiceCategory}',9,80),
                ('Appointment','Pet & service category','{pet&ServiceCategory}',10,80),
                ('Appointment','Pet & service category','{pet&ServiceCategory}',11,80),
                ('Appointment','Pet & service category','{pet&ServiceCategory}',13,80),
                ('Appointment','Pet & service category','{pet&ServiceCategory}',14,80),
                ('Appointment','Pet & service category','{pet&ServiceCategory}',15,80),
                ('Appointment','Pet & service category','{pet&ServiceCategory}',91,70),
                ('Appointment','Pet & service category','{pet&ServiceCategory}',92,70);
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_message"
            sql: |
              insert into moe_message_template_placeholder
                (placeholder_group, placeholder_name, placeholder_text, use_case, sort_order)
              values
                ('Appointment','Next appointment pet & service category','{nextAppointmentPet&ServiceCategory}',90,70),
                ('Appointment','Last appointment pet & service category','{lastAppointmentPet&ServiceCategory}',90,90),
                ('Appointment','Pet & service category','{pet&ServiceCategory}',1,80),
                ('Appointment','Pet & service category','{pet&ServiceCategory}',2,80),
                ('Appointment','Pet & service category','{pet&ServiceCategory}',3,80),
                ('Appointment','Pet & service category','{pet&ServiceCategory}',4,80),
                ('Appointment','Pet & service category','{pet&ServiceCategory}',5,80),
                ('Appointment','Pet & service category','{pet&ServiceCategory}',6,80),
                ('Appointment','Pet & service category','{pet&ServiceCategory}',7,80),
                ('Appointment','Pet & service category','{pet&ServiceCategory}',8,80),
                ('Appointment','Pet & service category','{pet&ServiceCategory}',9,80),
                ('Appointment','Pet & service category','{pet&ServiceCategory}',10,80),
                ('Appointment','Pet & service category','{pet&ServiceCategory}',11,80),
                ('Appointment','Pet & service category','{pet&ServiceCategory}',13,80),
                ('Appointment','Pet & service category','{pet&ServiceCategory}',14,80),
                ('Appointment','Pet & service category','{pet&ServiceCategory}',15,80),
                ('Appointment','Pet & service category','{pet&ServiceCategory}',91,70),
                ('Appointment','Pet & service category','{pet&ServiceCategory}',92,70);
