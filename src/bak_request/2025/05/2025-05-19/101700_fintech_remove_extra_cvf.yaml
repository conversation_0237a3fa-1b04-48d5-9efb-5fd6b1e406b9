name: "移除多余 CVFee CS-29204 Incident-67"
description: "移除多余 CVFee CS-29204 Incident-67，遗漏了 2 个单"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              INSERT INTO public.order_line_extra_fee(business_id, order_id, order_item_id, apply_type, is_deleted, fee_type, amount, collect_type, description, apply_by, create_time, update_time) VALUES (107492, 146296180, 0, 'none', false, 'convenience fee', 2.16, 'subtract', '', 0, NOW(), NOW());

              UPDATE public."order" SET 
                  total_amount = total_amount - 2.16,
                  extra_fee_amount = extra_fee_amount - 2.16,
                  remain_amount = GREATEST(0, remain_amount - 2.16),
                  update_time = NOW()
              WHERE business_id = 107492 AND id = 146296180;


              INSERT INTO public.order_line_extra_fee(business_id, order_id, order_item_id, apply_type, is_deleted, fee_type, amount, collect_type, description, apply_by, create_time, update_time) VALUES (107492, 145529431, 0, 'none', false, 'convenience fee', 5.16, 'subtract', '', 0, NOW(), NOW());

              UPDATE public."order" SET 
                  total_amount = total_amount - 5.16,
                  extra_fee_amount = extra_fee_amount - 5.16,
                  remain_amount = GREATEST(0, remain_amount - 5.16),
                  update_time = NOW()
              WHERE business_id = 107492 AND id = 145529431;
