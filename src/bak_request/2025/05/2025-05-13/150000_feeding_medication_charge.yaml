name: "Add is_new_order field to moe_grooming_appointment table"
description: "Add is_new_order field to moe_grooming_appointment table"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-mysql-grooming
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              alter table moe_grooming.appointment_pet_medication
              add date_type tinyint default 2 not null comment '1-EVERYDAY_EXCEPT_CHECKOUT_DATE; 2-EVERYDAY_INCLUDE_CHECKOUT_DATE; 3-SPECIFIC_DATE';
              
              alter table moe_grooming.appointment_pet_medication
              add specific_dates varchar(1024) default '[]' not null comment 'specific_date, yyyy-mm-dd';
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              alter table public.service_charge
                add charge_method smallint default 2 not null;
              
              comment on column public.service_charge.charge_method is '1-PER_DAY; 2-PER_ADMINISTRATION';
              
              alter table public.service_charge
              add food_source_ids int8[] default '{}' not null;
              
              alter table public.service_charge
              add is_all_food_source boolean default false not null;
              
              comment on column public.service_charge.is_all_food_source is 'is all food source';
              
              comment on column public.service_charge.food_source_ids is 'applicable food source id';
              
              comment on column public.service_charge.surcharge_type is 'surcharge type, 1-off hours fee, 2-customer fee, 3-charge 24-hour, 4-feeding fee, 5-medication fee';



  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-grooming
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              alter table moe_grooming.appointment_pet_medication
              add date_type tinyint default 2 not null comment '1-EVERYDAY_EXCEPT_CHECKOUT_DATE; 2-EVERYDAY_INCLUDE_CHECKOUT_DATE; 3-SPECIFIC_DATE';
              
              alter table moe_grooming.appointment_pet_medication
              add specific_dates varchar(1024) default '[]' not null comment 'specific_date, yyyy-mm-dd';

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              alter table public.service_charge
                add charge_method smallint default 2 not null;
              
              comment on column public.service_charge.charge_method is '1-PER_DAY; 2-PER_ADMINISTRATION';
              
              alter table public.service_charge
              add food_source_ids int8[] default '{}' not null;
              
              alter table public.service_charge
              add is_all_food_source boolean default false not null;
              
              comment on column public.service_charge.is_all_food_source is 'is all food source';
              
              comment on column public.service_charge.food_source_ids is 'applicable food source id';
              
              comment on column public.service_charge.surcharge_type is 'surcharge type, 1-off hours fee, 2-customer fee, 3-charge 24-hour, 4-feeding fee, 5-medication fee';
