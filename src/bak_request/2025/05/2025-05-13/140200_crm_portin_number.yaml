name: "porting number"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_message"
            sql: |
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 118325;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 113870;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 112767;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 117882;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 116028;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 117245;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 112316;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 113606;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 115637;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 117875;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 112135;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 112549;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 112696;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 112475;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 112324;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 112768;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 117886;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 113939;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 116155;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 112125;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 112057;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 115851;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 113233;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 112769;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 112342;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 112332;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 114219;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 118278;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 119350;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 114090;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 116905;
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_engagement"
            sql: |
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 118055, 118325);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 113457, 113870);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 112395, 112767);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 117456, 117882);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 115547, 116028);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 116706, 117245);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 111950, 112316);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 113198, 113606);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 115174, 115637);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 117450, 117875);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 111773, 112135);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 112178, 112549);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 112324, 112696);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 112104, 112475);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 111958, 112324);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 112396, 112768);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 117460, 117886);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 113523, 113939);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 115662, 116155);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 111763, 112125);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 111696, 112057);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 115376, 115851);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 112842, 113233);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 112397, 112769);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 111976, 112342);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 111966, 112332);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 113789, 114219);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 118010, 118278);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 119050, 119350);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 113670, 114090);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 116383, 116905);
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_sms"
            sql: |
              update business_sms_setting set twilio_number = '+***********' where business_id = 118325;
              update business_sms_setting set twilio_number = '+***********' where business_id = 113870;
              update business_sms_setting set twilio_number = '+***********' where business_id = 112767;
              update business_sms_setting set twilio_number = '+***********' where business_id = 117882;
              update business_sms_setting set twilio_number = '+***********' where business_id = 116028;
              update business_sms_setting set twilio_number = '+***********' where business_id = 117245;
              update business_sms_setting set twilio_number = '+***********' where business_id = 112316;
              update business_sms_setting set twilio_number = '+***********' where business_id = 113606;
              update business_sms_setting set twilio_number = '+***********' where business_id = 115637;
              update business_sms_setting set twilio_number = '+***********' where business_id = 117875;
              update business_sms_setting set twilio_number = '+***********' where business_id = 112135;
              update business_sms_setting set twilio_number = '+***********' where business_id = 112549;
              update business_sms_setting set twilio_number = '+***********' where business_id = 112696;
              update business_sms_setting set twilio_number = '+***********' where business_id = 112475;
              update business_sms_setting set twilio_number = '+***********' where business_id = 112324;
              update business_sms_setting set twilio_number = '+***********' where business_id = 112768;
              update business_sms_setting set twilio_number = '+***********' where business_id = 117886;
              update business_sms_setting set twilio_number = '+***********' where business_id = 113939;
              update business_sms_setting set twilio_number = '+***********' where business_id = 116155;
              update business_sms_setting set twilio_number = '+***********' where business_id = 112125;
              update business_sms_setting set twilio_number = '+***********' where business_id = 112057;
              update business_sms_setting set twilio_number = '+***********' where business_id = 115851;
              update business_sms_setting set twilio_number = '+***********' where business_id = 113233;
              update business_sms_setting set twilio_number = '+***********' where business_id = 112769;
              update business_sms_setting set twilio_number = '+***********' where business_id = 112342;
              update business_sms_setting set twilio_number = '+***********' where business_id = 112332;
              update business_sms_setting set twilio_number = '+***********' where business_id = 114219;
              update business_sms_setting set twilio_number = '+***********' where business_id = 118278;
              update business_sms_setting set twilio_number = '+***********' where business_id = 119350;
              update business_sms_setting set twilio_number = '+***********' where business_id = 114090;
              update business_sms_setting set twilio_number = '+***********' where business_id = 116905;
