name: "修复订单tips amount"
description: "修复订单数据 CS-29261"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              UPDATE "order" 
              SET tips_amount = 42, total_amount = 252, update_time = NOW()
              WHERE id = 144890681;
              
              UPDATE "order"
              SET tips_amount = 19, total_amount = 114, update_time = NOW()
              WHERE id = 145569502;
