name: "add fields for platform sales"
description: "add fields for platform sales"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              ALTER TABLE `moe_platform_sales`
              ADD COLUMN `tier` varchar(32) NULL DEFAULT NULL,
              ADD COLUMN `spif` decimal(10,4) NULL DEFAULT NULL;

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: "AWS_RDS"
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              ALTER TABLE `moe_platform_sales`
              ADD COLUMN `tier` varchar(32) NULL DEFAULT NULL,
              ADD COLUMN `spif` decimal(10,4) NULL DEFAULT NULL;