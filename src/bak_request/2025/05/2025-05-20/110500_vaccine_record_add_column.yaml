name: "moe_pet_pet_vaccine_binding add column"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_customer"
            sql: |
              ALTER TABLE `moe_customer`.`moe_pet_pet_vaccine_binding`
              ADD COLUMN `source` tinyint NOT NULL DEFAULT 0 COMMENT '1 - ver verifi' AFTER `type`,
              ADD COLUMN `verify_status` tinyint NOT NULL DEFAULT 0 COMMENT '1 - <PERSON>LEAR 2 - NOT CLEAR' AFTER `source`;
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_customer"
            sql: |
              ALTER TABLE `moe_customer`.`moe_pet_pet_vaccine_binding`
              ADD COLUMN `source` tinyint NOT NULL DEFAULT 0 COMMENT '1 - ver verifi' AFTER `type`,
              ADD COLUMN `verify_status` tinyint NOT NULL DEFAULT 0 COMMENT '1 - CLEAR 2 - NOT CLEAR' AFTER `source`;