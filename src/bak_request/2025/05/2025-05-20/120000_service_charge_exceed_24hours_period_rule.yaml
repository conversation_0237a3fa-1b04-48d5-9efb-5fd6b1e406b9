name: "新增 service_charge 表 service_filter 字段 && 新增 service_charge_applicable_service 表"
description: "新增 service_charge 表 service_filter 字段 && 新增 service_charge_applicable_service 表"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              ALTER TABLE service_charge
              ADD COLUMN time_based_pricing_type SMALLINT NOT NULL DEFAULT 1;
              
              ALTER TABLE service_charge
              ADD COLUMN multiple_pets_charge_type SMALLINT NOT NULL DEFAULT 1;
              
              ALTER TABLE service_charge
              ADD COLUMN hourly_exceed_rules JSONB DEFAULT '[]'::JSONB;
              
              COMMENT ON COLUMN service_charge.time_based_pricing_type IS 'Billing type: 1=Fixed rate, 2=Tiered rate';
              COMMENT ON COLUMN service_charge.multiple_pets_charge_type IS 'Multiple pets billing method: 1=Same charge per pet, 2=Different charge for additional pets';
              COMMENT ON COLUMN service_charge.hourly_exceed_rules IS 'List of 24-hour cycle rules, in JSON format. Each object contains fields such as id, fee_name, hour, base_price, additional_pet_price';

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: "AWS_RDS"
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              ALTER TABLE service_charge
              ADD COLUMN time_based_pricing_type SMALLINT NOT NULL DEFAULT 1;
              
              ALTER TABLE service_charge
              ADD COLUMN multiple_pets_charge_type SMALLINT NOT NULL DEFAULT 1;
              
              ALTER TABLE service_charge
              ADD COLUMN hourly_exceed_rules JSONB DEFAULT '[]'::JSONB;
              
              COMMENT ON COLUMN service_charge.time_based_pricing_type IS 'Billing type: 1=Fixed rate, 2=Tiered rate';
              COMMENT ON COLUMN service_charge.multiple_pets_charge_type IS 'Multiple pets billing method: 1=Same charge per pet, 2=Different charge for additional pets';
              COMMENT ON COLUMN service_charge.hourly_exceed_rules IS 'List of 24-hour cycle rules, in JSON format. Each object contains fields such as id, fee_name, hour, base_price, additional_pet_price';
