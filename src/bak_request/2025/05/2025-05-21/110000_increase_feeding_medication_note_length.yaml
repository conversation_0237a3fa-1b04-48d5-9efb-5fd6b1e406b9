name: "Increase pet feeding and medication note length"
description: "Increase pet feeding and medication note length"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-mysql-grooming
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              alter table appointment_pet_feeding
                modify column feeding_note text comment 'feeding note, user input';
              
              alter table appointment_pet_medication
              modify column medication_note text comment 'medication note, user input';

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_customer"
            sql: |
              alter table moe_customer.pet_feeding
                modify column feeding_note text comment 'feeding note, user input';
              
              alter table moe_customer.pet_medication
              modify column medication_note text comment 'medication note, user input';

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-grooming
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              alter table appointment_pet_feeding
                modify column feeding_note text comment 'feeding note, user input';
              
              alter table appointment_pet_medication
                modify column medication_note text comment 'medication note, user input';

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_customer"
            sql: |
              alter table moe_customer.pet_feeding
                modify column feeding_note text comment 'feeding note, user input';
  
              alter table moe_customer.pet_medication
                modify column medication_note text comment 'medication note, user input';
