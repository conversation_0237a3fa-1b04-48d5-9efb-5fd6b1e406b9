name: "add table vet_verify_task"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_enterprise"
            sql: |
              create table vet_verify_task
              (
                  id                bigint generated always as identity primary key,
                  name              text           default ''              not null,
                  status            text           default 'INIT'          not null,
                  attempts          int            default 0               not null,
                  created_at        timestamp      default now()           not null,
                  updated_at        timestamp      default now()           not null,
                  deleted_at        timestamp      default null
              );
              create index if not exists idx_vet_verify_task_name on vet_verify_task using btree (name);

              CREATE TRIGGER update_at
                  BEFORE UPDATE
                  ON vet_verify_task
                  FOR EACH ROW
              EXECUTE PROCEDURE update_at();

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_enterprise"
            sql: |
              create table vet_verify_task
              (
                  id                bigint generated always as identity primary key,
                  name              text           default ''              not null,
                  status            text           default 'INIT'          not null,
                  attempts          int            default 0               not null,
                  created_at        timestamp      default now()           not null,
                  updated_at        timestamp      default now()           not null,
                  deleted_at        timestamp      default null
              );
              create index if not exists idx_vet_verify_task_name on vet_verify_task using btree (name);

              CREATE TRIGGER update_at
                  BEFORE UPDATE
                  ON vet_verify_task
                  FOR EACH ROW
              EXECUTE PROCEDURE update_at();