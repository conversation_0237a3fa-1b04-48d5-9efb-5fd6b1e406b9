name: "Portin PB 1"
description: "Portin PB 1"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_message"
            sql: |
              insert into moe_message.moe_forward_business(to_phone_number, business_id, company_id)
              values ('+***********', 119424, 119114);
              insert into moe_message.moe_forward_business(to_phone_number, business_id, company_id)
              values ('+***********', 117833, 117408);
