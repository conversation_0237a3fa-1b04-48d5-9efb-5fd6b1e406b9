name: "online booking medication add date_type and specific_dates"
description: "online booking medication add date_type and specific_dates"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_online_booking"
            sql: |
              alter table public.medication
                add date_type smallint default 2 not null;

              comment on column public.medication.date_type is '1-EVERYDAY_EXCEPT_CHECKOUT_DATE; 2-EVERYDAY_INCLUDE_CHECKOUT_DATE; 3-SPECIFIC_DATE';

              alter table public.medication
                add specific_dates jsonb default '[]'::jsonb not null;

              comment on column public.medication.specific_dates is 'specific_date list, yyyy-mm-dd';


  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_online_booking"
            sql: |
              alter table public.medication
                add date_type smallint default 2 not null;

              comment on column public.medication.date_type is '1-EVERYDAY_EXCEPT_CHECKOUT_DATE; 2-EVERYDAY_INCLUDE_CHECKOUT_DATE; 3-SPECIFIC_DATE';

              alter table public.medication
                add specific_dates jsonb default '[]'::jsonb not null;

              comment on column public.medication.specific_dates is 'specific_date list, yyyy-mm-dd';
