name: "Assign PN 1"
description: "Assign PN 1"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_message"
            sql: |
              insert into moe_message.moe_forward_business(to_phone_number, business_id, company_id)
              values ('+***********', 123220, 122713);
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_sms"
            sql: |
              update business_sms_setting set twilio_number = '+***********' where business_id = 123220;
              