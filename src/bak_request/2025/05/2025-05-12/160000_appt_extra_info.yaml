name: "Add appointment extra info table"
description: "Add appointment extra info table"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-mysql-grooming
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              create table moe_grooming.appointment_extra_info
              (
              id             bigint auto_increment primary key,
              appointment_id bigint  default 0     not null comment 'The appointment ID',
              is_new_order   boolean default false not null comment 'Whether the appointment was created by a new order process'
              );
              
              create unique index uni_appointment on moe_grooming.appointment_extra_info (appointment_id);

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-grooming
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              create table moe_grooming.appointment_extra_info
              (
              id             bigint auto_increment primary key,
              appointment_id bigint  default 0     not null comment 'The appointment ID',
              is_new_order   boolean default false not null comment 'Whether the appointment was created by a new order process'
              );
              
              create unique index uni_appointment on moe_grooming.appointment_extra_info (appointment_id);
