name: "refinance"
description: "related slack: https://moegoworkspace.slack.com/archives/C07EN0YBPT3/p1746816245354689?thread_ts=1745947908.015519&cid=C07EN0YBPT3"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              UPDATE loan_offer
              SET offer_status = 'REPLACED',
              channel_offer_status = 'REFINANCED',
              version = 1668,
              updated_time = CURRENT_TIMESTAMP()
              WHERE offer_id = 'PLO2MCA1117852181a69899370551c78b4' and version = 1667;
              
              INSERT INTO moe_payment.loan_offer_update_log (offer_id, channel_offer_id, before_offer_status, cur_offer_status,
                                                            before_channel_offer_status, cur_channel_offer_status, cur_version,
                                                            before_remain_amount, cur_remain_amount, event_type, event_id,
                                                            created_time, updated_time, before_processing_amount,
                                                            after_processing_amount, before_channel_offer_id)
              VALUES ('PLO2MCA1117852181a69899370551c78b4', '401c6b58-471b-4c89-9ff3-13dbec0d15dd', 'PAID_OUT', 'REPLACED',
                      'CURRENT', 'REFINANCED', 1668, 6944.88, 6944.88, 'refinanced by chi', NOW(), DEFAULT, DEFAULT, 0.00, 0.00,
                      '401c6b58-471b-4c89-9ff3-13dbec0d15dd');