name: "Add is_new_order field to moe_grooming_appointment table"
description: "Add is_new_order field to moe_grooming_appointment table"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-mysql-grooming
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              alter table moe_grooming.moe_grooming_appointment
              add column
              is_new_order boolean default false not null comment 'Whether the appointment was created by a new order process';


  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-grooming
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              alter table moe_grooming.moe_grooming_appointment
              add column
              is_new_order boolean default false not null comment 'Whether the appointment was created by a new order process';
