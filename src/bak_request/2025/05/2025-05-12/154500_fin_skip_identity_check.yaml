name: "skip identity check"
description: "related slack: https://moegoworkspace.slack.com/archives/C06R0JU5NRK/p1746765948516699"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              INSERT INTO moe_payment.moe_stripe_allow_business
              (business_id, status, create_time, update_time, company_id)
              VALUES(122965, 1, unix_timestamp(), unix_timestamp(), 122356);
              
              INSERT INTO moe_payment.moe_stripe_allow_business
              (business_id, status, create_time, update_time, company_id)
              VALUES(122966, 1, unix_timestamp(), unix_timestamp(), 122356);
              
              INSERT INTO moe_payment.moe_stripe_allow_business
              (business_id, status, create_time, update_time, company_id)
              VALUES(122967, 1, unix_timestamp(), unix_timestamp(), 122356);