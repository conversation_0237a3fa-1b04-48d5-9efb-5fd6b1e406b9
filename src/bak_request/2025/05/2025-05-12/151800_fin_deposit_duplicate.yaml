name: "delete redundant deposit record"
description: "related slack: https://moegoworkspace.slack.com/archives/C01TT9K995M/p1746535938081489"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              delete from moe_grooming.moe_book_online_deposit where id = 111840;