name: "dml for membership perks"
description: "授权 membership 库权限给 ark"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_membership"
            username: "${db.user.admin}"
            sql: |
              GRANT USAGE, SELECT ON SEQUENCE membership_quantity_benefits_id_seq TO PUBLIC;
