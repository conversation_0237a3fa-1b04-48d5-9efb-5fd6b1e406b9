name: "Calendar view add new config"
description: "calendar view add new config"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_business"
            sql: |
              alter table moe_calendar add show_slot_location boolean default false not null comment '0 - hide, 1 - show slot location';
              alter table moe_calendar add show_slot_time boolean default false not null comment '0 - hide, 1 - show slot start time';

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_business"
            sql: |
              alter table moe_calendar add show_slot_location boolean default false not null comment '0 - hide, 1 - show slot location';
              alter table moe_calendar add show_slot_time boolean default false not null comment '0 - hide, 1 - show slot start time';