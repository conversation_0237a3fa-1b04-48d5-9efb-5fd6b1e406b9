name: "修复订单数据"
description: "修复订单数据 Datadog Incident #61"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              UPDATE "order" 
              SET tips_amount = 34.5, total_amount = 264.5, paid_amount= 264.5, remain_amount = 0, status = 1, payment_status = 'PAID', update_time = NOW()
              WHERE business_id = 118199
              AND id = 143423299
              ;
              
