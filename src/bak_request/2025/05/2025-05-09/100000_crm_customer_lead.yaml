name: "customer 添加 customer lead 字段"
description: "customer 添加 customer lead 字段"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_customer"
            sql: |
              CREATE TABLE `moe_customer_history_log` (
              `id` int NOT NULL AUTO_INCREMENT,
              `company_id` bigint NOT NULL,
              `business_id` bigint NOT NULL,
              `customer_id` bigint NOT NULL,
              `staff_id` bigint NOT NULL,
              `type` varchar(255) NOT NULL,
              `action` text NOT NULL,
              `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
              `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'update time',
              PRIMARY KEY (`id`)
              ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
              ALTER TABLE `moe_customer_history_log`
              ADD INDEX idx_customer_id (`customer_id`),
              ADD INDEX idx_type (`type`);
              
              CREATE TABLE `moe_customer_task` (
              `id` int NOT NULL AUTO_INCREMENT,
              `company_id` bigint NOT NULL,
              `business_id` bigint NOT NULL,
              `customer_id` bigint NOT NULL,
              `name` varchar(255) NOT NULL,
              `allocate_staff_id` bigint DEFAULT NULL,
              `complete_time` datetime DEFAULT NULL,
              `state` varchar(255) NOT NULL,
              `create_by` bigint NOT NULL,
              `update_by` bigint NOT NULL,
              `delete_by` bigint DEFAULT NULL,
              `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
              `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'update time',
              `delete_time` datetime DEFAULT NULL,
              PRIMARY KEY (`id`)
              ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
              
              ALTER TABLE `moe_customer_task`
              ADD INDEX idx_customer_id (`customer_id`);
