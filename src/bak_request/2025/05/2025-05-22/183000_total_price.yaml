name: "Add total_price, quantity, order_line_item_id columns to pet_detail and evaluation_service_detail tables"
description: "Add total_price, quantity, order_line_item_id columns to pet_detail and evaluation_service_detail tables"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-grooming"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              alter table moe_grooming.moe_grooming_pet_detail
                add column
                total_price decimal(10, 2) default 0.00 not null comment 'The total price of this service, affected by the inclusion of the pricing rule', ALGORITHM=INSTANT;
              
              alter table moe_grooming.moe_grooming_pet_detail
                add column
                quantity int default 1 not null comment 'The quantity of this selected service', ALGORITHM=INSTANT;
              
              alter table moe_grooming.moe_grooming_pet_detail
                add column
                  order_line_item_id bigint default 0 not null comment 'Order line item id, 0 if not created from order', ALGORITHM=INSTANT;
              
              alter table moe_grooming.evaluation_service_detail
                add column
                order_line_item_id bigint default 0 not null comment 'Order line item id, 0 if not created from order', ALGORITHM=INSTANT;
