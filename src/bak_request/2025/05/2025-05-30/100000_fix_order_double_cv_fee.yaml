name: "修复订单数据"
description: "修复订单数据 double cv fee ticket：https://moego.atlassian.net/browse/CS-29297"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              INSERT INTO public.order_line_extra_fee(business_id, order_id, order_item_id, apply_type, is_deleted, fee_type, amount, collect_type, description, apply_by, create_time, update_time) 
                VALUES (4672, 145620915, 0, 'none', false, 'convenience fee', 6.71, 'subtract', '', 0, NOW(), NOW());
              
              UPDATE public."order" SET 
                  total_amount = total_amount - 6.71,
                  extra_fee_amount = extra_fee_amount - 6.71,
                  remain_amount = GREATEST(0, remain_amount - 6.71),
                  update_time = NOW()
              WHERE business_id = 4672 AND id = 145620915;