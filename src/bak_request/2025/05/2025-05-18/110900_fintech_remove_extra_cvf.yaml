name: "移除多余 CVFee CS-29204 Incident-67"
description: "移除多余 CVFee CS-29204 Incident-67"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              INSERT INTO public.order_line_extra_fee(business_id, order_id, order_item_id, apply_type, is_deleted, fee_type, amount, collect_type, description, apply_by, create_time, update_time) VALUES (11955, 145344207, 0, 'none', false, 'convenience fee', 3.20, 'subtract', '', 0, NOW(), NOW());
              UPDATE public."order" SET 
                  total_amount = total_amount - 3.20,
                  extra_fee_amount = extra_fee_amount - 3.20,
                  remain_amount = GREATEST(0, remain_amount - 3.20),
                  update_time = NOW()
              WHERE business_id = 11955 AND id = 145344207;


              INSERT INTO public.order_line_extra_fee(business_id, order_id, order_item_id, apply_type, is_deleted, fee_type, amount, collect_type, description, apply_by, create_time, update_time) VALUES (120336, 146345461, 0, 'none', false, 'convenience fee', 3.31, 'subtract', '', 0, NOW(), NOW());
              INSERT INTO public.order_line_extra_fee(business_id, order_id, order_item_id, apply_type, is_deleted, fee_type, amount, collect_type, description, apply_by, create_time, update_time) VALUES (120336, 146345461, 0, 'none', false, 'convenience fee', 3.31, 'subtract', '', 0, NOW(), NOW());
              UPDATE public."order" SET 
                  total_amount = total_amount - 6.62,
                  extra_fee_amount = extra_fee_amount - 6.62,
                  remain_amount = GREATEST(0, remain_amount - 6.62),
                  update_time = NOW()
              WHERE business_id = 120336 AND id = 146345461;
