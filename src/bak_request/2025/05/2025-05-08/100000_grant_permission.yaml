name: "Grant permission"
description: "grant permission"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_offering"
            username: "${db.user.admin}"
            sql: |
              GRANT CONNECT ON DATABASE moego_offering TO developer_bob;
              GRANT USAGE ON SCHEMA public TO developer_bob;
              GRANT SELECT ON ALL TABLES IN SCHEMA public TO developer_bob;
              
              GRANT CONNECT ON DATABASE moego_offering TO developer_jason;
              GRANT USAGE ON SCHEMA public TO developer_jason;
              GRANT SELECT ON ALL TABLES IN SCHEMA public TO developer_jason;
              
              GRANT CONNECT ON DATABASE moego_offering TO developer_freeman;
              GRANT USAGE ON SCHEMA public TO developer_freeman;
              GRANT SELECT ON ALL TABLES IN SCHEMA public TO developer_freeman;

        - execute_sql:
            database: "moego_appointment"
            username: "${db.user.admin}"
            sql: |
              GRANT CONNECT ON DATABASE moego_appointment TO developer_bob;
              GRANT USAGE ON SCHEMA public TO developer_bob;
              GRANT SELECT ON ALL TABLES IN SCHEMA public TO developer_bob;
              
              GRANT CONNECT ON DATABASE moego_appointment TO developer_jason;
              GRANT USAGE ON SCHEMA public TO developer_jason;
              GRANT SELECT ON ALL TABLES IN SCHEMA public TO developer_jason;
              
              GRANT CONNECT ON DATABASE moego_appointment TO developer_freeman;
              GRANT USAGE ON SCHEMA public TO developer_freeman;
              GRANT SELECT ON ALL TABLES IN SCHEMA public TO developer_freeman;
