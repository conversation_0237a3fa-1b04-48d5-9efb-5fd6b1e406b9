name: "create table account_impersonate_approval_instance"
description: "create table account_impersonate_approval_instance"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_account"
            sql: |
              create table if not exists account_impersonate_approval_instance (
                instance_code        varchar(64)               not null primary key,
                impersonator         varchar(255)              not null,
                source               varchar(50)               not null,
                target_account_id    bigint                    not null,
                target_account_email varchar(255)              not null,
                max_age              bigint                    not null,
                status               varchar(20)               not null,
                created_at           timestamp with time zone  not null,
                approved_at          timestamp with time zone
              );
              
              create index account_impersonate_approval_instance_idx_approved ON account_impersonate_approval_instance(impersonator, source, approved_at) WHERE status = 'APPROVED';
              create index account_impersonate_approval_instance_idx_created ON account_impersonate_approval_instance(impersonator, source, status, created_at);

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_account"
            sql: |
              create table if not exists account_impersonate_approval_instance (
                instance_code        varchar(64)               not null primary key,
                impersonator         varchar(255)              not null,
                source               varchar(50)               not null,
                target_account_id    bigint                    not null,
                target_account_email varchar(255)              not null,
                max_age              bigint                    not null,
                status               varchar(20)               not null,
                created_at           timestamp with time zone  not null,
                approved_at          timestamp with time zone
              );
              
              create index account_impersonate_approval_instance_idx_approved ON account_impersonate_approval_instance(impersonator, source, approved_at) WHERE status = 'APPROVED';
              create index account_impersonate_approval_instance_idx_created ON account_impersonate_approval_instance(impersonator, source, status, created_at);
