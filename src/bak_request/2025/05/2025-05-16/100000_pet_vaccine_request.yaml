name: "add table pet_vaccine_request"
description: "add table pet_vaccine_request"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_customer"
            sql: |
              create table pet_vaccine_request
              (
                id                bigint auto_increment primary key,
                pet_id            bigint       default 0    not null,
                vaccine_record_id bigint       default 0    not null comment '0: add new record, >0: update existing record',
                vaccine_id        bigint       default 0    not null,
                expiration_date varchar(20)    default ''   not null,
                document_urls   varchar(2000)  default '[]' not null comment 'document urls, json array',
                status            int          default 1    not null comment '1: pending, 2: approved, 3: declined',
                create_time       bigint       default 0    not null comment 'second',
                update_time       bigint       default 0    not null comment 'second'
              );
              
              create index pet_id_idx on pet_vaccine_request (pet_id);
              create index vaccine_record_id_idx on pet_vaccine_request (vaccine_record_id);

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_customer"
            sql: |
              create table pet_vaccine_request
              (
                id                bigint auto_increment primary key,
                pet_id            bigint       default 0    not null,
                vaccine_record_id bigint       default 0    not null comment '0: add new record, >0: update existing record',
                vaccine_id        bigint       default 0    not null,
                expiration_date varchar(20)    default ''   not null,
                document_urls   varchar(2000)  default '[]' not null comment 'document urls, json array',
                status            int          default 1    not null comment '1: pending, 2: approved, 3: declined',
                create_time       bigint       default 0    not null comment 'second',
                update_time       bigint       default 0    not null comment 'second'
              );
              
              create index pet_id_idx on pet_vaccine_request (pet_id);
              create index vaccine_record_id_idx on pet_vaccine_request (vaccine_record_id);