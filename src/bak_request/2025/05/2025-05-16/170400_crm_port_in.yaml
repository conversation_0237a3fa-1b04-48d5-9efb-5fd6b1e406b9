name: "porting number"
description: ""
stopWhenError: true
requests:
  # origin number call forward
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_engagement"
            sql: |
              insert into forward_business(to_phone_number, company_id, business_id)
              VALUES ('+***********', 122146, 122624);