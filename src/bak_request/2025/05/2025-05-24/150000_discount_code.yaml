name: "Length discount code"
description: "Length discount code"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_marketing"
            sql: |
              alter table discount_code alter column discount_code type varchar(100) using discount_code::varchar(100);

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_marketing"
            sql: |
              alter table discount_code alter column discount_code type varchar(100) using discount_code::varchar(100);
