stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-grooming
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              update moe_grooming.moe_business_book_online
              set payment_option_map = '{
                "1": {
                  "paymentType": 2,
                  "prePay": {
                    "prepayType": 1,
                    "depositType": 0,
                    "depositPercentage": 0,
                    "depositAmount": 20.00
                  }
                }
              }'
              where business_id = 122947;
