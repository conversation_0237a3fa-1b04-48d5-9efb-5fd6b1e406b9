name: "customer 添加 customer lead 字段"
description: "customer 添加 customer lead 字段"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_customer"
            sql: |
              CREATE TABLE `moe_customer_action_state`
              (
              `id`          INT          NOT NULL AUTO_INCREMENT,
              `company_id`  BIGINT       NOT NULL,
              `business_id` BIGINT       NOT NULL,
              `created_by`  BIGINT       NOT NULL,
              `updated_by`  BIGINT       NOT NULL,
              `deleted_by`  BIGINT                DEFAULT NULL,
              `created_at`  DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
              `updated_at`  DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'update time',
              `deleted_at`  DATETIME              DEFAULT NULL,
              `name`        VARCHAR(255) NOT NULL,
              `sort`        INT          NOT NULL DEFAULT 0,
              `color`       VARCHAR(255) NOT NULL,
              PRIMARY KEY (`id`)
              ) ENGINE = InnoDB
              AUTO_INCREMENT = 1
              DEFAULT CHARSET = utf8mb4
              COLLATE = utf8mb4_0900_ai_ci;
              ALTER TABLE `moe_customer_action_state`
              ADD INDEX company_id (`company_id`);
              CREATE TABLE `moe_customer_life_cycle`
              (
              `id`          INT          NOT NULL AUTO_INCREMENT,
              `company_id`  BIGINT       NOT NULL,
              `business_id` BIGINT       NOT NULL,
              `created_by`  BIGINT       NOT NULL,
              `updated_by`  BIGINT       NOT NULL,
              `deleted_by`  BIGINT                DEFAULT NULL,
              `created_at`  DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
              `updated_at`  DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'update time',
              `deleted_at`  DATETIME              DEFAULT NULL,
              `name`        VARCHAR(255) NOT NULL,
              `sort`        INT          NOT NULL DEFAULT 0,
              `is_default`  INT          NOT NULL DEFAULT 0,
              PRIMARY KEY (`id`)
              ) ENGINE = InnoDB
              AUTO_INCREMENT = 1
              DEFAULT CHARSET = utf8mb4
              COLLATE = utf8mb4_0900_ai_ci;
              ALTER TABLE `moe_customer_life_cycle`
              ADD INDEX company_id (`company_id`);
