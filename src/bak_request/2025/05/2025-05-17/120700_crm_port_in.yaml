name: "porting number"
description: ""
stopWhenError: true
requests:
  # origin number message forward
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_message"
            sql: |
              UPDATE moe_message.moe_forward_business
              SET to_phone_number = '+***********'
              WHERE business_id = 119104;
