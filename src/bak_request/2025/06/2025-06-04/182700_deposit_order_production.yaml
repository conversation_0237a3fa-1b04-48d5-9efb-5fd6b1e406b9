name: "deposit order feature production"
description: "production ddl for deposit order"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              -------- order_line_item
              ALTER TABLE order_line_item
                  ADD external_uuid VARCHAR(100) DEFAULT '' NOT NULL;

              CREATE UNIQUE INDEX CONCURRENTLY order_line_item_uk_external_uuid
                  ON order_line_item (external_uuid)
                  WHERE external_uuid <> '';

              -------- order_item_price_detail
              CREATE TABLE IF NOT EXISTS order_item_price_detail
              (
                  id            BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
                  order_id      BIGINT         DEFAULT 0     NOT NULL,
                  order_item_id BIGINT         DEFAULT 0     NOT NULL,
                  name          VARCHAR(150)   DEFAULT ''    NOT NULL,
                  unit_price    DECIMAL(20, 2) DEFAULT 0.00  NOT NULL,
                  currency_code VARCHAR(3)     DEFAULT ''    NOT NULL,
                  quantity      INT            DEFAULT 0     NOT NULL,
                  subtotal      DECIMAL(20, 2) DEFAULT 0.00  NOT NULL,
                  operator      VARCHAR(50)    DEFAULT ''    NOT NULL,
                  create_time   TIMESTAMP      DEFAULT now() NOT NULL,
                  update_time   TIMESTAMP      DEFAULT now() NOT NULL
              );

              CREATE INDEX order_item_price_detail_idx_order_id ON order_item_price_detail (order_id);
              CREATE INDEX order_item_price_detail_idx_order_item_id ON order_item_price_detail (order_item_id);

              CREATE TRIGGER auto_update_order_item_price_detail_update_time
                  AFTER UPDATE
                  ON order_item_price_detail
                  FOR EACH ROW
              EXECUTE PROCEDURE update_modified_column();

              COMMENT ON TABLE order_item_price_detail IS 'Item subtotal detail';
              COMMENT ON COLUMN order_item_price_detail.id IS 'Primary key';
              COMMENT ON COLUMN order_item_price_detail.order_id IS 'Order ID';
              COMMENT ON COLUMN order_item_price_detail.order_item_id IS 'Order item ID';
              COMMENT ON COLUMN order_item_price_detail.name IS 'Price item name';
              COMMENT ON COLUMN order_item_price_detail.unit_price IS 'Unit price';
              COMMENT ON COLUMN order_item_price_detail.currency_code IS 'Currency Code';
              COMMENT ON COLUMN order_item_price_detail.quantity IS 'Quantity';
              COMMENT ON COLUMN order_item_price_detail.subtotal IS 'Subtotal, equal to (quantity * unit_price)';
              COMMENT ON COLUMN order_item_price_detail.operator IS 'ADD or SUBTRACT';
              COMMENT ON COLUMN order_item_price_detail.create_time IS 'Create time';
              COMMENT ON COLUMN order_item_price_detail.update_time IS 'Update time';

              -------- deposit order and deposit rule
              ALTER TABLE "order" ADD COLUMN deposit_amount NUMERIC(20, 2) DEFAULT 0.00 NOT NULL;
              COMMENT ON COLUMN "order".deposit_amount IS 'Deposit amount deducted in this order';

              ALTER TABLE refund_order ADD COLUMN refund_deposit_amount NUMERIC(20, 2) DEFAULT 0.00 NOT NULL;
              COMMENT ON COLUMN refund_order.refund_deposit_amount IS 'Deposit amount that deducted in the order and should be refunded';

              CREATE TABLE IF NOT EXISTS deposit_change_log
              (
                  id                  BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
                  deposit_order_id    BIGINT          DEFAULT 0       NOT NULL,
                  change_type         TEXT            DEFAULT ''      NOT NULL,
                  reason              TEXT            DEFAULT ''      NOT NULL,
                  dest_order_id       BIGINT          DEFAULT 0       NOT NULL,
                  changed_amount      NUMERIC(20, 2)  DEFAULT 0.00    NOT NULL,
                  balance             NUMERIC(20, 2)  DEFAULT 0.00    NOT NULL,
                  currency_code       TEXT            DEFAULT ''      NOT NULL,
                  previous_log_id     BIGINT          DEFAULT 0       NOT NULL,
                  company_id          BIGINT          DEFAULT 0       NOT NULL,
                  business_id         BIGINT          DEFAULT 0       NOT NULL,
                  customer_id         BIGINT          DEFAULT 0       NOT NULL,
                  staff_id            BIGINT          DEFAULT 0       NOT NULL,
                  create_time         TIMESTAMP       DEFAULT now()   NOT NULL,
                  update_time         TIMESTAMP       DEFAULT now()   NOT NULL
              );

              CREATE UNIQUE INDEX deposit_change_log_uq_idx_deposit_order_id_previous_log_id ON deposit_change_log (deposit_order_id, previous_log_id);
              CREATE INDEX deposit_change_log_idx_bid_cid ON deposit_change_log (business_id, customer_id);

              CREATE TRIGGER auto_update_deposit_change_log_update_time
                AFTER UPDATE
                ON deposit_change_log
                FOR EACH ROW
              EXECUTE PROCEDURE update_modified_column();

              COMMENT ON TABLE deposit_change_log IS 'Deposit balance change log';
              COMMENT ON COLUMN deposit_change_log.id IS 'Primary key';
              COMMENT ON COLUMN deposit_change_log.deposit_order_id IS 'Deposit order id';
              COMMENT ON COLUMN deposit_change_log.change_type IS 'Balance change direction (increase, decrease)';
              COMMENT ON COLUMN deposit_change_log.reason IS 'Balance change reason';
              COMMENT ON COLUMN deposit_change_log.dest_order_id IS 'ID of the order using this deposit (if applicable)';
              COMMENT ON COLUMN deposit_change_log.changed_amount IS 'Changed amount';
              COMMENT ON COLUMN deposit_change_log.balance IS 'Deposit balance after this change';
              COMMENT ON COLUMN deposit_change_log.currency_code IS 'Uppercase alpha3 currency code';
              COMMENT ON COLUMN deposit_change_log.previous_log_id IS 'id to the previous log for the changes to this deposit order';
              COMMENT ON COLUMN deposit_change_log.company_id IS 'Company id';
              COMMENT ON COLUMN deposit_change_log.business_id IS 'Business id';
              COMMENT ON COLUMN deposit_change_log.customer_id IS 'Customer id';
              COMMENT ON COLUMN deposit_change_log.staff_id IS 'Staff id';
              COMMENT ON COLUMN deposit_change_log.create_time IS 'Create time';
              COMMENT ON COLUMN deposit_change_log.update_time IS 'Update time';

              CREATE TABLE IF NOT EXISTS deposit_rule
              (
                  id                  BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
                  name                TEXT            DEFAULT ''      NOT NULL,
                  filters             TEXT            DEFAULT ''      NOT NULL,
                  deposit_type        TEXT            DEFAULT ''      NOT NULL,
                  deposit_currency    TEXT            DEFAULT ''      NOT NULL,
                  deposit_amount      NUMERIC(20, 2)  DEFAULT 0.00    NOT NULL,
                  deposit_percentage  NUMERIC(5, 2)   DEFAULT 0.00    NOT NULL,
                  company_id          BIGINT          DEFAULT 0       NOT NULL,
                  business_id         BIGINT          DEFAULT 0       NOT NULL,
                  create_time         TIMESTAMP       DEFAULT now()   NOT NULL,
                  update_time         TIMESTAMP       DEFAULT now()   NOT NULL
              );

              CREATE INDEX deposit_rule_idx_company_id ON deposit_rule (company_id);

              CREATE TRIGGER auto_update_deposit_rule_update_time
                  AFTER UPDATE
                  ON deposit_rule
                  FOR EACH ROW
              EXECUTE PROCEDURE update_modified_column();

              COMMENT ON TABLE deposit_rule IS 'Deposit rule';
              COMMENT ON COLUMN deposit_rule.id IS 'Primary key';
              COMMENT ON COLUMN deposit_rule.name IS 'Deposit rule name';
              COMMENT ON COLUMN deposit_rule.filters IS 'Filters pb json';
              COMMENT ON COLUMN deposit_rule.deposit_type IS 'Deposit type (fixed, percentage)';
              COMMENT ON COLUMN deposit_rule.deposit_currency IS 'Deposit currency';
              COMMENT ON COLUMN deposit_rule.deposit_amount IS 'Deposit fixed amount';
              COMMENT ON COLUMN deposit_rule.deposit_percentage IS 'Deposit percentage';
              COMMENT ON COLUMN deposit_rule.company_id IS 'Company id';
              COMMENT ON COLUMN deposit_rule.business_id IS 'Business id';
              COMMENT ON COLUMN deposit_rule.create_time IS 'Create time';
              COMMENT ON COLUMN deposit_rule.update_time IS 'Update time';

              -------- order promotion
              CREATE TABLE IF NOT EXISTS order_promotion  (
                  id BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
                  create_time         TIMESTAMP       DEFAULT now()   NOT NULL,
                  update_time         TIMESTAMP       DEFAULT now()   NOT NULL,
                  order_id BIGINT          DEFAULT 0       NOT NULL,
                  promotion_id BIGINT          DEFAULT 0       NOT NULL,
                  source_type TEXT DEFAULT ''      NOT NULL,
                  source_id BIGINT          DEFAULT 0       NOT NULL,
                  source_subject_id BIGINT          DEFAULT 0       NOT NULL,
                  name TEXT DEFAULT ''      NOT NULL,
                  discount_type TEXT DEFAULT ''      NOT NULL,
                  discount_value NUMERIC(20, 4) DEFAULT 0.00    NOT NULL,
                  applied_amount NUMERIC(20, 2) DEFAULT 0.00    NOT NULL,
                  status TEXT NOT NULL DEFAULT ''
              );

              CREATE INDEX order_promotion_idx_order_id ON order_promotion (order_id);
              CREATE INDEX order_promotion_idx_status_create_time ON order_promotion (status, create_time);

              CREATE TRIGGER auto_update_order_promotion_update_time
                AFTER UPDATE
                ON order_promotion
                FOR EACH ROW
              EXECUTE PROCEDURE update_modified_column();

              COMMENT ON TABLE order_promotion IS 'order promotion';
              COMMENT ON COLUMN order_promotion.id IS 'Primary key';
              COMMENT ON COLUMN order_promotion.create_time IS 'create time';
              COMMENT ON COLUMN order_promotion.update_time IS 'update time';
              COMMENT ON COLUMN order_promotion.order_id IS 'related order id';
              COMMENT ON COLUMN order_promotion.promotion_id IS 'related promotion id';
              COMMENT ON COLUMN order_promotion.source_type IS 'source promotion type, membership, package, discount etc';
              COMMENT ON COLUMN order_promotion.source_id IS 'source promotion id';
              COMMENT ON COLUMN order_promotion.source_subject_id IS 'source promotion subject id';
              COMMENT ON COLUMN order_promotion.name IS 'package name,discount code,membership name, etc';
              COMMENT ON COLUMN order_promotion.discount_type IS 'discount type, percentage, fixed_amount, item_deduction etc';
              COMMENT ON COLUMN order_promotion.discount_value IS 'discount value, for example 10 means 10% or fixed amount';
              COMMENT ON COLUMN order_promotion.applied_amount IS 'final applied amount';
              COMMENT ON COLUMN order_promotion.status IS 'status';


              CREATE TABLE IF NOT EXISTS order_promotion_item  (
                  id BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
                  create_time         TIMESTAMP       DEFAULT now()   NOT NULL,
                  update_time         TIMESTAMP       DEFAULT now()   NOT NULL,
                  order_promotion_id BIGINT          DEFAULT 0       NOT NULL,
                  order_item_id BIGINT          DEFAULT 0       NOT NULL,
                  applied_amount NUMERIC(20, 2) DEFAULT 0.00    NOT NULL
              );

              CREATE INDEX order_promotion_item_idx_order_promotion_id ON order_promotion_item (order_promotion_id);

              CREATE TRIGGER auto_update_order_promotion_item_update_time
                AFTER UPDATE
                ON order_promotion_item
                FOR EACH ROW
              EXECUTE PROCEDURE update_modified_column();

              COMMENT ON TABLE order_promotion_item IS 'order promotion item';
              COMMENT ON COLUMN order_promotion_item.id IS 'Primary key';
              COMMENT ON COLUMN order_promotion_item.create_time IS 'create time';
              COMMENT ON COLUMN order_promotion_item.update_time IS 'update time';
              COMMENT ON COLUMN order_promotion_item.order_promotion_id IS 'related order promotion id';
              COMMENT ON COLUMN order_promotion_item.order_item_id IS 'related order item id';
              COMMENT ON COLUMN order_promotion_item.applied_amount IS 'final applied amount';
