name: "Delete dirty slot data"
description: "delete dirty slot data"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_online_booking"
            username: "${db.user.admin}"
            sql: |
              delete from staff_availability_slot_day where business_id = 119517;