name: "Open Platform Webhook"
description: "add webhook"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_open_platform"
            sql: |
              CREATE TABLE IF NOT EXISTS webhook
              (
                  id            BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
                  enterprise_id BIGINT                      NOT NULL,
                  client_id     UUID                        NOT NULL,
                  organizations JSONB,
                  endpoint_url  VARCHAR(512)                NOT NULL,
                  event_types   INTEGER[]                   NOT NULL,
                  secret_token  VARCHAR(256),
                  content_type  INTEGER                              DEFAULT 1,
                  is_active     BOOLEAN                     NOT NULL DEFAULT TRUE,
                  verify_ssl    BOOLEAN                     NOT NULL DEFAULT TRUE,
                  headers       JSONB,
                  created_time  TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  updated_time  TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  deleted_time  TIMESTAMP WITHOUT TIME ZONE
              );
              
              CREATE INDEX IF NOT EXISTS webhook_filter ON webhook (enterprise_id, client_id);
              CREATE INDEX IF NOT EXISTS webhook_active ON webhook (is_active);
              CREATE INDEX IF NOT EXISTS webhook_event_types_gin ON webhook USING GIN (event_types);
              CREATE INDEX IF NOT EXISTS webhook_created_time ON webhook (created_time DESC);
              CREATE INDEX IF NOT EXISTS webhook_updated_time ON webhook (updated_time DESC);
              CREATE INDEX IF NOT EXISTS webhook_enterprise_active ON webhook (enterprise_id, is_active);
              CREATE INDEX IF NOT EXISTS webhook_client_id ON webhook (client_id);
              
              COMMENT ON TABLE webhook IS 'Webhook configurations for event notifications';
              COMMENT ON COLUMN webhook.id IS 'Unique ID of the webhook';
              COMMENT ON COLUMN webhook.enterprise_id IS 'Enterprise ID associated with this webhook';
              COMMENT ON COLUMN webhook.client_id IS 'Client ID associated with this webhook';
              COMMENT ON COLUMN webhook.organizations IS 'List of organizations the webhook is subscribed to. If empty, the webhook is subscribed to all organizations. should be parsed as Protocol Buffers Message';
              COMMENT ON COLUMN webhook.endpoint_url IS 'URL to receive webhook payloads';
              COMMENT ON COLUMN webhook.event_types IS 'List of event types the webhook is subscribed to. If it is empty, no events will be subscribed to. stored as JSON array';
              COMMENT ON COLUMN webhook.secret_token IS 'Optional secret token used to sign payloads';
              COMMENT ON COLUMN webhook.content_type IS 'Content-Type header, integer enum: 0=UNSPECIFIED, 1=JSON, 2=FORM_URLENCODED, 3=GRPC';
              COMMENT ON COLUMN webhook.is_active IS 'Whether the webhook is currently active';
              COMMENT ON COLUMN webhook.verify_ssl IS 'Whether to verify SSL certificates when delivering payloads (default: true)';
              COMMENT ON COLUMN webhook.headers IS 'Custom HTTP headers in map<string, repeated string> format (JSONB)';
              COMMENT ON COLUMN webhook.created_time IS 'Timestamp when the webhook was created';
              COMMENT ON COLUMN webhook.updated_time IS 'Timestamp when the webhook was last updated';
              COMMENT ON COLUMN webhook.deleted_time IS 'Timestamp when the webhook was deleted (soft delete)';
              
              CREATE TABLE IF NOT EXISTS webhook_delivery
              (
                  id               BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
                  webhook_id       BIGINT                      NOT NULL,
                  event_type       INTEGER                     NOT NULL,
                  event_id         VARCHAR(128)                NOT NULL,
                  request_url      VARCHAR(512)                NOT NULL,
                  delivered_to     VARCHAR(512),
                  request_headers  JSONB,
                  request_body     BYTEA,
                  response_status  INTEGER,
                  response_headers JSONB,
                  response_body    BYTEA,
                  delivered_at     TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  duration_ms      BIGINT,
                  success          BOOLEAN,
                  error            TEXT,
                  retry_count      INTEGER                     NOT NULL DEFAULT 0,
                  request_format   INTEGER                              DEFAULT 1,
                  response_format  INTEGER                              DEFAULT 1,
                  FOREIGN KEY (webhook_id) REFERENCES webhook (id)
              );
              
              CREATE INDEX IF NOT EXISTS webhook_delivery_by_webhook ON webhook_delivery (webhook_id);
              CREATE INDEX IF NOT EXISTS webhook_delivery_by_event ON webhook_delivery (event_type);
              CREATE INDEX IF NOT EXISTS webhook_delivery_by_time ON webhook_delivery (delivered_at);
              CREATE INDEX IF NOT EXISTS webhook_delivery_success ON webhook_delivery (success);
              CREATE INDEX IF NOT EXISTS webhook_delivery_event_id ON webhook_delivery (event_id);
              CREATE INDEX IF NOT EXISTS webhook_delivery_webhook_time ON webhook_delivery (webhook_id, delivered_at DESC);
              CREATE INDEX IF NOT EXISTS webhook_delivery_event_time ON webhook_delivery (event_type, delivered_at DESC);
              
              COMMENT ON TABLE webhook_delivery IS 'Log of webhook event deliveries for debugging and monitoring';
              COMMENT ON COLUMN webhook_delivery.id IS 'Unique ID for the delivery attempt';
              COMMENT ON COLUMN webhook_delivery.webhook_id IS 'Reference to the webhook that triggered this delivery';
              COMMENT ON COLUMN webhook_delivery.event_type IS 'Type of the event being delivered (from EventType enum)';
              COMMENT ON COLUMN webhook_delivery.request_url IS 'URL where the event was delivered';
              COMMENT ON COLUMN webhook_delivery.delivered_to IS 'The actual destination the request was delivered to (e.g., IP, domain, or URL)';
              COMMENT ON COLUMN webhook_delivery.request_headers IS 'HTTP headers sent with the request';
              COMMENT ON COLUMN webhook_delivery.request_body IS 'Payload of the event delivery (binary)';
              COMMENT ON COLUMN webhook_delivery.response_status IS 'HTTP status code returned by the endpoint';
              COMMENT ON COLUMN webhook_delivery.response_headers IS 'HTTP headers received in the response';
              COMMENT ON COLUMN webhook_delivery.response_body IS 'Body of the response, may be truncated (binary)';
              COMMENT ON COLUMN webhook_delivery.delivered_at IS 'Timestamp when the event was delivered';
              COMMENT ON COLUMN webhook_delivery.duration_ms IS 'Time taken to complete the delivery in milliseconds';
              COMMENT ON COLUMN webhook_delivery.success IS 'Whether the delivery was successful (HTTP 2xx)';
              COMMENT ON COLUMN webhook_delivery.error IS 'Error message in case of failure';
              COMMENT ON COLUMN webhook_delivery.retry_count IS 'Number of retries before final delivery outcome';
              COMMENT ON COLUMN webhook_delivery.request_format IS 'Content-Type of the request body, integer enum: 0=UNSPECIFIED, 1=JSON, 2=FORM_URLENCODED, 3=GRPC';
              COMMENT ON COLUMN webhook_delivery.response_format IS 'Content-Type of the response body, integer enum: 0=UNSPECIFIED, 1=JSON, 2=FORM_URLENCODED, 3=GRPC';
              
              
              -- 配置 webhook 的各种配额限制
              CREATE TABLE IF NOT EXISTS webhook_quota_config
              (
                  id                          BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
                  client_id                   UUID                        NOT NULL,
                  max_webhooks                INTEGER DEFAULT 50          NOT NULL,
                  max_delivery_retention_days INTEGER DEFAULT 15          NOT NULL,
                  max_push_per_minute         INTEGER DEFAULT 500         NOT NULL,
                  max_push_per_day            INTEGER DEFAULT 50000       NOT NULL,
                  max_push_per_month          INTEGER DEFAULT 1500000     NOT NULL,
                  created_time                TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  updated_time                TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  deleted_time                TIMESTAMP WITHOUT TIME ZONE,
                  FOREIGN KEY (client_id) REFERENCES oauth_client (id)
              );
              
              CREATE UNIQUE INDEX IF NOT EXISTS webhook_quota_config_by_client ON webhook_quota_config (client_id);
              
              COMMENT ON TABLE webhook_quota_config IS 'Webhook quota configuration per client';
              COMMENT ON COLUMN webhook_quota_config.client_id IS 'Associated client ID';
              COMMENT ON COLUMN webhook_quota_config.max_webhooks IS 'Maximum number of webhooks allowed for this client';
              COMMENT ON COLUMN webhook_quota_config.max_delivery_retention_days IS 'Number of days to retain delivery records';
              COMMENT ON COLUMN webhook_quota_config.max_push_per_minute IS 'Maximum push count per minute';
              COMMENT ON COLUMN webhook_quota_config.max_push_per_day IS 'Maximum push count per day';
              COMMENT ON COLUMN webhook_quota_config.max_push_per_month IS 'Maximum push count per month';
              COMMENT ON COLUMN webhook_quota_config.created_time IS 'Creation timestamp';
              COMMENT ON COLUMN webhook_quota_config.updated_time IS 'Last updated timestamp';
              COMMENT ON COLUMN webhook_quota_config.deleted_time IS 'Deletion timestamp';
              
              
              INSERT INTO webhook_quota_config (client_id)
              SELECT id
              FROM oauth_client;
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_open_platform"
            sql: |
              CREATE TABLE IF NOT EXISTS webhook
              (
                  id            BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
                  enterprise_id BIGINT                      NOT NULL,
                  client_id     UUID                        NOT NULL,
                  organizations JSONB,
                  endpoint_url  VARCHAR(512)                NOT NULL,
                  event_types   INTEGER[]                   NOT NULL,
                  secret_token  VARCHAR(256),
                  content_type  INTEGER                              DEFAULT 1,
                  is_active     BOOLEAN                     NOT NULL DEFAULT TRUE,
                  verify_ssl    BOOLEAN                     NOT NULL DEFAULT TRUE,
                  headers       JSONB,
                  created_time  TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  updated_time  TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  deleted_time  TIMESTAMP WITHOUT TIME ZONE
              );
              
              CREATE INDEX IF NOT EXISTS webhook_filter ON webhook (enterprise_id, client_id);
              CREATE INDEX IF NOT EXISTS webhook_active ON webhook (is_active);
              CREATE INDEX IF NOT EXISTS webhook_event_types_gin ON webhook USING GIN (event_types);
              CREATE INDEX IF NOT EXISTS webhook_created_time ON webhook (created_time DESC);
              CREATE INDEX IF NOT EXISTS webhook_updated_time ON webhook (updated_time DESC);
              CREATE INDEX IF NOT EXISTS webhook_enterprise_active ON webhook (enterprise_id, is_active);
              CREATE INDEX IF NOT EXISTS webhook_client_id ON webhook (client_id);
              
              COMMENT ON TABLE webhook IS 'Webhook configurations for event notifications';
              COMMENT ON COLUMN webhook.id IS 'Unique ID of the webhook';
              COMMENT ON COLUMN webhook.enterprise_id IS 'Enterprise ID associated with this webhook';
              COMMENT ON COLUMN webhook.client_id IS 'Client ID associated with this webhook';
              COMMENT ON COLUMN webhook.organizations IS 'List of organizations the webhook is subscribed to. If empty, the webhook is subscribed to all organizations. should be parsed as Protocol Buffers Message';
              COMMENT ON COLUMN webhook.endpoint_url IS 'URL to receive webhook payloads';
              COMMENT ON COLUMN webhook.event_types IS 'List of event types the webhook is subscribed to. If it is empty, no events will be subscribed to. stored as JSON array';
              COMMENT ON COLUMN webhook.secret_token IS 'Optional secret token used to sign payloads';
              COMMENT ON COLUMN webhook.content_type IS 'Content-Type header, integer enum: 0=UNSPECIFIED, 1=JSON, 2=FORM_URLENCODED, 3=GRPC';
              COMMENT ON COLUMN webhook.is_active IS 'Whether the webhook is currently active';
              COMMENT ON COLUMN webhook.verify_ssl IS 'Whether to verify SSL certificates when delivering payloads (default: true)';
              COMMENT ON COLUMN webhook.headers IS 'Custom HTTP headers in map<string, repeated string> format (JSONB)';
              COMMENT ON COLUMN webhook.created_time IS 'Timestamp when the webhook was created';
              COMMENT ON COLUMN webhook.updated_time IS 'Timestamp when the webhook was last updated';
              COMMENT ON COLUMN webhook.deleted_time IS 'Timestamp when the webhook was deleted (soft delete)';
              
              CREATE TABLE IF NOT EXISTS webhook_delivery
              (
                  id               BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
                  webhook_id       BIGINT                      NOT NULL,
                  event_type       INTEGER                     NOT NULL,
                  event_id         VARCHAR(128)                NOT NULL,
                  request_url      VARCHAR(512)                NOT NULL,
                  delivered_to     VARCHAR(512),
                  request_headers  JSONB,
                  request_body     BYTEA,
                  response_status  INTEGER,
                  response_headers JSONB,
                  response_body    BYTEA,
                  delivered_at     TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  duration_ms      BIGINT,
                  success          BOOLEAN,
                  error            TEXT,
                  retry_count      INTEGER                     NOT NULL DEFAULT 0,
                  request_format   INTEGER                              DEFAULT 1,
                  response_format  INTEGER                              DEFAULT 1,
                  FOREIGN KEY (webhook_id) REFERENCES webhook (id)
              );
              
              CREATE INDEX IF NOT EXISTS webhook_delivery_by_webhook ON webhook_delivery (webhook_id);
              CREATE INDEX IF NOT EXISTS webhook_delivery_by_event ON webhook_delivery (event_type);
              CREATE INDEX IF NOT EXISTS webhook_delivery_by_time ON webhook_delivery (delivered_at);
              CREATE INDEX IF NOT EXISTS webhook_delivery_success ON webhook_delivery (success);
              CREATE INDEX IF NOT EXISTS webhook_delivery_event_id ON webhook_delivery (event_id);
              CREATE INDEX IF NOT EXISTS webhook_delivery_webhook_time ON webhook_delivery (webhook_id, delivered_at DESC);
              CREATE INDEX IF NOT EXISTS webhook_delivery_event_time ON webhook_delivery (event_type, delivered_at DESC);
              
              COMMENT ON TABLE webhook_delivery IS 'Log of webhook event deliveries for debugging and monitoring';
              COMMENT ON COLUMN webhook_delivery.id IS 'Unique ID for the delivery attempt';
              COMMENT ON COLUMN webhook_delivery.webhook_id IS 'Reference to the webhook that triggered this delivery';
              COMMENT ON COLUMN webhook_delivery.event_type IS 'Type of the event being delivered (from EventType enum)';
              COMMENT ON COLUMN webhook_delivery.request_url IS 'URL where the event was delivered';
              COMMENT ON COLUMN webhook_delivery.delivered_to IS 'The actual destination the request was delivered to (e.g., IP, domain, or URL)';
              COMMENT ON COLUMN webhook_delivery.request_headers IS 'HTTP headers sent with the request';
              COMMENT ON COLUMN webhook_delivery.request_body IS 'Payload of the event delivery (binary)';
              COMMENT ON COLUMN webhook_delivery.response_status IS 'HTTP status code returned by the endpoint';
              COMMENT ON COLUMN webhook_delivery.response_headers IS 'HTTP headers received in the response';
              COMMENT ON COLUMN webhook_delivery.response_body IS 'Body of the response, may be truncated (binary)';
              COMMENT ON COLUMN webhook_delivery.delivered_at IS 'Timestamp when the event was delivered';
              COMMENT ON COLUMN webhook_delivery.duration_ms IS 'Time taken to complete the delivery in milliseconds';
              COMMENT ON COLUMN webhook_delivery.success IS 'Whether the delivery was successful (HTTP 2xx)';
              COMMENT ON COLUMN webhook_delivery.error IS 'Error message in case of failure';
              COMMENT ON COLUMN webhook_delivery.retry_count IS 'Number of retries before final delivery outcome';
              COMMENT ON COLUMN webhook_delivery.request_format IS 'Content-Type of the request body, integer enum: 0=UNSPECIFIED, 1=JSON, 2=FORM_URLENCODED, 3=GRPC';
              COMMENT ON COLUMN webhook_delivery.response_format IS 'Content-Type of the response body, integer enum: 0=UNSPECIFIED, 1=JSON, 2=FORM_URLENCODED, 3=GRPC';
              
              
              -- 配置 webhook 的各种配额限制
              CREATE TABLE IF NOT EXISTS webhook_quota_config
              (
                  id                          BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
                  client_id                   UUID                        NOT NULL,
                  max_webhooks                INTEGER DEFAULT 50          NOT NULL,
                  max_delivery_retention_days INTEGER DEFAULT 15          NOT NULL,
                  max_push_per_minute         INTEGER DEFAULT 500         NOT NULL,
                  max_push_per_day            INTEGER DEFAULT 50000       NOT NULL,
                  max_push_per_month          INTEGER DEFAULT 1500000     NOT NULL,
                  created_time                TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  updated_time                TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  deleted_time                TIMESTAMP WITHOUT TIME ZONE,
                  FOREIGN KEY (client_id) REFERENCES oauth_client (id)
              );
              
              CREATE UNIQUE INDEX IF NOT EXISTS webhook_quota_config_by_client ON webhook_quota_config (client_id);
              
              COMMENT ON TABLE webhook_quota_config IS 'Webhook quota configuration per client';
              COMMENT ON COLUMN webhook_quota_config.client_id IS 'Associated client ID';
              COMMENT ON COLUMN webhook_quota_config.max_webhooks IS 'Maximum number of webhooks allowed for this client';
              COMMENT ON COLUMN webhook_quota_config.max_delivery_retention_days IS 'Number of days to retain delivery records';
              COMMENT ON COLUMN webhook_quota_config.max_push_per_minute IS 'Maximum push count per minute';
              COMMENT ON COLUMN webhook_quota_config.max_push_per_day IS 'Maximum push count per day';
              COMMENT ON COLUMN webhook_quota_config.max_push_per_month IS 'Maximum push count per month';
              COMMENT ON COLUMN webhook_quota_config.created_time IS 'Creation timestamp';
              COMMENT ON COLUMN webhook_quota_config.updated_time IS 'Last updated timestamp';
              COMMENT ON COLUMN webhook_quota_config.deleted_time IS 'Deletion timestamp';
              
              
              INSERT INTO webhook_quota_config (client_id)
              SELECT id
              FROM oauth_client;