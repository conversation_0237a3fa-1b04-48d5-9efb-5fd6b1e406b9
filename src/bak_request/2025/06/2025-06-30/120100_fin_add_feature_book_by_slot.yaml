name: "add bookBySlot feature for growth plan or higher"
description: "add bookBySlot feature for growth plan or higher"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              INSERT INTO moe_payment.moe_plan_feature_relation (level, code, allow_type, enable, quota, create_time, update_time,
                                                    is_deleted)
              VALUES (1101, 'bookBySlot', DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT);

              INSERT INTO moe_payment.moe_plan_feature_relation (level, code, allow_type, enable, quota, create_time, update_time,
                                                                is_deleted)
              VALUES (1201, 'bookBySlot', DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT);

              INSERT INTO moe_payment.moe_plan_feature_relation (level, code, allow_type, enable, quota, create_time, update_time,
                                                                is_deleted)
              VALUES (1102, 'bookBySlot', DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT);

              INSERT INTO moe_payment.moe_plan_feature_relation (level, code, allow_type, enable, quota, create_time, update_time,
                                                                is_deleted)
              VALUES (1202, 'bookBySlot', DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT);

              INSERT INTO moe_payment.moe_plan_feature_relation (level, code, allow_type, enable, quota, create_time, update_time,
                                                                is_deleted)
              VALUES (1302, 'bookBySlot', DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT);


  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              INSERT INTO moe_payment.moe_plan_feature_relation (level, code, allow_type, enable, quota, create_time, update_time,
                                                    is_deleted)
              VALUES (1101, 'bookBySlot', DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT);

              INSERT INTO moe_payment.moe_plan_feature_relation (level, code, allow_type, enable, quota, create_time, update_time,
                                                                is_deleted)
              VALUES (1201, 'bookBySlot', DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT);

              INSERT INTO moe_payment.moe_plan_feature_relation (level, code, allow_type, enable, quota, create_time, update_time,
                                                                is_deleted)
              VALUES (1102, 'bookBySlot', DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT);

              INSERT INTO moe_payment.moe_plan_feature_relation (level, code, allow_type, enable, quota, create_time, update_time,
                                                                is_deleted)
              VALUES (1202, 'bookBySlot', DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT);

              INSERT INTO moe_payment.moe_plan_feature_relation (level, code, allow_type, enable, quota, create_time, update_time,
                                                                is_deleted)
              VALUES (1302, 'bookBySlot', DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT, DEFAULT);