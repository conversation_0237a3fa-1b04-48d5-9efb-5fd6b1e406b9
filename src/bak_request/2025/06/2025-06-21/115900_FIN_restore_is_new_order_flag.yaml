name: "Restore is_new_order flag"
description: "清理了 v2 版本的订单数据，需要恢复 appt 的 is_new_order 标记"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-grooming
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              UPDATE moe_grooming.appointment_extra_info 
              SET is_new_order = 1
              WHERE appointment_id IN 
              (79060408, 79273212, 79347069, 79348289, 79384357, 79396676, 79420580, 79421234, 79421248, 79436165, 79440736,
              79557330, 79580566, 79582235, 79583217, 79585248, 79602380, 79682527);
              ;
