name: "Unlink unexpected orders"
description: "把一些之前因 BUG 错误创建出来的 v2 的 order 断开与 company/business/customer/appointment 的链接"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              BEGIN;
              UPDATE "order"
              SET company_id = -company_id, 
                  customer_id = -customer_id,
                  business_id = -business_id,
                  source_id = -source_id,
                  status = 3
              WHERE id IN (*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********,*********)
                  AND paid_amount = 0 
                  AND status = 0;
              COMMIT;
