name: "Unlink unexpected orders"
description: "前序忘记更新 update time 了，补一个"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              BEGIN;
              UPDATE "order"
              SET update_time = NOW()
              WHERE id IN (148912584,149250174,149331115,149349929,149368638,149353964,149174775,149219059,149246287,149204383,149219002,149330100,149352110,149214397,149231017,149110661,149175834,149351347) 
                  AND status = 3;
              COMMIT;
