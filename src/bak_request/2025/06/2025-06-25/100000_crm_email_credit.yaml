name: "modify email credit"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_message"
            sql: |
              UPDATE moe_message.moe_business_message_control
              SET available_emails = available_emails + 100,
              purchase_email_amount = purchase_email_amount + 100
              WHERE id = 222702;