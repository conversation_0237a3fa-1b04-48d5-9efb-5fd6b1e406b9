stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-grooming"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              alter table moe_grooming.moe_book_online_question
              add new_pet_access_mode tinyint default 0 not null comment 'Access mode when creating a new pet, see NewPetAccessMode';

              alter table moe_grooming.moe_book_online_question
              add existing_pet_access_mode tinyint default 0 not null comment 'Access mode when managing an existing pet, see ExistingPetAccessMode';

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-grooming"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              alter table moe_grooming.moe_book_online_question
              add new_pet_access_mode tinyint default 0 not null comment 'Access mode when creating a new pet, see NewPetAccessMode';
              
              alter table moe_grooming.moe_book_online_question
              add existing_pet_access_mode tinyint default 0 not null comment 'Access mode when managing an existing pet, see ExistingPetAccessMode';
