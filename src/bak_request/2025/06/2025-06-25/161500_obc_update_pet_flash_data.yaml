stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-grooming"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              -- 更新 accept_pet_entry_type
              update moe_grooming.moe_book_online_question
              set accept_pet_entry_type = case
              when accepted_customer_type = 1 then 1
              when accepted_customer_type = 2 then 2
              when accepted_customer_type = 3 then 3
              else 0
              end
              where type = 1
              and accept_pet_entry_type = 0;
              
              -- 1. 更新 new_pet_access_mode
              UPDATE moe_grooming.moe_book_online_question
              SET new_pet_access_mode = CASE
              WHEN accept_pet_entry_type IN (1, 3) THEN 1 -- NEW, NEW_AND_EXISTING -> ENABLED
              WHEN accept_pet_entry_type = 2 THEN 2 -- EXISTING -> DISABLED
              ELSE new_pet_access_mode
              END
              WHERE type = 1
              AND accept_pet_entry_type IN (1, 2, 3)
              AND new_pet_access_mode = 0;
              
              -- 2. 更新 existing_pet_access_mode
              UPDATE moe_grooming.moe_book_online_question
              SET existing_pet_access_mode = CASE
              WHEN accept_pet_entry_type IN (2, 3) THEN 2 -- EXISTING, NEW_AND_EXISTING -> VIEW_AND_EDIT
              WHEN accept_pet_entry_type = 1 THEN 3 -- NEW -> DISABLED
              ELSE existing_pet_access_mode
              END
              WHERE type = 1
              AND accept_pet_entry_type IN (1, 2, 3)
              AND existing_pet_access_mode = 0;

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-grooming"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              -- 更新 accept_pet_entry_type
              update moe_grooming.moe_book_online_question
              set accept_pet_entry_type = case
              when accepted_customer_type = 1 then 1
              when accepted_customer_type = 2 then 2
              when accepted_customer_type = 3 then 3
              else 0
              end
              where type = 1
              and accept_pet_entry_type = 0;
              
              -- 1. 更新 new_pet_access_mode
              UPDATE moe_grooming.moe_book_online_question
              SET new_pet_access_mode = CASE
              WHEN accept_pet_entry_type IN (1, 3) THEN 1 -- NEW, NEW_AND_EXISTING -> ENABLED
              WHEN accept_pet_entry_type = 2 THEN 2 -- EXISTING -> DISABLED
              ELSE new_pet_access_mode
              END
              WHERE type = 1
              AND accept_pet_entry_type IN (1, 2, 3)
              AND new_pet_access_mode = 0;
              
              -- 2. 更新 existing_pet_access_mode
              UPDATE moe_grooming.moe_book_online_question
              SET existing_pet_access_mode = CASE
              WHEN accept_pet_entry_type IN (2, 3) THEN 2 -- EXISTING, NEW_AND_EXISTING -> VIEW_AND_EDIT
              WHEN accept_pet_entry_type = 1 THEN 3 -- NEW -> DISABLED
              ELSE existing_pet_access_mode
              END
              WHERE type = 1
              AND accept_pet_entry_type IN (1, 2, 3)
              AND existing_pet_access_mode = 0;
