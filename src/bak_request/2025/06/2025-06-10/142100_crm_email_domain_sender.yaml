name: "email domain"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_engagement"
            sql: |
              create table email_sender
              (
                  id          bigserial
                      primary key,
                  business_id bigint                                                                   not null,
                  email       text                                                                     not null,
                  name        text                                                                     not null,
                  domain      text                                                                     not null,
                  confirmed   boolean                  default false                                   not null,
                  created_at  timestamp with time zone default CURRENT_TIMESTAMP                       not null,
                  updated_at  timestamp with time zone default CURRENT_TIMESTAMP                       not null,
                  deleted_at  timestamp with time zone,
                  type        text                     default 'SENDER_EMAIL_USAGE_TYPE_DEFAULT'::text not null
              );

              comment on column email_sender.type is 'Sender类型: SENDER_EMAIL_USAGE_TYPE_CUSTOMIZE=自定义, SENDER_EMAIL_USAGE_TYPE_DEFAULT=默认';

              create index idx_email_senders_deleted_at
                  on email_sender (deleted_at);

              create index idx_email_sender_deleted_at
                  on email_sender (deleted_at);

              create unique index email_sender_business_id_deleted_at_uindex
                  on email_sender (business_id, deleted_at)
                  where (deleted_at IS NULL);


  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_engagement"
            sql: |
              create table email_sender
              (
                  id          bigserial
                      primary key,
                  business_id bigint                                                                   not null,
                  email       text                                                                     not null,
                  name        text                                                                     not null,
                  domain      text                                                                     not null,
                  confirmed   boolean                  default false                                   not null,
                  created_at  timestamp with time zone default CURRENT_TIMESTAMP                       not null,
                  updated_at  timestamp with time zone default CURRENT_TIMESTAMP                       not null,
                  deleted_at  timestamp with time zone,
                  type        text                     default 'SENDER_EMAIL_USAGE_TYPE_DEFAULT'::text not null
              );

              comment on column email_sender.type is 'Sender类型: SENDER_EMAIL_USAGE_TYPE_CUSTOMIZE=自定义, SENDER_EMAIL_USAGE_TYPE_DEFAULT=默认';

              create index idx_email_senders_deleted_at
                  on email_sender (deleted_at);

              create index idx_email_sender_deleted_at
                  on email_sender (deleted_at);

              create unique index email_sender_business_id_deleted_at_uindex
                  on email_sender (business_id, deleted_at)
                  where (deleted_at IS NULL);

