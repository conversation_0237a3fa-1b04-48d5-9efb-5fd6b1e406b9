name: "daily report add email send init"
description: "daily report add email send init"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_appointment"
            sql: |
              update public.daily_report_send_log set send_method = 1 where send_method = 0;



  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_appointment"
            sql: |
              update public.daily_report_send_log set send_method = 1 where send_method = 0;
