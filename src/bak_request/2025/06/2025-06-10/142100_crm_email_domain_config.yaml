name: "email domain"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_engagement"
            sql: |
              create table email_dns_record
              (
                  id           bigserial
                      primary key,
                  domain       varchar(255)                                                                      not null,
                  record_type  varchar(50)              default 'DNS_RECORD_TYPE_UNSPECIFIED'::character varying not null,
                  host         varchar(255)                                                                      not null,
                  record_value text                                                                              not null,
                  created_at   timestamp with time zone default CURRENT_TIMESTAMP                                not null,
                  updated_at   timestamp with time zone default CURRENT_TIMESTAMP                                not null,
                  deleted_at   timestamp with time zone
              );


              create index idx_email_dns_records_deleted_at
                  on email_dns_record (deleted_at);

              create index email_dns_record_domain_index
                  on email_dns_record (domain);


  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_engagement"
            sql: |
              create table email_dns_record
              (
                  id           bigserial
                      primary key,
                  domain       varchar(255)                                                                      not null,
                  record_type  varchar(50)              default 'DNS_RECORD_TYPE_UNSPECIFIED'::character varying not null,
                  host         varchar(255)                                                                      not null,
                  record_value text                                                                              not null,
                  created_at   timestamp with time zone default CURRENT_TIMESTAMP                                not null,
                  updated_at   timestamp with time zone default CURRENT_TIMESTAMP                                not null,
                  deleted_at   timestamp with time zone
              );


              create index idx_email_dns_records_deleted_at
                  on email_dns_record (deleted_at);

              create index email_dns_record_domain_index
                  on email_dns_record (domain);

