name: "email domain"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_engagement"
            sql: |
              create table email_domain
              (
                  id            bigserial
                      primary key,
                  domain        varchar(255)                                                             not null
                      constraint email_domains_domain_unique
                          unique,
                  verify_status varchar(50)              default 'VERIFY_STATUS_INIT'::character varying not null,
                  created_at    timestamp with time zone default CURRENT_TIMESTAMP                       not null,
                  updated_at    timestamp with time zone default CURRENT_TIMESTAMP                       not null,
                  deleted_at    timestamp with time zone
              );

              create index idx_email_domains_deleted_at
                  on email_domain (deleted_at);


  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_engagement"
            sql: |
              create table email_domain
              (
                  id            bigserial
                      primary key,
                  domain        varchar(255)                                                             not null
                      constraint email_domains_domain_unique
                          unique,
                  verify_status varchar(50)              default 'VERIFY_STATUS_INIT'::character varying not null,
                  created_at    timestamp with time zone default CURRENT_TIMESTAMP                       not null,
                  updated_at    timestamp with time zone default CURRENT_TIMESTAMP                       not null,
                  deleted_at    timestamp with time zone
              );

              create index idx_email_domains_deleted_at
                  on email_domain (deleted_at);

