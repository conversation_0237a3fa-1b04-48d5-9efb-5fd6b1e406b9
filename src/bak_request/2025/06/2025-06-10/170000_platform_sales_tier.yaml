name: "redefine tier value in platform sales"
description: "redefine tier value in platform sales"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              update moe_platform_sales SET tier = 'T1' where tier = 'Tier 1';
              update moe_platform_sales SET tier = 'T2' where tier = 'Tier 2';
              update moe_platform_sales SET tier = 'T3' where tier = 'Tier 3';
              update moe_platform_sales SET tier = 'T4' where tier = 'Tier 4';
              update moe_platform_sales SET tier = 'T5' where tier = 'Tier 5';

              select distinct tier from moe_platform_sales;

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              update moe_platform_sales SET tier = 'T1' where tier = 'Tier 1';
              update moe_platform_sales SET tier = 'T2' where tier = 'Tier 2';
              update moe_platform_sales SET tier = 'T3' where tier = 'Tier 3';
              update moe_platform_sales SET tier = 'T4' where tier = 'Tier 4';
              update moe_platform_sales SET tier = 'T5' where tier = 'Tier 5';
              
              select distinct tier from moe_platform_sales;