name: "engagement contact check"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_engagement"
            sql: |
              alter table moe_customer_contact_shadow
                  drop constraint moe_customer_contact_type_check;
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_engagement"
            sql: |
              alter table moe_customer_contact_shadow
                  drop constraint moe_customer_contact_type_check;
