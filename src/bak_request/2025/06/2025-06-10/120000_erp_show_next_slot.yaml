name: "Add show onw available time field"
description: "Add show onw available time field"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-grooming
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              alter table moe_business_book_online add by_slot_show_one_available_time boolean default false not null comment 'only show the next available slot, false-disable, true-enable';