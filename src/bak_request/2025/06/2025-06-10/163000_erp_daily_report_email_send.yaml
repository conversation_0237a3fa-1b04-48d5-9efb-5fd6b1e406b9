name: "daily report add email send"
description: "daily report add email send"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_appointment"
            sql: |
              alter table public.daily_report_send_log
                add send_method integer default 0 not null;

              comment on column public.daily_report_send_log.send_method is '1-sms 2-email';


  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_appointment"
            sql: |
              alter table public.daily_report_send_log
                add send_method integer default 0 not null;

              comment on column public.daily_report_send_log.send_method is '1-sms 2-email';