name: "Add staff availability type"
description: "add staff availability type"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_business"
            sql: |
              ALTER TABLE moe_business ADD COLUMN staff_availability_type int NOT NULL DEFAULT 1 COMMENT '1-by time, 2-by slot';