name: "delete square cof for business 105238"
description: "delete square cof for business 105238"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              UPDATE moe_payment.customer_square 
              SET status = 2, update_time = now() 
              WHERE id IN (85150,86576,86633,86678,86685,86686,86825,86826,86889,86934,86939,86958,88004,162426,85209,85425,85441,85446,85571,85640,85659,85663,86337,86581,86597,86630,86691,86835,86861,86869,86873,86952,90844,123150,157547,102171,85145,85445,85664,85669,86422,86579,86580,86585,86680,86684,86852,86854,86855,86858,86866,86914,86918,86925,86935,86936,87243,87245,87248,87998,88001,88015,88352,91386,91438,149373,72705,85224,85384,85393,86577,86615,86643,86673,86674,86848,86850,86871,86876,86910,86927,86937,86943,86953,86956,86960,87239,88006,88094,141997,78652,85236,85403,85614,85666,85671,85992,86342,86409,86619,86628,86675,86681,86690,86701,86823,86863,86868,86928,86938,86940,86959,87237,87244,87247,87939,88000,88003,100390,134814,84651,85413,85652,85805,86593,86617,86622,86671,86676,86677,86683,86862,86865,86908,86920,86932,86954,86961,87997,133975,142976)
              AND business_id = 105238 
              AND status = 1;