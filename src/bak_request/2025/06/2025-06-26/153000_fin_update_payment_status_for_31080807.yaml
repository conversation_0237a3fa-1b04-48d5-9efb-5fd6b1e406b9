name: "update payment status for 31080807 CS-30571"
description: "https://moegoworkspace.slack.com/archives/C01TT9K995M/p1750885*********"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              BEGIN;
              
              UPDATE moe_payment.payment SET status = 3, update_time = unix_timestamp(now()) WHERE id = 31080807 AND status = 2;

              INSERT INTO moe_payment.moe_pay_detail( company_id,business_id,payment_id,order_id,stripe_intent_id,amount, gross_sales,convenience_fee) 
              VALUES ( 121994,122465,31080807,*********,"pi_3RYBrwIZwcIFVLGr0QftXbMq",137,137,0);
              
              COMMIT;