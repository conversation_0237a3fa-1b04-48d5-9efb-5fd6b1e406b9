name: "fix discount amount for order"
description: "fix discount amount for order"
stopWhenError: true
#  149369899,149240419,149220659,149215006,149184846,149184421,149183547,149182172,149180607,149180484,149180474,149180451,
#  149180425,149179511,149178789,149159120,149156582,149156542,149122308,149114831
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              BEGIN;
              
              -- 149369899
              UPDATE order_line_discount
              SET is_deleted = false, update_time = NOW()
              WHERE order_id = 149369899
              AND id IN (104196828,104196829);
              
              UPDATE order_line_discount
              SET is_deleted = true, update_time = NOW()
              WHERE order_id = 149369899
              AND id IN (104196862,104196863);
              
              UPDATE order_line_item
              SET discount_amount = 3.6, total_amount = 32.4, update_time = NOW()
              WHERE order_id = 149369899
              AND id = 198893021;
              
              UPDATE order_line_item
              SET discount_amount = 0.7, total_amount = 6.3, update_time = NOW()
              WHERE order_id = 149369899
              AND id = 198893022;
              
              UPDATE "order"
              SET update_time = NOW(), discount_amount = 4.3, total_amount = 38.7
              WHERE id = 149369899;
              
              -- 149240419
              UPDATE order_line_discount
              SET is_deleted = false, update_time = NOW()
              WHERE order_id = 149240419
              AND id IN (104186939,104186940,104186941);
              UPDATE order_line_discount
              SET is_deleted = true, update_time = NOW()
              WHERE order_id = 149240419
              AND id IN (104186942,104186943,104186944);
              
              UPDATE order_line_item
              SET discount_amount = 148.5, total_amount = 841.5, update_time = NOW()
              WHERE order_id = 149240419
              AND id = 198510713;
              UPDATE order_line_item
              SET discount_amount = 13.5, total_amount = 76.5, update_time = NOW()
              WHERE order_id = 149240419
              AND id = 198510714;
              UPDATE order_line_item
              SET discount_amount = 7.2, total_amount = 40.8, update_time = NOW()
              WHERE order_id = 149240419
              AND id = 198510715;
              
              UPDATE "order"
              SET update_time = NOW(), discount_amount = 169.2, total_amount = 594.3
              WHERE id = 149240419;
              
              -- 149220659
              UPDATE order_line_discount
              SET is_deleted = false, update_time = NOW()
              WHERE order_id = 149220659
              AND id IN (104183749,104183750);
              UPDATE order_line_discount
              SET is_deleted = true, update_time = NOW()
              WHERE order_id = 149220659
              AND id IN (104183754,104183755);
              
              UPDATE order_line_item
              SET discount_amount = 73.26, total_amount = 415.14, update_time = NOW()
              WHERE order_id = 149220659
              AND id = 198416613;
              UPDATE order_line_item
              SET discount_amount = 0.9, total_amount = 5.1, update_time = NOW()
              WHERE order_id = 149220659
              AND id = 198416614;
              UPDATE order_line_item
              SET discount_amount = 0, total_amount = 5, update_time = NOW()
              WHERE order_id = 149220659
              AND id = 198416615;
              
              UPDATE "order"
              SET update_time = NOW(), discount_amount = 74.16, total_amount = 159.76
              WHERE id = 149220659;
              
              -- 149215006
              UPDATE order_line_discount
              SET is_deleted = false, update_time = NOW()
              WHERE order_id = 149215006
              AND id IN (104183064,104183065,104183066,104183067);
              UPDATE order_line_discount
              SET is_deleted = true, update_time = NOW()
              WHERE order_id = 149215006
              AND id IN (104183070,104183071,104183072,104183073);
              
              UPDATE order_line_item
              SET discount_amount = 48.84, total_amount = 276.76, update_time = NOW()
              WHERE order_id = 149215006
              AND id = 198391001;
              UPDATE order_line_item
              SET discount_amount = 10.2, total_amount = 57.8, update_time = NOW()
              WHERE order_id = 149215006
              AND id = 198391002;
              UPDATE order_line_item
              SET discount_amount = 3, total_amount = 17, update_time = NOW()
              WHERE order_id = 149215006
              AND id = 198391003;
              UPDATE order_line_item
              SET discount_amount = 9, total_amount = 51, update_time = NOW()
              WHERE order_id = 149215006
              AND id = 198391004;
              
              UPDATE "order"
              SET update_time = NOW(), discount_amount = 71.04, total_amount = 212.47
              WHERE id = 149215006;
              
              -- 149184846
              UPDATE order_line_discount
              SET is_deleted = false, update_time = NOW()
              WHERE order_id = 149184846
              AND id IN (104180168,104180169);
              UPDATE order_line_discount
              SET is_deleted = true, update_time = NOW()
              WHERE order_id = 149184846
              AND id IN (104181159,104181160);
              
              UPDATE order_line_item
              SET discount_amount = 41.58, total_amount = 235.62, update_time = NOW()
              WHERE order_id = 149184846
              AND id = 198258498;
              UPDATE order_line_item
              SET discount_amount = 9.9, total_amount = 56.1, update_time = NOW()
              WHERE order_id = 149184846
              AND id = 198258499;
              
              UPDATE "order"
              SET update_time = NOW(), discount_amount = 51.48, total_amount = 152.87
              WHERE id = 149184846;
              
              -- 149184421
              UPDATE order_line_discount
              SET is_deleted = false, update_time = NOW()
              WHERE order_id = 149184421
              AND id IN (104180109,104180110,104180111,104180112);
              UPDATE order_line_discount
              SET is_deleted = true, update_time = NOW()
              WHERE order_id = 149184421
              AND id IN (104180113,104180114,104180115,104180116);
              
              UPDATE order_line_item
              SET discount_amount = 41.58, total_amount = 235.62, update_time = NOW()
              WHERE order_id = 149184421
              AND id = 198257267;
              UPDATE order_line_item
              SET discount_amount = 3.6, total_amount = 20.4, update_time = NOW()
              WHERE order_id = 149184421
              AND id = 198257268;
              UPDATE order_line_item
              SET discount_amount = 1.8, total_amount = 10.2, update_time = NOW()
              WHERE order_id = 149184421
              AND id = 198257269;
              UPDATE order_line_item
              SET discount_amount = 6.6, total_amount = 37.4, update_time = NOW()
              WHERE order_id = 149184421
              AND id = 198257270;
              
              UPDATE "order"
              SET update_time = NOW(), discount_amount = 53.58, total_amount = 109.12, deposit_amount = 194.5
              WHERE id = 149184421;
              
              -- 149183547
              UPDATE order_line_discount
              SET is_deleted = false, update_time = NOW()
              WHERE order_id = 149183547
              AND id IN (104179964,104179965);
              UPDATE order_line_discount
              SET is_deleted = true, update_time = NOW()
              WHERE order_id = 149183547
              AND id IN (104179966,104179967);
              
              UPDATE order_line_item
              SET discount_amount = 3.6, total_amount = 32.4, update_time = NOW()
              WHERE order_id = 149183547
              AND id = 198252740;
              UPDATE order_line_item
              SET discount_amount = 0.7, total_amount = 6.3, update_time = NOW()
              WHERE order_id = 149183547
              AND id = 198252741;
              
              UPDATE "order"
              SET update_time = NOW(), discount_amount = 4.3, total_amount = 38.7
              WHERE id = 149183547;
              
              -- 149182172
              UPDATE order_line_discount
              SET is_deleted = false, update_time = NOW()
              WHERE order_id = 149182172
              AND id IN (104179702);
              UPDATE order_line_discount
              SET is_deleted = true, update_time = NOW()
              WHERE order_id = 149182172
              AND id IN (104179703);
              
              UPDATE order_line_item
              SET discount_amount = 50.32, total_amount = 285.18, update_time = NOW()
              WHERE order_id = 149182172
              AND id = 198246089;
              UPDATE order_line_item
              SET discount_amount = 0, total_amount = 5, update_time = NOW()
              WHERE order_id = 149182172
              AND id = 198246090;
              
              UPDATE "order"
              SET update_time = NOW(), discount_amount = 50.32, total_amount = 166.05
              WHERE id = 149182172;
              
              -- 149180607
              UPDATE order_line_discount
              SET is_deleted = false, update_time = NOW()
              WHERE order_id = 149180607
              AND id IN (104179403,104179404,104179405);
              UPDATE order_line_discount
              SET is_deleted = true, update_time = NOW()
              WHERE order_id = 149180607
              AND id IN (104179406,104179407,104179408);
              
              UPDATE order_line_item
              SET discount_amount = 35.2, total_amount = 316.8, update_time = NOW()
              WHERE order_id = 149180607
              AND id = 198237676;
              UPDATE order_line_item
              SET discount_amount = 3.6, total_amount = 32.4, update_time = NOW()
              WHERE order_id = 149180607
              AND id = 198237677;
              UPDATE order_line_item
              SET discount_amount = 4.8, total_amount = 43.2, update_time = NOW()
              WHERE order_id = 149180607
              AND id = 198237678;
              
              UPDATE "order"
              SET update_time = NOW(), discount_amount = 43.6, total_amount = 392.4
              WHERE id = 149180607;
              
              -- 149180484
              UPDATE order_line_discount
              SET is_deleted = false, update_time = NOW()
              WHERE order_id = 149180484
              AND id IN (104179357,104179358,104179359,104179360,104179361,104179362,104179363,104179364,104179365,104179366,104179367,104179368,104179369,104179370,104179371,104179372,104179373,104179374);
              UPDATE order_line_discount
              SET is_deleted = true, update_time = NOW()
              WHERE order_id = 149180484
              AND id IN (104179375,104179376,104179377,104179378,104179379,104179380,104179381,104179382,104179383,104179384,104179385,104179386,104179387,104179388,104179389,104179390,104179391,104179392);
              
              UPDATE order_line_item
              SET discount_amount = sub_total_amount * 0.15, total_amount = sub_total_amount * 0.85, update_time = NOW()
              WHERE order_id = 149180484
              AND id IN (198236600,198236601,198236602,198236603,198236604,198236605,198236606,198236607,198236608,198236609,198236610,198236611,198236612,198236613,198236614,198236615,198236616,198236617);
              
              UPDATE "order"
              SET update_time = NOW(), discount_amount = 112.56, total_amount = 358.28, deposit_amount = 279.56
              WHERE id = 149180484;
              
              -- 149180474
              UPDATE order_line_discount
              SET is_deleted = false, update_time = NOW()
              WHERE order_id = 149180474
              AND id >= 104179302 AND id <= 104179325;
              UPDATE order_line_discount
              SET is_deleted = true, update_time = NOW()
              WHERE order_id = 149180474
              AND id >= 104179328 AND id <= 104179351;
              
              UPDATE order_line_item
              SET discount_amount = sub_total_amount * 0.15, total_amount = sub_total_amount * 0.85, update_time = NOW()
              WHERE order_id = 149180474
              AND id >= 198236543 AND id <= 198236566;
              UPDATE order_line_item
              SET discount_amount = 0, total_amount = 5, update_time = NOW()
              WHERE order_id = 149180474
              AND id = 198236567;
              
              UPDATE "order"
              SET update_time = NOW(), discount_amount = 117.06, total_amount = 388.78, deposit_amount = 279.56
              WHERE id = 149180474;
              
              -- 149180451
              UPDATE order_line_discount
              SET is_deleted = false, update_time = NOW()
              WHERE order_id = 149180451
              AND id >= 104179259 AND id <= 104179276;
              UPDATE order_line_discount
              SET is_deleted = true, update_time = NOW()
              WHERE order_id = 149180451
              AND id >= 104179280 AND id <= 104179297;
              
              UPDATE order_line_item
              SET discount_amount = sub_total_amount * 0.15, total_amount = sub_total_amount * 0.85, update_time = NOW()
              WHERE order_id = 149180451
              AND id >= 198236436 AND id <= 198236453;
              
              UPDATE "order"
              SET update_time = NOW(), discount_amount = 112.56, total_amount = 358.28, deposit_amount = 279.56
              WHERE id = 149180451;
              
              -- 149180425
              UPDATE order_line_discount
              SET is_deleted = false, update_time = NOW()
              WHERE order_id = 149180425
              AND id IN (104179250,104179251,104179252);
              UPDATE order_line_discount
              SET is_deleted = true, update_time = NOW()
              WHERE order_id = 149180425
              AND id IN (104179253,104179254,104179255);
              
              UPDATE order_line_item
              SET discount_amount = sub_total_amount * 0.1, total_amount = sub_total_amount * 0.9, update_time = NOW()
              WHERE order_id = 149180425
              AND id >= 198236327 AND id <= 198236329;
              
              UPDATE "order"
              SET update_time = NOW(), discount_amount = 18.48, total_amount = 106.92, deposit_amount = 59.4
              WHERE id = 149180425;
              
              -- 149179511
              UPDATE order_line_discount
              SET is_deleted = false, update_time = NOW()
              WHERE order_id = 149179511
              AND id IN (104179107,104179108,104179109);
              UPDATE order_line_discount
              SET is_deleted = true, update_time = NOW()
              WHERE order_id = 149179511
              AND id IN (104179111,104179112,104179113);
              
              UPDATE order_line_item
              SET discount_amount = sub_total_amount * 0.15, total_amount = sub_total_amount * 0.85, update_time = NOW()
              WHERE order_id = 149179511
              AND id >= 198229578 AND id <= 198229580;
              
              UPDATE "order"
              SET update_time = NOW(), discount_amount = 192.9, total_amount = 614.38, deposit_amount = 478.72
              WHERE id = 149179511;
              
              -- 149178789
              UPDATE order_line_discount
              SET is_deleted = false, update_time = NOW()
              WHERE order_id = 149178789
              AND id IN (104178946,104178947,104178948);
              UPDATE order_line_discount
              SET is_deleted = true, update_time = NOW()
              WHERE order_id = 149178789
              AND id IN (104178955,104178956,104178957);
              
              UPDATE order_line_item
              SET discount_amount = sub_total_amount * 0.15, total_amount = sub_total_amount * 0.85, update_time = NOW()
              WHERE order_id = 149178789
              AND id >= 198226314 AND id <= 198226316;
              UPDATE order_line_item
              SET discount_amount = 0, total_amount = 5, update_time = NOW()
              WHERE order_id = 149178789
              AND id = 198226317;
              
              UPDATE "order"
              SET update_time = NOW(), discount_amount = 90.45, total_amount = 420.65, deposit_amount = 96.9
              WHERE id = 149178789;
              
              -- 149159120
              UPDATE order_line_discount
              SET is_deleted = false, update_time = NOW()
              WHERE order_id = 149159120
              AND id >=104175275 AND id <= 104175279;
              UPDATE order_line_discount
              SET is_deleted = true, update_time = NOW()
              WHERE order_id = 149159120
              AND id >= 104175280 AND id <= 104175284;
              
              UPDATE order_line_item
              SET discount_amount = sub_total_amount * 0.15, total_amount = sub_total_amount * 0.85, update_time = NOW()
              WHERE order_id = 149159120
              AND id >= 198129816 AND id <= 198129820;
              UPDATE order_line_item
              SET discount_amount = 0, total_amount = 5, update_time = NOW()
              WHERE order_id = 149159120
              AND id = 198129821;
              
              UPDATE "order"
              SET update_time = NOW(), discount_amount = 94.56, total_amount = 361.79, deposit_amount = 179.05
              WHERE id = 149159120;
              
              -- 149156582
              UPDATE order_line_discount
              SET is_deleted = false, update_time = NOW()
              WHERE order_id = 149156582
              AND id >=104174396 AND id <= 104174404;
              UPDATE order_line_discount
              SET is_deleted = true, update_time = NOW()
              WHERE order_id = 149156582
              AND id >= 104174405 AND id <= 104174413;
              
              UPDATE order_line_item
              SET discount_amount = sub_total_amount * 0.1, total_amount = sub_total_amount * 0.9, update_time = NOW()
              WHERE order_id = 149156582
              AND id >= 198117104 AND id <= 198117112;
              
              UPDATE "order"
              SET update_time = NOW(), discount_amount = 37, total_amount = 162.87, deposit_amount = 170.13
              WHERE id = 149156582;
              
              -- 149156542
              UPDATE order_line_discount
              SET is_deleted = false, update_time = NOW()
              WHERE order_id = 149156542
              AND id >=104174361 AND id <= 104174369;
              UPDATE order_line_discount
              SET is_deleted = true, update_time = NOW()
              WHERE order_id = 149156542
              AND id >= 104174371 AND id <= 104174379;
              
              UPDATE order_line_item
              SET discount_amount = sub_total_amount * 0.1, total_amount = sub_total_amount * 0.9, update_time = NOW()
              WHERE order_id = 149156542
              AND id >= 198116977 AND id <= 198116985;
              
              UPDATE "order"
              SET update_time = NOW(), discount_amount = 37, total_amount = 162.87, deposit_amount = 170.13
              WHERE id = 149156542;
              
              -- 149122308
              UPDATE order_line_discount
              SET is_deleted = false, update_time = NOW()
              WHERE order_id = 149122308
              AND id IN (104169016,104169017);
              UPDATE order_line_discount
              SET is_deleted = true, update_time = NOW()
              WHERE order_id = 149122308
              AND id IN (104169020,104169021);
              
              UPDATE order_line_item
              SET discount_amount = sub_total_amount * 0.15, total_amount = sub_total_amount * 0.85, update_time = NOW()
              WHERE order_id = 149122308
              AND id >= 197978826 AND id <= 197978827;
              UPDATE order_line_item
              SET discount_amount = 0, total_amount = 5, update_time = NOW()
              WHERE order_id = 149122308
              AND id = 197978828;
              
              UPDATE "order"
              SET update_time = NOW(), discount_amount = 51.15, total_amount = 294.85
              WHERE id = 149122308;
              
              -- 149114831
              UPDATE order_line_discount
              SET is_deleted = false, update_time = NOW()
              WHERE order_id = 149114831
              AND id = 104167030;
              UPDATE order_line_discount
              SET is_deleted = true, update_time = NOW()
              WHERE order_id = 149114831
              AND id = 104167031;
              
              UPDATE order_line_item
              SET discount_amount = 97.02, total_amount = 549.78, update_time = NOW()
              WHERE order_id = 149114831
              AND id = 197946292;
              UPDATE order_line_item
              SET discount_amount = 0, total_amount = sub_total_amount, update_time = NOW()
              WHERE order_id = 149114831
              AND id IN (197946293,197946294);
              
              UPDATE "order"
              SET update_time = NOW(), discount_amount = 97.02, total_amount = 223.47, deposit_amount = 361.31
              WHERE id = 149114831;
              
              COMMIT;
