name: "moe_intake_form 新增 cover image & marketing policy"
description: "moe_intake_form 新增 cover image & marketing policy"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_customer"
            sql: |
              alter table moe_intake_form add column `cover_image` varchar(500) default '' not null comment '封面';
              alter table moe_intake_form add column `marketing_policy` varchar(500) default '' not null comment '运营政策';
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_customer"
            sql: |
              alter table moe_intake_form add column `cover_image` varchar(500) default '' not null comment '封面';
              alter table moe_intake_form add column `marketing_policy` varchar(500) default '' not null comment '运营政策';
