name: "update order: remove duplicate discount for CS-29970"
description: "update order: remove duplicate discount for CS-29970"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              UPDATE order_line_discount 
              SET is_deleted = true, update_time = NOW()
              WHERE id IN (104006436, 104006437, 104006438, 104006439, 104006440, 104006441);
