name: "modify email credit"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_message"
            sql: |
              UPDATE moe_message.moe_business_message_control 
              SET available_emails = 300, purchase_email_amount = 270
              WHERE id = 217683;
              