name: "Fix package payment status"
description: "Fix package payment status"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              UPDATE moe_payment.payment SET
                module = 'retail', status = 3, update_time = unix_timestamp(now())
                WHERE id = 30953856 AND status = 2;
