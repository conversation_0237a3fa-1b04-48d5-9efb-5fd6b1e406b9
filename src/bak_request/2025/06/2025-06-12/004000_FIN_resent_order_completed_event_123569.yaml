name: "resent order completed event for RD ohio "
description: "resent order completed event for RD ohio, company id = 121834"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              ---- 更新sync status
              UPDATE message_delivery
              SET status = 'PENDING'
              WHERE message_type = 'ORDER_COMPLETED'
                AND status = 'SENT'
                AND reference_id IN (SELECT id
                                     FROM "order"
                                     WHERE company_id = 121834 AND status = 2 AND source_type = 'appointment' AND order_version = 4);
