name: "moe_retail category&supplier add index"
description: "moe_retail category&supplier add index"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_retail"
            sql: |
              ALTER TABLE `moe_retail`.`category` 
              ADD INDEX `IDX_category_company_id`(`company_id`) USING BTREE;
              ALTER TABLE `moe_retail`.`supplier` 
              ADD INDEX `IDX_supplier_company_id`(`company_id`) USING BTREE;

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_retail"
            sql: |
              ALTER TABLE `moe_retail`.`category` 
              ADD INDEX `IDX_category_company_id`(`company_id`) USING BTREE;
              ALTER TABLE `moe_retail`.`supplier` 
              ADD INDEX `IDX_supplier_company_id`(`company_id`) USING BTREE;
              