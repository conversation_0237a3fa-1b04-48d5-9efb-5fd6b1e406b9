name: "<PERSON>ame 'complete invoice' to 'close invoice'"
description: "<PERSON>ame 'complete invoice' to 'close invoice'"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_permission"
            sql: |
              UPDATE public.permission SET display_name = 'Can close invoice manually' WHERE id = 101 AND display_name = 'Can complete invoice manually';
