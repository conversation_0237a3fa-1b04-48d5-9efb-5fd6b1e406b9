stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-grooming"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              update moe_grooming.moe_grooming_pet_detail
              set staff_id    = 183696,
                  update_time = unix_timestamp()
              where id in (
                           236267724,
                           236115461,
                           236109389,
                           236053033,
                           235820665,
                           235820664,
                           235810384,
                           235810383,
                           235474525,
                           235474524,
                           235277039,
                           235225483,
                           235220109,
                           235220108,
                           234881510,
                           234881509)
              and staff_id = 178698;
