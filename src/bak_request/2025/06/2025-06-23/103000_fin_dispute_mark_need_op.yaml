name: "mark dispute need to op manually"
description: "https://moegoworkspace.slack.com/archives/C07S1UNAK9Q/p1744641450293249?thread_ts=1744638774.396609&cid=C07S1UNAK9Q"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              UPDATE mm_stripe_dispute_event_log 
              SET status = 'OPS_HOLD', updated_at = CURRENT_TIMESTAMP()
              WHERE id IN (1809, 1810)
              AND dispute_id IN ('du_1RAWErIZwcIFVLGr1PfNldLN', 'du_1RAWijIZwcIFVLGrDI9N7IWC')
              ;
