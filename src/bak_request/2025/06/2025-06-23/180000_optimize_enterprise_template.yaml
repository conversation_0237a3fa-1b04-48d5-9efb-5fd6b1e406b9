name: "重构 service_template_mapping 表"
description: "重构 service_template_mapping 表"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_enterprise"
            sql: |
              ALTER TABLE service_template_mapping RENAME TO template_push_mapping;
              update template_push_mapping set template_type = 'SERVICE' ;
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_enterprise"
            sql: |
              ALTER TABLE service_template_mapping RENAME TO template_push_mapping;
              update template_push_mapping set template_type = 'SERVICE' ;