name: "update appt is new order for red dog"
description: "update appt is new order for red dog"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-grooming
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              UPDATE moe_grooming.appointment_extra_info 
              SET is_new_order = 0
              WHERE appointment_id IN (79030270,79369335,79293994,79348138,79395531,79360765,79296990,79417923,79350392,79348523,79360932,79421234,79366477,79293616,
                79231444,79347069,79433382,79436165,79362962,79409857,79407380,79339902,79440736,79377376,79427005,79377457,79347598,79231402,79032946,79420580,79348289,
                79420774,79432987,79345881,79364914,79267552,79394190,79367302,79377377,79339931,79054059,79361040,79034703,79377835,79042424,79377550,79380089,79389439,
                79231595,79325900,79426604,79437206,79231560,79345801,79380091,79394319,79396592,79413148,79378709,78928258,79300122,79407709,79438494,79231404,79347386,
                79300888,79379174,79054116,79383197,79315379,79439607,79396676,79231412,79237149,79369323,79380315,79421248,79392963,79349900,79361100,79344839,79231568,
                79384357,79060408,79369145,79241234,79355929,79370779,79354819,79401402,79426477,79388473,79366473,79237399,79377233,79438400,79392884,79366255,79408047,
                79231398,79345861,79231393,79443309,79305753,79231397,79344102,79354378,79409791,79427276,79438594,79362978,79273212,79423381,79033614,79401547
              )
              ;
