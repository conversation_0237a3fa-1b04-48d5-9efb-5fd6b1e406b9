name: "dml for automation admin"
description: "授权 automation 库权限给 chris"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_automation"
            username: "${db.user.admin}"
            sql: |
              GRANT CONNECT ON DATABASE moego_automation TO developer_chris;
              GRANT USAGE ON SCHEMA public TO developer_chris;
              GRANT SELECT, UPDATE, DELETE, INSERT ON ALL TABLES IN SCHEMA public TO developer_chris;
              ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, UPDATE, DELETE, INSERT ON TABLES TO developer_chris;
              GRANT USAGE,SELECT,UPDATE ON SEQUENCE workflow_id_seq TO developer_chris;
              GRANT USAGE,SELECT,UPDATE ON SEQUENCE step_id_seq TO developer_chris;