name: "resent order completed event for RD ohio "
description: "resent order completed event for RD ohio, 149080758,148927303,149081765,149081703,148904156,149081667"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              ---- 更新sync status
              UPDATE message_delivery
              SET status = 'PENDING'
              WHERE message_type = 'ORDER_COMPLETED'
              AND status = 'SENT'
              AND reference_id IN ('149080758','148927303','149081765','149081703','148904156','149081667')
              ;
