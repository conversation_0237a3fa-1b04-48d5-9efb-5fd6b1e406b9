name: "tenant add type column"
description: "tenant add type column"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_enterprise"
            sql: |
              ALTER TABLE "public"."tenant" 
              ADD COLUMN "type" text COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'NORMAL'::text;
              COMMENT ON COLUMN "public"."tenant"."type" IS 'see api/moego/models/enterprise/v1/tenant_models.proto:71';

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_enterprise"
            sql: |
              ALTER TABLE "public"."tenant" 
              ADD COLUMN "type" text COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'NORMAL'::text;
              COMMENT ON COLUMN "public"."tenant"."type" IS 'see api/moego/models/enterprise/v1/tenant_models.proto:71';
              