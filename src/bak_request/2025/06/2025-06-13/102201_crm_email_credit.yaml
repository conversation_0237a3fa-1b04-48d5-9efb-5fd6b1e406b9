name: "modify email credit"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_message"
            sql: |
              INSERT INTO moe_message.moe_business_message_control (company_id, cycle_begin_time, cycle_end_time,
              force_block_message, force_allow_message, force_block_email,
              subscription_amount, purchase_amount, purchase_leftover,
              used_2way_message, free_auto_message, used_auto_message,
              used_call, subscription_date, used_message, business_id,
              available_emails, used_emails, locked_emails,
              subscription_email_amount, purchase_email_amount,
              purchase_email_leftover)
              VALUES (122252, 1747296000, 1749974400, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 0, 0, 100, 0, 0, 0, 100,
              0),
              (118828, 1747296000, 1749974400, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 0, 0, 100, 0, 0, 0, 100,
              0);