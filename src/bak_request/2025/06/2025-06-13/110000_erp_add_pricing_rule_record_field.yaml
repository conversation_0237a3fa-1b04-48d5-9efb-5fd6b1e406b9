name: "add is charge per lodging field and update data"
description: "add is charge per lodging field and update data"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_offering"
            sql: |
              ALTER TABLE "public"."pricing_rule_record" 
              ADD COLUMN "is_charge_per_lodging" BOOLEAN NOT NULL DEFAULT FALSE;
              
              COMMENT ON COLUMN "public"."pricing_rule_record"."rule_apply_type"
              IS 'rule apply type, 1-each pet, 2-additional pets, 3-all pets(deprecated), 4-first pet';
  
              UPDATE "public"."pricing_rule_record"
              SET rule_apply_type = 4
              WHERE rule_apply_type = 3;

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_offering"
            sql: |
              ALTER TABLE "public"."pricing_rule_record" 
              ADD COLUMN "is_charge_per_lodging" BOOLEAN NOT NULL DEFAULT FALSE;
              
              COMMENT ON COLUMN "public"."pricing_rule_record"."rule_apply_type"
              IS 'rule apply type, 1-each pet, 2-additional pets, 3-all pets(deprecated), 4-first pet';
              
              UPDATE "public"."pricing_rule_record"
              SET rule_apply_type = 4
              WHERE rule_apply_type = 3;