name: "add table file_transfer_log"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_enterprise"
            sql: |
              CREATE TABLE file_transfer_log (
                  id BIGSERIAL PRIMARY KEY,
                  source_url TEXT NOT NULL UNIQUE default '',
                  s3_url TEXT NOT NULL default '',
                  created_at timestamp not null DEFAULT NOW(),
                  updated_at timestamp not null DEFAULT NOW()
              );
              CREATE TRIGGER update_at
                  BEFORE UPDATE
                  ON file_transfer_log
                  FOR EACH ROW
              EXECUTE PROCEDURE update_at();
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_enterprise"
            sql: |
              CREATE TABLE file_transfer_log (
                  id BIGSERIAL PRIMARY KEY,
                  source_url TEXT NOT NULL UNIQUE default '',
                  s3_url TEXT NOT NULL default '',
                  created_at timestamp not null DEFAULT NOW(),
                  updated_at timestamp not null DEFAULT NOW()
              );
              CREATE TRIGGER update_at
                  BEFORE UPDATE
                  ON file_transfer_log
                  FOR EACH ROW
              EXECUTE PROCEDURE update_at();