name: "Add date type to grooming service details"
description: "Add date type to grooming service details"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_online_booking"
            sql: |
              alter table public.grooming_service_detail 
              add column date_type integer default 3 not null;
              comment on column public.grooming_service_detail.date_type is 'date type, see moego.models.appointment.v1.PetDetailDateType';
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_online_booking"
            sql: |
              alter table public.grooming_service_detail
              add column date_type integer default 3 not null;
              comment on column public.grooming_service_detail.date_type is 'date type, see moego.models.appointment.v1.PetDetailDateType';