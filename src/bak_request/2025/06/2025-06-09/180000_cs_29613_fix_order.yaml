name: "修复订单数据"
description: "修复订单数据 https://moego.atlassian.net/browse/CS-29613"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              UPDATE public."order" SET
                status = 2,
                payment_status = 'PAID',
                paid_amount = paid_amount + 89.60,
                remain_amount = GREATEST(0, remain_amount - 89.60),
                update_time = NOW()
                WHERE business_id = 113466 AND id = 144932825;
