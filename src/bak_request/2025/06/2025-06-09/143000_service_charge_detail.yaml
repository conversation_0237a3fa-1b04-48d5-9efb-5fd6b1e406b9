name: "Add quantity and total_price column to service_charge_detail table"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-grooming"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              alter table service_charge_detail
              add column total_price decimal(10, 2) default 0.00 not null comment 'The total price of this service charge';
            
              alter table service_charge_detail
              add column quantity int default 1 not null comment 'The quantity of this apply service charge';
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-grooming"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              alter table service_charge_detail
              add column total_price decimal(10, 2) default 0.00 not null comment 'The total price of this service charge';
              
              alter table service_charge_detail
              add column quantity int default 1 not null comment 'The quantity of this apply service charge';