name: "grant markting db for better"
description: "授权 better 访问 markting db"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_marketing"
            username: "${db.user.admin}"
            sql: |
              GRANT CONNECT ON DATABASE moego_marketing TO developer_better;
              GRANT USAGE ON SCHEMA public TO developer_better;
              GRANT SELECT, UPDATE, DELETE, INSERT ON ALL TABLES IN SCHEMA public TO developer_better;
              ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, UPDATE, DELETE, INSERT ON TABLES TO developer_better;
