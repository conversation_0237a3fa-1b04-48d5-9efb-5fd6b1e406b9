name: "授权 admin_v3 库权限给 haozhi"
description: "授权 admin_v3 库权限给 haozhi"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_admin_v3"
            username: "${db.user.admin}"
            sql: |
              GRANT CONNECT ON DATABASE moego_admin_v3 TO developer_haozhi;
              GRANT USAGE ON SCHEMA public TO developer_haozhi;
              GRANT SELECT, UPDATE, DELETE, INSERT ON ALL TABLES IN SCHEMA public TO developer_haozhi;
              ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, UPDATE, DELETE, INSERT ON TABLES TO developer_haozhi;