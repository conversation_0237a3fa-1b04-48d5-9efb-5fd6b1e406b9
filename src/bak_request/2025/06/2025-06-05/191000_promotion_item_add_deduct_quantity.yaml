name: "order promotion add deduct quantity column"
description: "order promotion add deduct quantity column"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              ALTER TABLE order_promotion_item 
                ADD COLUMN deduct_quantity INT NOT NULL DEFAULT 0;
  
              COMMENT ON COLUMN order_promotion_item.deduct_quantity IS 'deduct quantity';
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              ALTER TABLE order_promotion_item 
                ADD COLUMN deduct_quantity INT NOT NULL DEFAULT 0;

              COMMENT ON COLUMN order_promotion_item.deduct_quantity IS 'deduct quantity';