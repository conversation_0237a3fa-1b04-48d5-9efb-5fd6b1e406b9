name: "add api token records for engagement"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_engagement"
            username: "${db.user.admin}"
            sql: |
              GRANT USAGE ON SCHEMA public TO developer_harvie;
              GRANT SELECT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO developer_harvie;
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_automation"
            username: "${db.user.admin}"
            sql: |
              GRANT CONNECT ON DATABASE moego_automation TO developer_harvie;
              GRANT USAGE ON SCHEMA public TO developer_harvie;
              GRANT SELECT, UPDATE, DELETE, INSERT ON ALL TABLES IN SCHEMA public TO developer_harvie;
              ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, UPDATE, DELETE, INSERT ON TABLES TO developer_harvie;
              GRANT USAGE,SELECT,UPDATE ON SEQUENCE workflow_id_seq TO developer_harvie;
              GRANT USAGE,SELECT,UPDATE ON SEQUENCE step_id_seq TO developer_harvie;
