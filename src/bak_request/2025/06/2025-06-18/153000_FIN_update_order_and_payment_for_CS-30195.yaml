name: "update order and payment for CS-30195"
description: "update order and payment for CS-30195"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              UPDATE payment SET status = 3, update_time = UNIX_TIMESTAMP() WHERE id = 31580069;
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              UPDATE "order" SET tips_amount = 5, total_amount = 67.69, paid_amount = 67.69, update_time = NOW()
              WHERE id = 149080858
              ;
              UPDATE order_payment SET processing_fee = 2.66, convenience_fee = 2.69, payment_status = 'ORDER_PAYMENT_STATUS_PAID', update_time = NOW()
              WHERE id = 100385434
              AND payment_id = 31580069
              ;
              UPDATE order_payment SET payment_status = 'ORDER_PAYMENT_STATUS_CANCELED'
              WHERE id = 100385428
              ;
