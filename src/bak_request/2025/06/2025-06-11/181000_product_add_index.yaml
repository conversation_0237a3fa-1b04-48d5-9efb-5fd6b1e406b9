name: "moe_retail product add index"
description: "moe_retail product add index"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_retail"
            sql: |
              ALTER TABLE `moe_retail`.`product` 
              ADD INDEX `IDX_product_company_id`(`company_id`) USING BTREE;

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_retail"
            sql: |
              ALTER TABLE `moe_retail`.`product` 
              ADD INDEX `IDX_product_company_id`(`company_id`) USING BTREE;
              