name: "forward number"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_message"
            sql: |
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 117303;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 118904;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 116746;
              update moe_message.moe_forward_business set to_phone_number = '+***********' where business_id = 116797;
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_engagement"
            sql: |
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 116760, 117303);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 118620, 118904);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 116228, 116746);
              insert into forward_business(to_phone_number, company_id, business_id) VALUES ('+***********', 116277, 116797);
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_sms"
            sql: |
              update business_sms_setting set twilio_number = '+***********' where business_id = 117303;
              update business_sms_setting set twilio_number = '+***********' where business_id = 118904;
              update business_sms_setting set twilio_number = '+***********' where business_id = 116746;
              update business_sms_setting set twilio_number = '+***********' where business_id = 116797;
