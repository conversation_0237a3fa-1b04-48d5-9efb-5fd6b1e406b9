name: "add theme_color and logo_path for moe_company"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_business"
            sql: |
              ALTER TABLE `moe_business`.`moe_company` 
                ADD COLUMN `theme_color` varchar(255) NOT NULL DEFAULT '' ,
                ADD COLUMN `logo_path` varchar(255) NOT NULL DEFAULT '' ;
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_business"
            sql: |
              ALTER TABLE `moe_business`.`moe_company` 
                ADD COLUMN `theme_color` varchar(255) NOT NULL DEFAULT '' ,
                ADD COLUMN `logo_path` varchar(255) NOT NULL DEFAULT '' ;