name: "GA support"
description: "add thank_you_page_url column for moe_book_online_landing_page_config"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              update moe_book_online_landing_page_config
              set thank_you_page_url = 'https://furryland.us/thank-you-booking/'
              where business_id in (
                                    104874, 106500, 107291, 107470, 108913, 108917, 110206, 110207, 112043, 113325, 113328, 115658,
                                    117686, 105808, 105811, 105812, 107395, 107994, 108916, 112040, 105810, 106498, 107995, 109293,
                                    110208, 110209, 110944, 120831, 107127, 107996, 110201, 110502, 110506, 112038, 112041, 113326,
                                    113327, 113564, 115063, 115729, 115752, 116930, 106499, 108910, 108914, 108918, 110204, 110505,
                                    110507, 112042, 113329, 113563, 114030, 115548, 115657, 116393, 119818, 119912, 121555, 105807,
                                    105809, 108909, 108911, 108912, 108915, 108919, 110202, 110205, 112039, 112044, 112045, 115463,
                                    115970, 116125, 116461
                  );
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              update moe_book_online_landing_page_config
              set thank_you_page_url = 'https://saltydawgpetsalon.com/booking-confirmation/atlanta'
              where business_id in (
                                    117676, 119733, 117858
                  );
