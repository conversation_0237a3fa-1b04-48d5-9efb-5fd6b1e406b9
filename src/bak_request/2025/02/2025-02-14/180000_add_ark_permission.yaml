name: "Grant permission to Ark"
description: "grant permission to <PERSON>"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_business"
            username: "${db.user.admin}"
            sql: |
              GRANT SELECT, INSERT, UPDATE, DELETE ON moe_business.* TO 'developer_ark'@'%';
