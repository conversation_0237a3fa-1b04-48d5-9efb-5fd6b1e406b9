name: "grant seq permission"
description: "grant seq permission"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_permission"
            sql: |
              GRANT USAGE, SELECT,UPDATE ON SEQUENCE role_id_seq TO developer_haozhi;
              ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT USAGE, SELECT, UPDATE ON SEQUENCES TO developer_haozhi;