name: "table abandoned_schedule_message_setting add enabled_at"
description: "table abandoned_schedule_message_setting add enabled_at"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              alter table moe_grooming.abandoned_schedule_message_setting
                add column enabled_at datetime default null comment '最近一次将 is_enabled 设置为 true 的时间';

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              alter table moe_grooming.abandoned_schedule_message_setting
                add column enabled_at datetime default null comment '最近一次将 is_enabled 设置为 true 的时间';
