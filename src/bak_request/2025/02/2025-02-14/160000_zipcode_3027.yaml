name: "Add 3027 zipcode data"
description: "Add 3027 zipcode data"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              INSERT INTO moe_grooming.moe_zipcode (zip_code, place_name, state, state_abbreviation, county, lat, lng,
                                      place_id, status)
              VALUES ('3027', 'Williams Landing', 'Victoria', 'VIC', 'Melbourne', '-37.8614859', '144.7439497',
              'ChIJ6dFHpQmF1moRME8uRnhWBBw', 0);