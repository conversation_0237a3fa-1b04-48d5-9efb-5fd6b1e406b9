name: "stripe customer removed"
description: "stripe customer remove"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              UPDATE moe_payment.mm_stripe_dispute 
              SET status = 'lost'
              WHERE payment_intent_id = 'pi_3QMZqfIZwcIFVLGr0Jkg3S8Z';

              UPDATE moe_payment.mm_stripe_dispute 
              SET status = 'won'
              WHERE payment_intent_id = 'pi_3QPWICIZwcIFVLGr1h7RKZgO';