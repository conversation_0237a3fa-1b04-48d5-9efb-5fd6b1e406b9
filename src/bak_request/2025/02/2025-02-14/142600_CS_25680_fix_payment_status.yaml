name: "CS-25680: fix payment status"
description: "Manually fix payment status for the orders which full redeemed by pkg."
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              UPDATE "order" SET payment_status = 'PAID', update_time = NOW() WHERE payment_status = 'UNPAID' AND business_id = 120469 AND id IN (131889820,132461548,132657815,132658151,132920841,132921673,133416493,133498103,133714154,133720837,133784782,133794950,133861061,134283391,134595531,134728298,134931224,135165130,135877193,136309061,136469202,137129526,137178848);
