name: "stripe customer removed"
description: "stripe customer remove"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              update mm_stripe_customer set status = 2, update_time = unix_timestamp() where business_id = 118438 and id in (1046719 ,1068550        ,1087226        ,1093270        ,1121633        ,1143025        ,1013052 ,1073295        ,1028967        ,1072026        ,1165143        ,1057035        ,1066568 ,1072341        ,1169671        ,1021540        ,1059198        ,1109264        ,1130008 ,1041703        ,1044460);