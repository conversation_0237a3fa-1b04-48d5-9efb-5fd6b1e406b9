name: "add customer entitlement"
description: "related slack: https://moegoworkspace.slack.com/archives/C01TT9K995M/p1738374068631079"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_membership"
            sql: |
              UPDATE "public"."membership_perk"
              SET "deleted_at" = now()
              WHERE "id" = 103;
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_subscription"
            sql: |
              UPDATE "public"."license"
              SET "status" = 'INVALID'
              WHERE "subscription_id" = 777;
              
              DELETE FROM "public"."entitlement"
              WHERE "id" IN (45435, 45436, 45434);

  

