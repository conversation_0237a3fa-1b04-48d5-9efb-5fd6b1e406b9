name: "sync accept_pet_entry_type column"
description: "sync accept_pet_entry_type column"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              update moe_book_online_question
              set accept_pet_entry_type = accepted_customer_type
              where accept_pet_entry_type = 0
                and accepted_customer_type in (1, 2, 3);

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              update moe_book_online_question
              set accept_pet_entry_type = accepted_customer_type
              where accept_pet_entry_type = 0
                and accepted_customer_type in (1, 2, 3);