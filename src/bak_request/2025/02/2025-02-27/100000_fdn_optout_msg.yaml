name: "0206-porting-number"
description: "0206-porting-number"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_message"
            sql: |
              CREATE TABLE moe_message_optout_config
              (
              id                         INT AUTO_INCREMENT PRIMARY KEY,
              company_id                 BIGINT                             NOT NULL,
              source                     VARCHAR(255)                       NOT NULL COMMENT 'MessageDetailEnum.MESSAGE_SOURCE',
              method                     VARCHAR(255)                       NOT NULL COMMENT 'MessageDetailEnum.MESSAGE_METHOD',
              
              optout_content_placeholder TEXT,
              optout_content             TEXT,
              optout_keyword             TEXT,
              optout_reply_content       TEXT,
              optin_content_placeholder  TEXT,
              optin_content              TEXT,
              optin_keyword              TEXT,
              optin_reply_content        TEXT,
              
              create_time                DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT 'create time',
              update_time                DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT 'update time'
              );
              
              CREATE UNIQUE INDEX idx_unique_company_source_method
              ON moe_message_optout_config (company_id, source, method);
              
              CREATE TABLE moe_message_optout_customer
              (
              id          INT AUTO_INCREMENT PRIMARY KEY,
              company_id  BIGINT                             NOT NULL,
              source      VARCHAR(255)                       NOT NULL COMMENT 'MessageDetailEnum.MESSAGE_SOURCE',
              method      VARCHAR(255)                       NOT NULL COMMENT 'MessageDetailEnum.MESSAGE_METHOD',
              customer_id BIGINT                             NOT NULL,
              status      TINYINT  DEFAULT 0                 NOT NULL COMMENT '0-Valid,1-Deleted',
              
              create_time DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT 'create time',
              update_time DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT 'update time'
              );
              
              CREATE UNIQUE INDEX idx_unique_company_source_method_customer
              ON moe_message_optout_customer (company_id, source, method, customer_id);
              
              CREATE INDEX idx_customer_method_source ON moe_message_optout_customer (customer_id, method, source);
