name: "add customer entitlement"
description: "related slack: https://moegoworkspace.slack.com/archives/C01TT9K995M/p1738374068631079"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_subscription"
            sql: |
              INSERT INTO "public"."entitlement" (
              "license_id",
              "product_id",
              "feature_id",
              "feature_key",
              "feature_name",
              "feature_description",
              "feature_setting"
              )
              VALUES (
              48200,
              561,
              216,
              'MEMBERSHIP_SERVICE_ADDON_QUANTITY',
              '120669-1537302-SERVICE',
              '120669-1537302-SERVICE',
              '{"count":{"totalAmount":"2"}}'
              );

