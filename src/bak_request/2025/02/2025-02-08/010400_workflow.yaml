name: "修改workflow数据"
description: "修改workflow数据"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_automation"
            sql: |
              select * from public.workflow_record where company_id = 9108 and status = 'PENDING';
              update public.workflow_record set status = 'SUCCESS' where company_id = 9108 and status = 'PENDING';