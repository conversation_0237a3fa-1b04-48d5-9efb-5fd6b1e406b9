name: "service charge 初始化 apply_type、 service_item_types字段"
description: "service charge 初始化 apply_type、 service_item_types字段"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              update public.service_charge set apply_type = 2 where auto_apply_status = 3;
              update public.service_charge set service_item_types = '{1,2,3,5}' where auto_apply_status = 2;

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              update public.service_charge set apply_type = 2 where auto_apply_status = 3;
              update public.service_charge set service_item_types = '{1,2,3,5}' where auto_apply_status = 2;
