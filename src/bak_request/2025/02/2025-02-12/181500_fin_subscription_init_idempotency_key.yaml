name: "subscription init idempotency key"
description: "subscription init idempotency key"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_subscription"
            sql: |
              UPDATE subscription 
              SET idempotency_key = 'subscription_' || id || '_' || floor(extract(epoch from now()))::text
              WHERE idempotency_key = '';

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_subscription"
            sql: |
              UPDATE subscription 
              SET idempotency_key = 'subscription_' || id || '_' || floor(extract(epoch from now()))::text
              WHERE idempotency_key = '';
