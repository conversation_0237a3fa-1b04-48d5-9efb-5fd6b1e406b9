name: "engagement add column resolve mark"
description: "engagement add column resolve mark"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_engagement"
            sql: |
              alter table calling_log
                  add column is_resolved boolean not null default false;
              create index calling_log_company_index
                  on public.calling_log (company_id);
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_engagement"
            sql: |
              alter table calling_log
                  add column is_resolved boolean not null default false;
              create index calling_log_company_index
                  on public.calling_log (company_id);
