name: "enterprise add column source"
description: "enterprise add column source"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_enterprise"
            sql: |
              ALTER TABLE "public"."enterprise" ALTER COLUMN "date_format_type" SET DEFAULT 'MM_DD_YYYY_LINE'::text;
              ALTER TABLE "public"."enterprise" ALTER COLUMN "time_format_type" SET DEFAULT 'HOUR_24'::text;
              ALTER TABLE "public"."enterprise" ALTER COLUMN "unit_of_weight_type" SET DEFAULT 'POUND'::text;
              ALTER TABLE "public"."enterprise" ALTER COLUMN "unit_of_distance_type" SET DEFAULT 'MILE'::text;
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_enterprise"
            sql: |
              ALTER TABLE "public"."enterprise" ALTER COLUMN "date_format_type" SET DEFAULT 'MM_DD_YYYY_LINE'::text;
              ALTER TABLE "public"."enterprise" ALTER COLUMN "time_format_type" SET DEFAULT 'HOUR_24'::text;
              ALTER TABLE "public"."enterprise" ALTER COLUMN "unit_of_weight_type" SET DEFAULT 'POUND'::text;
              ALTER TABLE "public"."enterprise" ALTER COLUMN "unit_of_distance_type" SET DEFAULT 'MILE'::text;