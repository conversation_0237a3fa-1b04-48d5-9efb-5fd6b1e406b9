name: "subscription add idempotency key"
description: "subscription add idempotency key"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_subscription"
            sql: |
              alter table subscription add column idempotency_key text default '';
              comment on column subscription.idempotency_key is 'idempotency key used to call vendor CreateSubscription';
