name: "enterprise add column source"
description: "enterprise add column source"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_enterprise"
            sql: |
              ALTER TABLE "public"."enterprise" ADD COLUMN "source" text NOT NULL DEFAULT 'NORMALLY_ADD';
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_enterprise"
            sql: |
              ALTER TABLE "public"."enterprise" ADD COLUMN "source" text NOT NULL DEFAULT 'NORMALLY_ADD';