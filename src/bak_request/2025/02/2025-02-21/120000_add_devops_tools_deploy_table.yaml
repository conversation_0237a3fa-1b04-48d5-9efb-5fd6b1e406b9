name: "add theme_color and logo_path for moe_company"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: testing-postgres-aurora
      actions:
        - execute_sql:
            database: "moego_tools"
            sql: |
              CREATE TABLE deploy_task (
                  id SERIAL PRIMARY KEY,
                  project_id INTEGER NOT NULL,
                  project_name VARCHAR(255) NOT NULL,
                  created_by VARCHAR(255) NOT NULL,
                  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                  status VARCHAR(50) NOT NULL,
                  current_phase VARCHAR(50),
                  parameters JSONB
              );

              CREATE TABLE deploy_phase (
                  id SERIAL PRIMARY KEY,
                  deploy_task_id INTEGER NOT NULL,
                  third_party_id VARCHAR(255),
                  phase_name VARCHAR(50) NOT NULL,
                  status VARCHAR(50) NOT NULL,
                  parameters JSONB,
                  started_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                  ended_at TIMESTAMPTZ
              );

              CREATE TABLE deploy_log (
                  id SERIAL PRIMARY KEY,
                  deploy_task_id INTEGER NOT NULL,
                  timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                  type VARCHAR(50) NOT NULL,
                  message JSONB
              );
