name: "insert capital interval"
description: "https://moegoworkspace.slack.com/archives/C071M1D10L9/p1740115124235589?thread_ts=17********.902849&cid=C071M1D10L9"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              INSERT INTO moe_payment.loan_repayment_interval (company_id, entity_id, entity_type, offer_id, channel_offer_id,
                                                 channel_name, channel_account_id, sequence, begin_at, due_at,
                                                 minimum_amount, paid_amount, version, created_time, updated_time)
              VALUES (113993, 114426, 'business', 'PLO11144261ZGcy17ea692e133d06d9b53f', 'financingoffer_1PmHfdIZwcIFVLGrlZiJZGcy',
                      'STRIPE', 'acct_1OB9KeIM0rfVZ9XP', 4, '2025-02-15 00:00:00', '2025-04-15 00:00:00', 719.34, 0.00, 1, DEFAULT,
                      DEFAULT);
