name: "add api token records for engagement"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_engagement"
            username: "${db.user.admin}"
            sql: |
              GRANT SELECT, DELETE, INSERT ON TABLE awsdms_ddl_audit TO PUBLIC;
              GRANT USAGE, SELECT ON SEQUENCE awsdms_ddl_audit_c_key_seq TO PUBLIC;
              DROP TABLE engagement_api_token;
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_engagement"
            sql: |
              CREATE TABLE IF NOT EXISTS engagement_api_token (
                  id BIGSERIAL PRIMARY KEY,
                  token VARCHAR(32) NOT NULL,
                  description TEXT,
                  deleted_at TIMESTAMP(6),
                  updated_at TIMESTAMP(6) NOT NULL DEFAULT NOW(),
                  created_at TIMESTAMP(6) NOT NULL DEFAULT NOW()
              );
              CREATE INDEX idx_engagement_api_token_deleted_at ON engagement_api_token(deleted_at);
              CREATE INDEX idx_engagement_api_token_token ON engagement_api_token(token); 
              insert into engagement_api_token(token, description)
              values (md5('slack-app'), 'slack app tool for manage the whitelist');
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_engagement"
            username: "${db.user.admin}"
            sql: |
              GRANT USAGE ON SCHEMA public TO developer_better;
              GRANT SELECT ON ALL TABLES IN SCHEMA public TO developer_better;
