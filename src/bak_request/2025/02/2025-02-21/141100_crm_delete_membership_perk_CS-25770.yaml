name: "delete customer entitlement"
description: "related jira: https://moego.atlassian.net/browse/CS-25770"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_membership"
            sql: |
              UPDATE "public"."membership_perk"
              SET "deleted_at" = now()
              WHERE "id" = 204;
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_subscription"
            sql: |
              UPDATE "public"."license"
              SET "status" = 'INVALID'
              WHERE "subscription_id" = 878;
              
              DELETE FROM "public"."entitlement"
              WHERE "id" = 49854;
              
              

  

