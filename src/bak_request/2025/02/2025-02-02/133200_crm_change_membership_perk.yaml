name: "change membership in subscription perk"
description: "related slack: https://moegoworkspace.slack.com/archives/C07RYT10C7P/p1738376637124169"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_membership"
            sql: |
              UPDATE "public"."membership_perk"
              SET "validity_end_time" = "validity_start_time" + INTERVAL '1 month'
              WHERE ABS(EXTRACT(EPOCH FROM ("validity_end_time" - "validity_start_time"))) / 3600 <= 24;
              
              UPDATE "public"."membership_quantity_benefits"
              SET 
                  "period_unit" = 'MONTH',
                  "period_value" = 1,
                  "period_type" = 'FOLLOW_MEMBERSHIP'
              WHERE "period_unit" = 'CALENDAR_PERIOD_UNSPECIFIED';
              
              UPDATE "public"."membership_quantity_benefits"
              SET
              "period_unit" = 'WEEK',
              "period_value" = 1,
              "period_type" = 'FOLLOW_MEMBERSHIP'
              WHERE "id" IN (161, 162, 163, 166, 167, 168, 169);
  
              UPDATE "public"."membership_quantity_benefits" 
              SET      
              "period_unit" = 'YEAR',     
              "period_value" = 1,     
              "period_type" = 'FOLLOW_MEMBERSHIP'
              WHERE "id" = 60;

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_subscription"
            sql: |
              UPDATE "public"."entitlement"
              SET
              "feature_id" = 561
              WHERE "id" IN (44269, 257, 44693);