name: "修改用户invoice支付数据"
description: "修改用户invoice支付数据"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              UPDATE public.order
              SET 
                  status = 2, 
                  payment_status = 'PAID', 
                  tips_amount = 24.00,
                  total_amount = 144.00, 
                  paid_amount = 144.00,
                  remain_amount = 0.00, 
                  update_time = NOW()
              WHERE 
                  id = 135769994;