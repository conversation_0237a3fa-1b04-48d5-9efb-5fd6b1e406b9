name: "修改用户支付数据"
description: "修改用户支付数据"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              UPDATE public.order
              SET 
                status = 2,
                payment_status = 'fully paid',
                total_amount = total_amount - 4.61,
                remain_amount = remain_amount - 4.61,
                update_time = NOW()
              WHERE 
                  id = 134568300 
                  AND business_id = '116393';
              
              UPDATE public.order_line_extra_fee
              SET is_deleted = 'true',update_time = NOW()
              where id = 117760749 and business_id = '114043' and order_id = '132435738';