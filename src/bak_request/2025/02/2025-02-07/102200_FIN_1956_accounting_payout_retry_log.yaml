name: "accounting retry log modify"
description: "accounting retry log modify"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_accounting"
            sql: |
              DELETE FROM public.accounting_sync_retry_log WHERE retry_entity_type = 'PAYOUT' AND retry_entity_id IN ('po_1OqyR9RNTSlz6Pch9Ql1yEqa');