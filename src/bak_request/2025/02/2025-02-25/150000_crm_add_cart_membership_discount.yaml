name: "cart 模块增加 membership 折扣"
description: "cart 模块增加 membership 折扣"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_retail"
            sql: |
              CREATE TABLE `cart_membership_discount` (
                `id` bigint NOT NULL AUTO_INCREMENT,
                `company_id` bigint NOT NULL DEFAULT '0' COMMENT 'company id',
                `cart_id` int NOT NULL DEFAULT '0' COMMENT 'cart id',
                `discount_type` varchar(20) NOT NULL DEFAULT '' COMMENT 'discount type (amount/percentage)',
                `discount_amount` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT 'discount amount',
                `discount_rate` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT 'discount rate',
                `membership_discount_id` bigint NOT NULL DEFAULT '0' COMMENT 'membership discount id',
                `membership_id` bigint NOT NULL DEFAULT '0' COMMENT 'membership id',
                `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
                `cart_item_id` int NOT NULL DEFAULT '0' COMMENT 'cart item id',
                `price_reduction` decimal(20,2) DEFAULT NULL COMMENT 'price reduction amount',
                PRIMARY KEY (`id`),
                KEY `cart_discount_company_id_cart_id_index` (`company_id`,`cart_id`)
                ) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_retail"
            sql: |
              CREATE TABLE `cart_membership_discount` (
                `id` bigint NOT NULL AUTO_INCREMENT,
                `company_id` bigint NOT NULL DEFAULT '0' COMMENT 'company id',
                  `cart_id` int NOT NULL DEFAULT '0' COMMENT 'cart id',
                  `discount_type` varchar(20) NOT NULL DEFAULT '' COMMENT 'discount type (amount/percentage)',
                  `discount_amount` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT 'discount amount',
                  `discount_rate` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT 'discount rate',
                  `membership_discount_id` bigint NOT NULL DEFAULT '0' COMMENT 'membership discount id',
                  `membership_id` bigint NOT NULL DEFAULT '0' COMMENT 'membership id',
                  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
                  `cart_item_id` int NOT NULL DEFAULT '0' COMMENT 'cart item id',
                  `price_reduction` decimal(20,2) DEFAULT NULL COMMENT 'price reduction amount',
                  PRIMARY KEY (`id`),
                  KEY `cart_discount_company_id_cart_id_index` (`company_id`,`cart_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;