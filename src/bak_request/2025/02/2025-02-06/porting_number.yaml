name: "0206-porting-number"
description: "0206-porting-number"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_message"
            sql: |
              UPDATE moe_message.moe_forward_business
              SET to_phone_number = '+***********'
              WHERE to_phone_number = '+***********';
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_message"
            sql: |
              UPDATE moe_message.moe_forward_business
              SET to_phone_number = '+***********'
              WHERE to_phone_number = '+***********';
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_engagement"
            sql: |
              UPDATE public.forward_business
              SET to_phone_number = '+***********'
              WHERE to_phone_number = '+***********';
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_engagement"
            sql: |
              UPDATE public.forward_business
              SET to_phone_number = '+***********'
              WHERE to_phone_number = '+***********';

