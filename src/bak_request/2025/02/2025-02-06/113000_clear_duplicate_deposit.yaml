name: "Clear duplicate deposit"
description: "Clear duplicate deposit"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              delete from moe_grooming.moe_invoice_deposit
              where id = 251660;
              
              update moe_grooming.moe_invoice_deposit
              set amount = 22.26,
              update_time  = now()
              where id = 251659;
