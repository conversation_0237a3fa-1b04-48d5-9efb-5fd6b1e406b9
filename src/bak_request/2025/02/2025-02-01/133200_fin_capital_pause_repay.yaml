name: "pause repay"
description: "related slack: https://moegoworkspace.slack.com/archives/C07EN0YBPT3/p1737071161560319"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              UPDATE loan_offer
              SET offer_status = 'REPLACED',
              channel_offer_status = 'REFINANCED',
              version = 51
              WHERE offer_id = 'PLO2MCA0000002181b8d8afbe535cfc9e5' and version = 50;

              INSERT INTO moe_payment.loan_offer_update_log (offer_id, channel_offer_id, before_offer_status, cur_offer_status,
                                                            before_channel_offer_status, cur_channel_offer_status, cur_version,
                                                            before_remain_amount, cur_remain_amount, event_type, event_id,
                                                            created_time, updated_time, before_processing_amount,
                                                            after_processing_amount, before_channel_offer_id)
              VALUES ('PLO2MCA0000002181b8d8afbe535cfc9e5', 'abf947d2-a546-44f9-b401-28675078e86f', 'PAID_OUT', 'PAID_OUT',
                      'CURRENT', 'REFINANCED', 51, 9347.7, 9347.7, 'refinanced by chi', NOW(), DEFAULT, DEFAULT, 0.00, 0.00,
                      'abf947d2-a546-44f9-b401-28675078e86f');


              UPDATE loan_offer
              SET offer_status = 'REPLACED',
              channel_offer_status = 'REFINANCED',
              version = 51
              WHERE offer_id = 'PLO2MCA0000002181b8d8dd52db669bf2b' and version = 50;

              INSERT INTO moe_payment.loan_offer_update_log (offer_id, channel_offer_id, before_offer_status, cur_offer_status,
                                                            before_channel_offer_status, cur_channel_offer_status, cur_version,
                                                            before_remain_amount, cur_remain_amount, event_type, event_id,
                                                            created_time, updated_time, before_processing_amount,
                                                            after_processing_amount, before_channel_offer_id)
              VALUES ('PLO2MCA0000002181b8d8dd52db669bf2b', '3d2eb996-1774-4390-9640-4078bf022ae5', 'PAID_OUT', 'PAID_OUT',
                      'CURRENT', 'REFINANCED', 51, 38070.33, 38070.33, 'refinanced by chi', NOW(), DEFAULT, DEFAULT, 0.00, 0.00,
                      '3d2eb996-1774-4390-9640-4078bf022ae5');