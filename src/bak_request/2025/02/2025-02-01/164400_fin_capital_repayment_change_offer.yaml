name: "change offer for repayments"
description: "related slack: https://moegoworkspace.slack.com/archives/C07EN0YBPT3/p1737793683962779"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              UPDATE loan_offer_repayment_transaction
              SET offer_id = 'PLO2MCA0000002182000598df9620b77c', 
              channel_offer_id = '55eb92fa-6ac0-4b35-8b4c-69b1f8df265c',
              updated_time = CURRENT_TIMESTAMP()
              WHERE status = 'TRANSFERRED' AND channel_offer_id = '3d2eb996-1774-4390-9640-4078bf022ae5';

              UPDATE loan_offer
              SET version = 3, remaining_amount = remaining_amount - 282.43,
              updated_time = CURRENT_TIMESTAMP()
              WHERE offer_id = 'PLO2MCA0000002182000598df9620b77c' and version = 2;
