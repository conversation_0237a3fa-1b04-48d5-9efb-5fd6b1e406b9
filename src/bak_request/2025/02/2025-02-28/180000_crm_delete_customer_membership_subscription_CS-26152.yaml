name: "delete customer subscription https://moego.atlassian.net/browse/CS-26152"
description: "related slack: https://moegoworkspace.slack.com/archives/C032UFCSH1R/p1740696987753219"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_subscription"
            sql: |
              UPDATE "public"."subscription"
              SET "status" = 'INCOMPLETE'
              WHERE "id" = 948;



