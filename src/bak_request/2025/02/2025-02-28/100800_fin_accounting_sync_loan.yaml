name: "Alter biz loan column"
description: "Alter biz loan column"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_accounting"
            sql: |
              DROP INDEX IF EXISTS public.uniq_biz_loan_id;
              ALTER TABLE public.biz_loan ALTER COLUMN loan_id TYPE TEXT USING loan_id::TEXT;
              CREATE UNIQUE INDEX uniq_biz_loan_id
              ON public.biz_loan USING btree (loan_id text_pattern_ops, channel_type text_pattern_ops);

              DROP INDEX IF EXISTS public.uniq_biz_loan_repayment_id;
              ALTER TABLE public.biz_loan_repayment
              ALTER COLUMN loan_repayment_id TYPE TEXT USING loan_repayment_id::TEXT;
              ALTER TABLE public.biz_loan_repayment DROP COLUMN loan_id;
              CREATE UNIQUE INDEX uniq_biz_loan_repayment_id
              ON public.biz_loan_repayment USING btree (loan_repayment_id text_pattern_ops, channel_type text_pattern_ops);
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_accounting"
            sql: |
              DROP INDEX IF EXISTS public.uniq_biz_loan_id;
              ALTER TABLE public.biz_loan ALTER COLUMN loan_id TYPE TEXT USING loan_id::TEXT;
              CREATE UNIQUE INDEX uniq_biz_loan_id
              ON public.biz_loan USING btree (loan_id text_pattern_ops, channel_type text_pattern_ops);

              DROP INDEX IF EXISTS public.uniq_biz_loan_repayment_id;
              ALTER TABLE public.biz_loan_repayment
              ALTER COLUMN loan_repayment_id TYPE TEXT USING loan_repayment_id::TEXT;
              ALTER TABLE public.biz_loan_repayment DROP COLUMN loan_id;
              CREATE UNIQUE INDEX uniq_biz_loan_repayment_id
              ON public.biz_loan_repayment USING btree (loan_repayment_id text_pattern_ops, channel_type text_pattern_ops);


