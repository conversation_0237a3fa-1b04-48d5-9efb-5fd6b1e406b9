name: "Change category for services"
description: "Change category for services"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-cluster"
      actions:
        execute_sql:
          database: "moe_grooming"
          sql: |
            update moe_grooming.moe_grooming_service
            set category_id = 0
            where id in (1556268, 1556287);
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        execute_sql:
          database: "moe_grooming"
          sql: |
            update moe_grooming.moe_grooming_service
            set category_id = 0
            where id in (1556268, 1556287);
