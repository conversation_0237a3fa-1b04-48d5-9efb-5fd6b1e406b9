name: "Add loan message delivery"
description: "Add loan message delivery"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-cluster"
      actions:
        execute_sql:
            database: "moe_payment"
            sql: |
              CREATE TABLE moe_payment.loan_message_delivery
              (
                id BIGINT AUTO_INCREMENT PRIMARY KEY comment 'Unique ID for each message',
                created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL comment 'Creation time',
                updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL comment 'Last update time',
                message_type VARCHAR(256) NOT NULL DEFAULT '' comment 'Type of message (e.g., payout, refund, etc.)',
                reference_id VARCHAR(256) NOT NULL DEFAULT '' comment 'Foreign key to the relevant entity (e.g., offer ID, transaction ID)',
                payload TEXT NULL comment 'Actual message content stored as JSON',
                status VARCHAR(64) NOT NULL DEFAULT 'EVENT_DELIVERY_STATUS_UNSPECIFIED' comment 'Status of the message (PENDING, SENT, FAILED, etc.)',
                retry_count INT DEFAULT 0 comment 'Number of retry attempts',
                last_attempt_time TIMESTAMP NULL DEFAULT NULL comment 'Timestamp of the last attempt to send'
              ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='message delivery';

              CREATE INDEX idx_message_type_reference_id USING BTREE ON moe_payment.loan_message_delivery (message_type,reference_id);
              CREATE INDEX idx_status USING BTREE ON moe_payment.loan_message_delivery (status);
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        execute_sql:
            database: "moe_payment"
            sql: |
              CREATE TABLE moe_payment.loan_message_delivery
              (
                id BIGINT AUTO_INCREMENT PRIMARY KEY comment 'Unique ID for each message',
                created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL comment 'Creation time',
                updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL comment 'Last update time',
                message_type VARCHAR(256) NOT NULL DEFAULT '' comment 'Type of message (e.g., payout, refund, etc.)',
                reference_id VARCHAR(256) NOT NULL DEFAULT '' comment 'Foreign key to the relevant entity (e.g., offer ID, transaction ID)',
                payload TEXT NULL comment 'Actual message content stored as JSON',
                status VARCHAR(64) NOT NULL DEFAULT 'EVENT_DELIVERY_STATUS_UNSPECIFIED' comment 'Status of the message (PENDING, SENT, FAILED, etc.)',
                retry_count INT DEFAULT 0 comment 'Number of retry attempts',
                last_attempt_time TIMESTAMP NULL DEFAULT NULL comment 'Timestamp of the last attempt to send'
              ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='message delivery';

              CREATE INDEX idx_message_type_reference_id USING BTREE ON moe_payment.loan_message_delivery (message_type,reference_id);
              CREATE INDEX idx_status USING BTREE ON moe_payment.loan_message_delivery (status);
  - api: "/moego.devops.api.console.v1.MessageQueueApi/CreateTopics"
    body:
      identifier:
        platform: AWS_MSK
        identifier: "kafka-staging"
      topics:
        - name: "moego.capital.staging"
  - api: "/moego.devops.api.console.v1.MessageQueueApi/CreateTopics"
    body:
      identifier:
        platform: AWS_MSK
        identifier: "moego-kafka"
      topics:
        - name: "moego.capital.production"
