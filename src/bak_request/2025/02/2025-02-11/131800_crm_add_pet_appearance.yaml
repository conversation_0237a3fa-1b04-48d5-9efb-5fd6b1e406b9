name: "customer pet 添加 pet appearance 字段"
description: "customer pet 添加 pet appearance 字段"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_customer"
            sql: |
              ALTER TABLE moe_customer.moe_customer_pet
              ADD COLUMN pet_appearance_notes VARCHAR(512) NOT NULL DEFAULT '' COMMENT 'pet appearance notes',
              ADD COLUMN pet_appearance_color VARCHAR(128) NOT NULL DEFAULT '' COMMENT 'pet appearance color';
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_customer"
            sql: |
              ALTER TABLE moe_customer.moe_customer_pet
              ADD COLUMN pet_appearance_notes VARCHAR(512) NOT NULL DEFAULT '' COMMENT 'pet appearance notes',
              ADD COLUMN pet_appearance_color VARCHAR(128) NOT NULL DEFAULT '' COMMENT 'pet appearance color';