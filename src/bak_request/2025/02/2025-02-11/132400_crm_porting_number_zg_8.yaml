name: "Porting number"
description: "Porting number"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_message"
            sql: |
              INSERT INTO moe_message.moe_forward_business (to_phone_number, company_id, business_id)
                            VALUES ('+***********', 118620, 118904);
              INSERT INTO moe_message.moe_forward_business (to_phone_number, company_id, business_id)
                            VALUES ('+***********', 112112, 112483);
              INSERT INTO moe_message.moe_forward_business (to_phone_number, company_id, business_id)
                            VALUES ('+***********', 116228, 116746);
              INSERT INTO moe_message.moe_forward_business (to_phone_number, company_id, business_id)
                            VALUES ('+***********', 116760, 117303);
              INSERT INTO moe_message.moe_forward_business (to_phone_number, company_id, business_id)
                            VALUES ('+***********', 114302, 114741);
              INSERT INTO moe_message.moe_forward_business (to_phone_number, company_id, business_id)
                            VALUES ('+***********', 115367, 115841);
              INSERT INTO moe_message.moe_forward_business (to_phone_number, company_id, business_id)
                            VALUES ('+***********', 112825, 113214);
              INSERT INTO moe_message.moe_forward_business (to_phone_number, company_id, business_id)
                            VALUES ('+***********', 116277, 116797);
