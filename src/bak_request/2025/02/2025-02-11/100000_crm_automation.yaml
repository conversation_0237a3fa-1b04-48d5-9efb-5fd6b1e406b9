name: "pg moego_automation 库清空表数据"
description: "pg moego_automation 库清空表数据"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_automation"
            sql: |
              UPDATE workflow_record SET status = 'FAIL' WHERE company_id = 120785 AND status = 'PENDING';