name: "service charge 添加 apply_type 字段"
description: "service charge 添加 apply_type 字段"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              alter table public.service_charge
              add apply_type integer default 1 not null;

              comment on column public.service_charge.apply_type is '1-PER_APPOINTMENT; 2-PER_PET';

              update public.service_charge set apply_type = 2 where auto_apply_status = 3;

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              alter table public.service_charge
              add apply_type integer default 1 not null;

              comment on column public.service_charge.apply_type is '1-PER_APPOINTMENT; 2-PER_PET';

              update public.service_charge set apply_type = 2 where auto_apply_status = 3;
