name: "moe_book_online_question add accept_pet_entry_type column"
description: "moe_book_online_question add accept_pet_entry_type column"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              alter table moe_grooming.moe_book_online_question
                add accept_pet_entry_type tinyint default 0 not null comment '当 type 为 pet 时，question 应用到的 pet 类型，see AcceptPetEntryType';

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              alter table moe_grooming.moe_book_online_question
                add accept_pet_entry_type tinyint default 0 not null comment '当 type 为 pet 时，question 应用到的 pet 类型，see AcceptPetEntryType';
