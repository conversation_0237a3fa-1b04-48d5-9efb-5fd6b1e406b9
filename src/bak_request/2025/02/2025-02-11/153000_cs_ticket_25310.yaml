name: "删除 arrival window 配置"
description: "有个商家 mobile 转 salon，没清空 arrival window 配置，导致发送 reminder 时带 arrival window"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_message"
            sql: |
              update moe_message.moe_business_reminder set status = 2, update_time = unix_timestamp() where business_id = 110736 and reminder_type = 6;
