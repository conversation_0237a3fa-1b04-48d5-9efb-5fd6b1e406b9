name: "Add assigned task field"
description: "Add assigned task field"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_business"
            sql: |
              alter table moe_business.moe_staff_notification
              add column assigned_task tinyint default 2 not null comment 'Assign task to staff';
              
              GRANT SELECT, INSERT, UPDATE, DELETE ON moe_business.* TO 'developer_jason'@'%';
