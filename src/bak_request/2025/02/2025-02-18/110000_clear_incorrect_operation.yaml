name: "清理错误的 multi staff 数据"
description: "清理错误的 multi staff 数据"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              delete from moe_grooming.moe_grooming_service_operation where grooming_id in (64476501,64476503,64476504,64476505,64476506,64476507,64476508);