name: "Add start_date for daycare_add_on_detail"
description: "Add start_date for daycare_add_on_detail"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_online_booking"
            sql: |
              alter table public.boarding_add_on_detail
                add column start_date date default null;
  
              comment on column public.boarding_add_on_detail.start_date is '当 date_type 为 PET_DETAIL_DATE_SPECIFIC_DATE 时，使用该字段表示具体日期';

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_online_booking"
            sql: |
              alter table public.boarding_add_on_detail
                add column start_date date default null;
              
              comment on column public.boarding_add_on_detail.start_date is '当 date_type 为 PET_DETAIL_DATE_SPECIFIC_DATE 时，使用该字段表示具体日期';
