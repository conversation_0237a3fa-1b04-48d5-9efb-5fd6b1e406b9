name: "Grant marketing permission to <PERSON>"
description: "Grant marketing permission to <PERSON>"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_marketing"
            username: "${db.user.admin}"
            sql: |
              GRANT CONNECT ON DATABASE moego_marketing TO developer_freeman;
              GRANT USAGE ON SCHEMA public TO developer_freeman;
              GRANT SELECT ON ALL TABLES IN SCHEMA public TO developer_freeman;
              ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO developer_freeman;

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_marketing"
            username: "${db.user.admin}"
            sql: |
              GRANT CONNECT ON DATABASE moego_marketing TO developer_freeman;
              GRANT USAGE ON SCHEMA public TO developer_freeman;
              GRANT SELECT ON ALL TABLES IN SCHEMA public TO developer_freeman;
              ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO developer_freeman;
