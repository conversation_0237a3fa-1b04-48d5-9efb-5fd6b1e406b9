name: "subscription add price payment_price_id index"
description: "subscription add price payment_price_id index"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_subscription"
            sql: |
              create index idx_price_payment_price_id on price_include_tax(payment_price_id);

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_subscription"
            sql: |
              create index idx_price_payment_price_id on price_include_tax(payment_price_id);

