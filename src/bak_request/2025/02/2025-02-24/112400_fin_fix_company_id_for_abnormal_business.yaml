name: "FIN-1678 给 Business ID 异常的订单修复 Company ID"
description: "脚本刷完数据之后发现部分订单的 business ID 为负数，需要人工介入修复。负数 ID 是 Business 注销等导致的，均为 2022 年的历史数据可安全的进行 company id 调整"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              UPDATE "order" SET company_id = 101095, update_time = NOW() WHERE business_id = -101147 AND id IN (20004392,20004396);
              UPDATE "order" SET company_id = 102887, update_time = NOW() WHERE business_id = -102975 AND id IN (20000241,20000256,20002763);