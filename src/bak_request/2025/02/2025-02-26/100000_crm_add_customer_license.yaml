name: "add customer license"
description: "related slack: https://moego.atlassian.net/browse/CS-25950"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_subscription"
            sql: |
              INSERT INTO "public"."license" (
              "subscription_id",
              "owner_id",
              "owner_type",
              "status"
              )
              VALUES (
              790,
              18169499,
              'CUSTOMER',
              'VALID'
              );