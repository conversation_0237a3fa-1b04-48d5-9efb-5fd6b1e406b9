name: "add customer license"
description: "related slack: https://moegoworkspace.slack.com/archives/C01TT9K995M/p1738374068631079"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_subscription"
            sql: |
              INSERT INTO "public"."license" (
              "subscription_id",
              "owner_id",
              "owner_type",
              "status"
              )
              VALUES (
              742,
              18169518,
              'CUSTOMER',
              'VALID'
              );