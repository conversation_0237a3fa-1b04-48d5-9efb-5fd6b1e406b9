name: "moe_book_online_abandon_record add is_send_notification column"
description: "moe_book_online_abandon_record add is_send_notification column"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              alter table moe_book_online_abandon_record
                add is_notification_sent tinyint(1) default 0 not null comment '是否发送过 notification';

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              alter table moe_book_online_abandon_record
                add is_notification_sent tinyint(1) default 0 not null comment '是否发送过 notification';
