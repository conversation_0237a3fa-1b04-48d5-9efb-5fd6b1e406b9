name: "membership delete name check"
description: "jira: https://moego.atlassian.net/browse/CRM-2573"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_membership"
            sql: |
              ALTER TABLE "public"."membership" DROP CONSTRAINT "membership_name_check";
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_membership"
            sql: |
              ALTER TABLE "public"."membership" DROP CONSTRAINT "membership_name_check";

