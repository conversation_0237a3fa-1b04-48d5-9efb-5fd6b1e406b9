name: "membership delete name check"
description: "jira: https://moego.atlassian.net/browse/CRM-2573"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              -- message_delivery add column delete_time
              alter table message_delivery add column delete_time TIMESTAMP DEFAULT NULL;
              create unique index uniq_type_refId_none_delete on message_delivery (message_type, reference_id) where delete_time is null;
              COMMENT ON INDEX uniq_type_refId_none_delete IS 'unique index for message_type and reference id for all none delete records';

              drop index if exists uniq_type_reference_id;
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              -- message_delivery add column delete_time
              alter table message_delivery add column delete_time TIMESTAMP DEFAULT NULL;
              create unique index uniq_type_refId_none_delete on message_delivery (message_type, reference_id) where delete_time is null;
              COMMENT ON INDEX uniq_type_refId_none_delete IS 'unique index for message_type and reference id for all none delete records';

              drop index if exists uniq_type_reference_id;
