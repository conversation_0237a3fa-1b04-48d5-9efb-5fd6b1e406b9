name: "incident 表新增 created by 字段"
description: "incident 表新增 created by 字段"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_customer"
            sql: |
              alter table pet_incident_report
              add created_by bigint not null default 0 after is_deleted;

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_customer"
            sql: |
              alter table pet_incident_report
              add created_by bigint not null default 0 after is_deleted;