name: "add customer entitlement"
description: "related slack: https://moegoworkspace.slack.com/archives/C01TT9K995M/p1738374068631079"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_membership"
            sql: |
              INSERT INTO "public"."membership_perk" 
              (
              "entitlement_id",
              "membership_id",
              "validity_start_time",
              "validity_end_time"
              )
              VALUES
              (
              48024,
              558,
              '2025-01-28 16:56:00.000000',
              '2025-02-28 16:56:00.000000'
              );

