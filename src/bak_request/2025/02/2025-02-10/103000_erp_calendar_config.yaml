name: "calendar view config 添加 show_staff_estimated_revenue 字段"
description: "calendar view config 添加 show_staff_estimated_revenue 字段"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_business"
            sql: |
              alter table moe_calendar
              add show_staff_estimated_revenue tinyint(1) default 0 not null comment '是否展示 staff estimated revenue';

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_business"
            sql: |
              alter table moe_calendar
              add show_staff_estimated_revenue tinyint(1) default 0 not null comment '是否展示 staff estimated revenue';