name: "update refund index"
description: "现有的 refund 的 module_invoice_id idx 无法作用于只用 invoice id 查询，因此调整顺序"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              create index idx_invoice_id_module on refund (invoice_id, module);

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              create index idx_invoice_id_module on refund (invoice_id, module);
