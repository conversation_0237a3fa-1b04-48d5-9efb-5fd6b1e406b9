name: "Add index"
description: "add index to staff_availability_day_hour"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_online_booking"
            sql: |
              create index idx_day_id_day_type on staff_availability_day_hour (day_id, day_type);
