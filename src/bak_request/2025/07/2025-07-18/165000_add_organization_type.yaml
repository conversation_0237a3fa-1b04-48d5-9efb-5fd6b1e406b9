name: "165000_add_organization_type"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_enterprise"
            sql: |
              DROP TRIGGER update_at ON template_push_mapping;
              ALTER table template_push_mapping add column target_organization_type text NOT NULL DEFAULT 'TYPE_UNSPECIFIED'::text;
              update template_push_mapping set target_organization_type = 'COMPANY';
              ALTER table template_push_mapping RENAME column target_company_id TO target_organization_id;
              CREATE TRIGGER update_at
                  BEFORE UPDATE
                  ON template_push_mapping
                  FOR EACH ROW
              EXECUTE PROCEDURE update_at();

              drop INDEX idx_template_type_template_id;

              CREATE UNIQUE INDEX "idx_template_type_template_id" ON "public"."template_push_mapping" USING btree (
                "template_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
                "template_id" "pg_catalog"."int8_ops" ASC NULLS LAST,
                "target_organization_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
                "target_organization_id" "pg_catalog"."int8_ops" ASC NULLS LAST
              );


  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_enterprise"
            sql: |
              DROP TRIGGER update_at ON template_push_mapping;
              ALTER table template_push_mapping add column target_organization_type text NOT NULL DEFAULT 'TYPE_UNSPECIFIED'::text;
              update template_push_mapping set target_organization_type = 'COMPANY';
              ALTER table template_push_mapping RENAME column target_company_id TO target_organization_id;
              CREATE TRIGGER update_at
                  BEFORE UPDATE
                  ON template_push_mapping
                  FOR EACH ROW
              EXECUTE PROCEDURE update_at();

              drop INDEX idx_template_type_template_id;

              CREATE UNIQUE INDEX "idx_template_type_template_id" ON "public"."template_push_mapping" USING btree (
                "template_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
                "template_id" "pg_catalog"."int8_ops" ASC NULLS LAST,
                "target_organization_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
                "target_organization_id" "pg_catalog"."int8_ops" ASC NULLS LAST
              );

