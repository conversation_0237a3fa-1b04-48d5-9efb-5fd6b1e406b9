name: "Revert delete items CS-29983"
description: "在 CS-29983 的 Thread 内处理了 421 笔订单的数据，但是部分 items 因为匹配的时候忘记去重导致有 28 个 items 被重复匹配，需要重新修复"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              UPDATE public."order_line_item" SET is_deleted = false, update_time = NOW() WHERE id = 204356386 AND is_deleted = true;
              UPDATE public."order_line_item" SET is_deleted = false, update_time = NOW() WHERE id = 168865613 AND is_deleted = true;
              UPDATE public."order_line_item" SET is_deleted = false, update_time = NOW() WHERE id = 168865617 AND is_deleted = true;
              UPDATE public."order_line_item" SET is_deleted = false, update_time = NOW() WHERE id = 168865629 AND is_deleted = true;
              UPDATE public."order_line_item" SET is_deleted = false, update_time = NOW() WHERE id = 194721001 AND is_deleted = true;
              UPDATE public."order_line_item" SET is_deleted = false, update_time = NOW() WHERE id = 168865637 AND is_deleted = true;
              UPDATE public."order_line_item" SET is_deleted = false, update_time = NOW() WHERE id = 168865645 AND is_deleted = true;
              UPDATE public."order_line_item" SET is_deleted = false, update_time = NOW() WHERE id = 168865647 AND is_deleted = true;
              UPDATE public."order_line_item" SET is_deleted = false, update_time = NOW() WHERE id = 168865651 AND is_deleted = true;
              UPDATE public."order_line_item" SET is_deleted = false, update_time = NOW() WHERE id = 168865655 AND is_deleted = true;
              UPDATE public."order_line_item" SET is_deleted = false, update_time = NOW() WHERE id = 172015930 AND is_deleted = true;
              UPDATE public."order_line_item" SET is_deleted = false, update_time = NOW() WHERE id = 176032343 AND is_deleted = true;
              UPDATE public."order_line_item" SET is_deleted = false, update_time = NOW() WHERE id = 181904156 AND is_deleted = true;
              UPDATE public."order_line_item" SET is_deleted = false, update_time = NOW() WHERE id = 182308627 AND is_deleted = true;
              UPDATE public."order_line_item" SET is_deleted = false, update_time = NOW() WHERE id = 184010677 AND is_deleted = true;
              UPDATE public."order_line_item" SET is_deleted = false, update_time = NOW() WHERE id = 186186673 AND is_deleted = true;
              UPDATE public."order_line_item" SET is_deleted = false, update_time = NOW() WHERE id = 187446706 AND is_deleted = true;
              UPDATE public."order_line_item" SET is_deleted = false, update_time = NOW() WHERE id = 188910054 AND is_deleted = true;
              UPDATE public."order_line_item" SET is_deleted = false, update_time = NOW() WHERE id = 190946247 AND is_deleted = true;
              UPDATE public."order_line_item" SET is_deleted = false, update_time = NOW() WHERE id = 191337438 AND is_deleted = true;
              UPDATE public."order_line_item" SET is_deleted = false, update_time = NOW() WHERE id = 192127849 AND is_deleted = true;
              UPDATE public."order_line_item" SET is_deleted = false, update_time = NOW() WHERE id = 194252268 AND is_deleted = true;
              UPDATE public."order_line_item" SET is_deleted = false, update_time = NOW() WHERE id = 194665428 AND is_deleted = true;
              UPDATE public."order_line_item" SET is_deleted = false, update_time = NOW() WHERE id = 196574634 AND is_deleted = true;
              UPDATE public."order_line_item" SET is_deleted = false, update_time = NOW() WHERE id = 198761827 AND is_deleted = true;
              UPDATE public."order_line_item" SET is_deleted = false, update_time = NOW() WHERE id = 206050678 AND is_deleted = true;
              UPDATE public."order_line_item" SET is_deleted = false, update_time = NOW() WHERE id = 206801131 AND is_deleted = true;
              UPDATE public."order_line_item" SET is_deleted = false, update_time = NOW() WHERE id = 206977522 AND is_deleted = true;
