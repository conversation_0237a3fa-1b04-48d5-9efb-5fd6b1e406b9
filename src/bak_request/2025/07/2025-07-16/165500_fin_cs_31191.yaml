name: "Fix Order data for ticket cs-31191"
description: "Fix Order table and payment && pay detail table data for ticket cs-31191"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              ---- 更新 status
              BEGIN;
              
              UPDATE public.order
              SET status = 2, payment_status = 'PAID', remain_amount = 0,paid_amount = total_amount, update_time = NOW()
              WHERE id = *********;
              
              INSERT INTO message_delivery
                (message_type, reference_id, payload, status, last_attempt_time)
                VALUES (
                  'ORDER_COMPLETED',
                  '*********',
                  '{"id":"*********","orderType":"ORIGIN","status":"2","sourceType":"APPOINTMENT","paymentStatus":"PAID","fulfillmentStatus":"COMPLETED","completeTime":"1750551215","sourceId":"79611549","paidAmount":209.00,"totalAmount":209.00,"companyId":"118943","businessId":"119235","orderVersion":0}',
                  'PENDING',
                  NULL
                );
              
              COMMIT;

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              ---- 更新 payment status 和 insert moe_pay_detail record
              BEGIN;

              UPDATE moe_payment.payment
              SET status = 3, update_time = unix_timestamp(now())
              where invoice_id = ********* and stripe_intent_id = 'pi_3RcbXQIZwcIFVLGr21n6ugjm';

              INSERT INTO moe_payment.moe_pay_detail
                (business_id, payment_id, order_id, stripe_intent_id, amount, gross_sales, discount, tax, tips, booking_fee, convenience_fee, is_deposit, company_id)
                VALUES(119235, 31922955, *********, 'pi_3RcbXQIZwcIFVLGr21n6ugjm', 209.00, 209.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0, 118943);
              
              COMMIT;