name: "bulk add bundle addon"
description: "bulk add bundle addon"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_offering"
            sql: |
              INSERT INTO public.service_bundle_sale_mapping (company_id, service_id, bundle_service_id, created_at)
                SELECT
                  122356 AS company_id,
                  service_id,
                  bundle_service_id,
                  NOW() AS created_at
                FROM
                  unnest(ARRAY[1610578,1611188,1611194,1611212,1611215,1611218,1611247,1611282,1611327,1611330,1611367,1611385,1611396,1611408,1611412,1611419,1611231,1611280,1611296,1611309,1611336,1611347,1611399,1611415
                  ,1611425,1611441,1611466,1633807,1611160,1611169,1611171,1611177,1611186,1611190,1611197,1611227,1611238,1611260,1611288,1611290,1611302,1611311,1611313,1611334,1611343,1611401,1611406,1611432,1611434,1611444
                  ,1611446,1611448,1611460,1611462,1611470,1611473,1611475,1611173,1611175,1611278,1611155,1611192,1611200,1611222,1611250,1611252,1611255,1611276,1611316,1611338,1611379,1611383,1611410,1611436,1611153,1611240
                  ,1611257,1611319,1611342,1611351,1611375,1611381,1611403,1611458,1611468,1610577,1610648,1611157,1611164,1611167,1611183,1611205,1611207,1611209,1611220,1611234,1611236,1611242,1611245,1611262,1611285,1611321
                  ,1611323,1611325,1611332,1611340,1611349,1611353,1611355,1611377,1611417,1611427,1611430,1611438]) AS service_id
                CROSS JOIN
                  unnest(ARRAY[1676835,1676834,1676833,1676832,1676831,1676830,1676829,1676828]) AS bundle_service_id;
