name: "disable operation for pet detail"
description: "disable operation for pet detail"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-grooming
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              update moe_grooming_pet_detail set enable_operation = 0 where id = 246535988 and grooming_id = 83522099;