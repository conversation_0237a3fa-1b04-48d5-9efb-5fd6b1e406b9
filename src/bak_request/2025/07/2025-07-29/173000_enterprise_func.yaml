name: "173000_enterprise_func"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_enterprise"
            sql: |
              CREATE OR REPLACE FUNCTION update_at() R<PERSON><PERSON><PERSON> trigger AS
              $$
              BEGIN
                IF current_setting('enterprise.skip_updated_at', true) = 'on' THEN
                  RETURN NEW;
                END IF;
                new.updated_at = STATEMENT_TIMESTAMP()::timestamp WITHOUT TIME ZONE;
                RETURN new;
              END;
              $$
                LANGUAGE plpgsql;

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_enterprise"
            sql: |
              CREATE OR REPLACE FUNCTION update_at() <PERSON><PERSON><PERSON><PERSON> trigger AS
              $$
              BEGIN
                IF current_setting('enterprise.skip_updated_at', true) = 'on' THEN
                  RETURN NEW;
                END IF;
                new.updated_at = STATEMENT_TIMESTAMP()::timestamp WITHOUT TIME ZONE;
                RETURN new;
              END;
              $$
                LANGUAGE plpgsql;

