name: "erp permission"
description: "erp permission"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_offering"
            username: "${db.user.admin}"
            sql: |
              GRANT CONNECT ON DATABASE moego_offering TO developer_zhangdong;
              GRANT USAGE ON SCHEMA public TO developer_zhangdong;
              GRANT SELECT, UPDATE, DELETE, INSERT ON ALL TABLES IN SCHEMA public TO developer_zhangdong;
              ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, UPDATE, DELETE, INSERT ON TABLES TO developer_zhangdong;
