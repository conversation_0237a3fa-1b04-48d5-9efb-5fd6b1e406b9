name: "fix tips split"
description: "fix tips split"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              BEGIN;

              UPDATE tips_split
                SET split_config = '{"splitMethod": "SPLIT_TIPS_METHOD_BY_EQUALLY", "staffConfigs": [{"amount": {"nanos": 700000000, "units": "14", "currencyCode": "USD"}, "staffId": "186251", "percentage": 100}], "businessTipAmount": {"nanos": 0, "units": "0", "currencyCode": "USD"}}', update_time = NOW()
                WHERE source_id = 77710537 AND id = 285960;
              UPDATE tips_split_detail
                SET split_amount = 14.7, update_time = NOW()
                WHERE tips_split_id = 285960 AND id = 323995;
              UPDATE tips_split
                SET split_config = '{"splitMethod": "SPLIT_TIPS_METHOD_BY_PERCENTAGE", "staffConfigs": [{"amount": {"nanos": 0, "units": "10", "currencyCode": "USD"}, "staffId": "186251", "percentage": 50}, {"amount": {"nanos": 0, "units": "10", "currencyCode": "USD"}, "staffId": "186254", "percentage": 50}], "businessTipAmount": {"nanos": 0, "units": "0", "currencyCode": "USD"}}', update_time = NOW()
                WHERE source_id = 63426986 AND id = 287822;
              UPDATE tips_split_detail
                SET split_amount = 10, update_time = NOW()
                WHERE tips_split_id = 287822 AND id = 325915;
              UPDATE tips_split_detail
                SET split_amount = 10, update_time = NOW()
                WHERE tips_split_id = 287822 AND id = 325916;

              COMMIT;
