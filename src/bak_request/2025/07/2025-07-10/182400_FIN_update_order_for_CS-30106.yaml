name: "update remaining amount for CS-30106"
description: "add tips, because tips was paid , integration square"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              UPDATE public."order"
                SET status = 2, payment_status='PAID', paid_amount= paid_amount + tips_amount , remain_amount = 0,update_time = NOW()
              WHERE id=146850986 and business_id=108580;