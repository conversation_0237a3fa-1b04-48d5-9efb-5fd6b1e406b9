name: "<PERSON>ert Cancel order"
description: "<PERSON>ert https://moegoworkspace.slack.com/archives/C094WSP49T9/p1752212389257629"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              UPDATE public.order SET status = 0, update_time = NOW() 
                WHERE id IN (149796559, 151324786)
                  AND paid_amount = 0 AND deposit_amount = 0 AND discount_amount = 0;
