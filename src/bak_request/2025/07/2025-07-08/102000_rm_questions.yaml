stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-grooming
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              update moe_grooming.moe_book_online_question
              set status = 0
              where id in (1291867, 1291865, 1291871, 1291869, 1291874, 1291873, 1291875)
              and status = 1;
