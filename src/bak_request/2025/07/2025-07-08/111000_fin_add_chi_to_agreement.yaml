name: "add chi to agreement"
description: "add chi to agreement"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_agreement"
            username: "${db.user.admin}"
            sql: |
              GRANT CONNECT ON DATABASE moego_agreement TO developer_chihuang;
              GRANT USAGE ON SCHEMA public TO developer_chihuang;
              GRANT SELECT, UPDATE, DELETE, INSERT ON ALL TABLES IN SCHEMA public TO developer_chihuang;
              ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, UPDATE, DELETE, INSERT ON TABLES TO developer_chihuang;
              GRANT USAGE, SELECT,UPDATE ON SEQUENCE subscription_id_seq TO developer_chihuang;
