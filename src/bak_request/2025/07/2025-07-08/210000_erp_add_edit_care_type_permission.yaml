name: "add edit care type name permission"
description: "add edit care type name permission"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_permission"
            sql: |
              INSERT INTO "public"."permission" 
              ("id", "name", "display_name", "description", "parent_permission_id", "category_id") 
              VALUES 
              (365, 'editCareTypeName', 'Edit care type name', 'Edit care type name', 59, 8);
              
              insert into "public"."role_permission_mapping" 
              (role_id, permission_id)
              select id, 365 from role where deleted_at is null and "type" = 'ROLE_TYPE_COMPANY_OWNER';
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_permission"
            sql: |
              INSERT INTO "public"."permission" 
              ("id", "name", "display_name", "description", "parent_permission_id", "category_id")
              VALUES
              (365, 'editCareTypeName', 'Edit care type name', 'Edit care type name', 59, 8);
              
              insert into "public"."role_permission_mapping" 
              (role_id, permission_id)
              select id, 365 from role where deleted_at is null and "type" = 'ROLE_TYPE_COMPANY_OWNER';
