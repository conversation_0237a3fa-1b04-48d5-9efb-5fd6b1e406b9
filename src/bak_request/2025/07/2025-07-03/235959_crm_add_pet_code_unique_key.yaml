name: "add pet code unique key"
description: "add pet code unique key"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_customer"
            sql: |
            
              DELETE FROM moe_customer.moe_pet_pet_code_binding 
              WHERE id IN (
                SELECT id FROM (
                  SELECT id,
                         ROW_NUMBER() OVER (PARTITION BY pet_id, pet_code_id ORDER BY id) as rn
                  FROM moe_customer.moe_pet_pet_code_binding
                ) t
                WHERE t.rn > 1
              );

              ALTER TABLE moe_customer.moe_pet_pet_code_binding
              ADD UNIQUE KEY uniq_pet_code_id_pet_id (pet_id, pet_code_id); 

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_customer"
            sql: |
              DELETE FROM moe_customer.moe_pet_pet_code_binding 
              WHERE id IN (
                SELECT id FROM (
                  SELECT id,
                         ROW_NUMBER() OVER (PARTITION BY pet_id, pet_code_id ORDER BY id) as rn
                  FROM moe_customer.moe_pet_pet_code_binding
                ) t
                WHERE t.rn > 1
              );

              ALTER TABLE moe_customer.moe_pet_pet_code_binding
              ADD UNIQUE KEY uniq_pet_code_id_pet_id (pet_id, pet_code_id); 