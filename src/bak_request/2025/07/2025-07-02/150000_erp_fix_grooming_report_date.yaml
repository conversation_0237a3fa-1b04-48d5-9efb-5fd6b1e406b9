name: "Incident-101: fix dirty data"
description: "将 grooming report 数据库中的脏数据修复"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-mysql-grooming
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              UPDATE moe_grooming_report
              SET content_json = JSON_SET(
                      content_json,
                      '$.petConditions',
                      (
                          SELECT JSON_ARRAYAGG(
                                        CASE
                                            WHEN JSON_EXTRACT(pc.value, '$.type') = 'single_choice'
                                                AND JSON_TYPE(JSON_EXTRACT(pc.value, '$.choices')) = 'ARRAY'
                                                AND JSON_LENGTH(JSON_EXTRACT(pc.value, '$.choices')) > 0
                                                THEN JSON_SET(
                                                    pc.value,
                                                    '$.choices',
                                                    JSON_ARRAY(JSON_EXTRACT(JSON_EXTRACT(pc.value, '$.choices'), '$[0]'))
                                                      )
                                            WHEN JSON_EXTRACT(pc.value, '$.type') IN ('multi_choice', 'tag_choice')
                                                AND JSON_TYPE(JSON_EXTRACT(pc.value, '$.choices')) = 'ARRAY'
                                                AND JSON_LENGTH(JSON_EXTRACT(pc.value, '$.choices')) > 0
                                                THEN JSON_SET(
                                                    pc.value,
                                                    '$.choices',
                                                    (
                                                        SELECT CAST(
                                                                        CONCAT('[', GROUP_CONCAT(DISTINCT CONCAT('"', REPLACE(choice_val, '"', '\\"'), '"')), ']')
                                                                    AS JSON
                                                                )
                                                        FROM JSON_TABLE(
                                                                      JSON_EXTRACT(pc.value, '$.choices'),
                                                                      '$[*]' COLUMNS (choice_val VARCHAR(1000) PATH '$')
                                                              ) AS t
                                                    )
                                                      )
                                            ELSE pc.value
                                            END
                                )
                          FROM JSON_TABLE(
                                      JSON_EXTRACT(content_json, '$.petConditions'),
                                      '$[*]' COLUMNS (value JSON PATH '$')
                              ) AS pc
                      )
                                )
              WHERE
                  JSON_EXTRACT(content_json, '$.petConditions') IS NOT NULL
                AND update_time > '2025-06-29 14:00:00';

              UPDATE moe_grooming_report
              SET content_json = JSON_SET(
                      content_json,
                      '$.feedbacks',
                      (
                          SELECT JSON_ARRAYAGG(
                                        CASE
                                            WHEN JSON_EXTRACT(pc.value, '$.type') = 'single_choice'
                                                AND JSON_TYPE(JSON_EXTRACT(pc.value, '$.choices')) = 'ARRAY'
                                                AND JSON_LENGTH(JSON_EXTRACT(pc.value, '$.choices')) > 0
                                                THEN JSON_SET(
                                                    pc.value,
                                                    '$.choices',
                                                    JSON_ARRAY(JSON_EXTRACT(JSON_EXTRACT(pc.value, '$.choices'), '$[0]'))
                                                      )
                                            WHEN JSON_EXTRACT(pc.value, '$.type') IN ('multi_choice', 'tag_choice')
                                                AND JSON_TYPE(JSON_EXTRACT(pc.value, '$.choices')) = 'ARRAY'
                                                AND JSON_LENGTH(JSON_EXTRACT(pc.value, '$.choices')) > 0
                                                THEN JSON_SET(
                                                    pc.value,
                                                    '$.choices',
                                                    (
                                                        SELECT CAST(
                                                                        CONCAT('[', GROUP_CONCAT(DISTINCT CONCAT('"', REPLACE(choice_val, '"', '\\"'), '"')), ']')
                                                                    AS JSON
                                                                )
                                                        FROM JSON_TABLE(
                                                                      JSON_EXTRACT(pc.value, '$.choices'),
                                                                      '$[*]' COLUMNS (choice_val VARCHAR(1000) PATH '$')
                                                              ) AS t
                                                    )
                                                      )
                                            ELSE pc.value
                                            END
                                )
                          FROM JSON_TABLE(
                                      JSON_EXTRACT(content_json, '$.feedbacks'),
                                      '$[*]' COLUMNS (value JSON PATH '$')
                              ) AS pc
                      )
                                )
              WHERE
                  JSON_EXTRACT(content_json, '$.feedbacks') IS NOT NULL
                AND update_time > '2025-06-29 14:00:00';
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-grooming
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              UPDATE moe_grooming_report
              SET content_json = JSON_SET(
                      content_json,
                      '$.petConditions',
                      (
                          SELECT JSON_ARRAYAGG(
                                        CASE
                                            WHEN JSON_EXTRACT(pc.value, '$.type') = 'single_choice'
                                                AND JSON_TYPE(JSON_EXTRACT(pc.value, '$.choices')) = 'ARRAY'
                                                AND JSON_LENGTH(JSON_EXTRACT(pc.value, '$.choices')) > 0
                                                THEN JSON_SET(
                                                    pc.value,
                                                    '$.choices',
                                                    JSON_ARRAY(JSON_EXTRACT(JSON_EXTRACT(pc.value, '$.choices'), '$[0]'))
                                                      )
                                            WHEN JSON_EXTRACT(pc.value, '$.type') IN ('multi_choice', 'tag_choice')
                                                AND JSON_TYPE(JSON_EXTRACT(pc.value, '$.choices')) = 'ARRAY'
                                                AND JSON_LENGTH(JSON_EXTRACT(pc.value, '$.choices')) > 0
                                                THEN JSON_SET(
                                                    pc.value,
                                                    '$.choices',
                                                    (
                                                        SELECT CAST(
                                                                        CONCAT('[', GROUP_CONCAT(DISTINCT CONCAT('"', REPLACE(choice_val, '"', '\\"'), '"')), ']')
                                                                    AS JSON
                                                                )
                                                        FROM JSON_TABLE(
                                                                      JSON_EXTRACT(pc.value, '$.choices'),
                                                                      '$[*]' COLUMNS (choice_val VARCHAR(1000) PATH '$')
                                                              ) AS t
                                                    )
                                                      )
                                            ELSE pc.value
                                            END
                                )
                          FROM JSON_TABLE(
                                      JSON_EXTRACT(content_json, '$.petConditions'),
                                      '$[*]' COLUMNS (value JSON PATH '$')
                              ) AS pc
                      )
                                )
              WHERE
                  JSON_EXTRACT(content_json, '$.petConditions') IS NOT NULL
                AND update_time > '2025-06-29 14:00:00';

              UPDATE moe_grooming_report
              SET content_json = JSON_SET(
                      content_json,
                      '$.feedbacks',
                      (
                          SELECT JSON_ARRAYAGG(
                                        CASE
                                            WHEN JSON_EXTRACT(pc.value, '$.type') = 'single_choice'
                                                AND JSON_TYPE(JSON_EXTRACT(pc.value, '$.choices')) = 'ARRAY'
                                                AND JSON_LENGTH(JSON_EXTRACT(pc.value, '$.choices')) > 0
                                                THEN JSON_SET(
                                                    pc.value,
                                                    '$.choices',
                                                    JSON_ARRAY(JSON_EXTRACT(JSON_EXTRACT(pc.value, '$.choices'), '$[0]'))
                                                      )
                                            WHEN JSON_EXTRACT(pc.value, '$.type') IN ('multi_choice', 'tag_choice')
                                                AND JSON_TYPE(JSON_EXTRACT(pc.value, '$.choices')) = 'ARRAY'
                                                AND JSON_LENGTH(JSON_EXTRACT(pc.value, '$.choices')) > 0
                                                THEN JSON_SET(
                                                    pc.value,
                                                    '$.choices',
                                                    (
                                                        SELECT CAST(
                                                                        CONCAT('[', GROUP_CONCAT(DISTINCT CONCAT('"', REPLACE(choice_val, '"', '\\"'), '"')), ']')
                                                                    AS JSON
                                                                )
                                                        FROM JSON_TABLE(
                                                                      JSON_EXTRACT(pc.value, '$.choices'),
                                                                      '$[*]' COLUMNS (choice_val VARCHAR(1000) PATH '$')
                                                              ) AS t
                                                    )
                                                      )
                                            ELSE pc.value
                                            END
                                )
                          FROM JSON_TABLE(
                                      JSON_EXTRACT(content_json, '$.feedbacks'),
                                      '$[*]' COLUMNS (value JSON PATH '$')
                              ) AS pc
                      )
                                )
              WHERE
                  JSON_EXTRACT(content_json, '$.feedbacks') IS NOT NULL
                AND update_time > '2025-06-29 14:00:00';