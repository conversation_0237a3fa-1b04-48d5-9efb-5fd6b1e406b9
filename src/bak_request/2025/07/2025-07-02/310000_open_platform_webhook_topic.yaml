name: "Open Platform Webhook topic"
description: "add webhook topic"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.MessageQueueApi/CreateTopics"
    body:
      identifier:
        platform: AWS_MSK
        identifier: "kafka-staging"
      topics:
        - name: "moego.platform.webhook_delivery.staging"
          partition_number: 8
  - api: "/moego.devops.api.console.v1.MessageQueueApi/CreateTopics"
    body:
      identifier:
        platform: AWS_MSK
        identifier: "moego-kafka"
      topics:
        - name: "moego.platform.webhook_delivery.production"
          partition_number: 16
