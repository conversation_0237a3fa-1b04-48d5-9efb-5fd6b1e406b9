name: "fix deposit partial paid"
description: "fix deposit partial paid"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              BEGIN;
              
              UPDATE order_line_item
                SET unit_price = 153.97, sub_total_amount = 153.97, total_amount = 153.97, update_time = NOW()
                WHERE order_id = ********* AND id = 199964382;
              UPDATE order_line_item
                SET unit_price = 158.50, sub_total_amount = 158.50, total_amount = 158.50, update_time = NOW()
                WHERE order_id = ********* AND id = 202749188;

              UPDATE deposit_change_log
                SET changed_amount = 153.97, balance = 153.97, update_time = NOW()
                WHERE deposit_order_id = ********* AND id = 3999;
              UPDATE deposit_change_log
                SET changed_amount = 158.50, balance = 158.50, update_time = NOW()
                WHERE deposit_order_id = ********* AND id = 5306;

              UPDATE "order"
                SET status = 2, payment_status = 'PAID',
                sub_total_amount = 153.97, total_amount = 153.97, remain_amount = 0.0,
                update_time = NOW(), complete_time = to_timestamp(1751451300)
                WHERE id = *********;
              UPDATE "order"
                SET status = 2, payment_status = 'PAID',
                sub_total_amount = 158.50, total_amount = 158.50, remain_amount = 0.0,
                update_time = NOW(), complete_time = to_timestamp(1751451300)
                WHERE id = *********;

              INSERT INTO message_delivery
                (message_type, reference_id, payload, status, last_attempt_time)
                VALUES (
                  'ORDER_COMPLETED',
                  '*********',
                  '{"id":"*********","orderType":"DEPOSIT","status":"2","sourceType":"APPOINTMENT","paymentStatus":"PAID","fulfillmentStatus":"FULFILLMENT_STATUS_UNSPECIFIED","completeTime":"1751451300","sourceId":"79932530","paidAmount":153.97,"totalAmount":153.97,"companyId":"122283","businessId":"122772","orderVersion":4}',
                  'PENDING',
                  NULL
                );
              INSERT INTO message_delivery
                (message_type, reference_id, payload, status, last_attempt_time)
                VALUES (
                  'ORDER_COMPLETED',
                  '*********',
                  '{"id":"*********","orderType":"DEPOSIT","status":"2","sourceType":"APPOINTMENT","paymentStatus":"PAID","fulfillmentStatus":"FULFILLMENT_STATUS_UNSPECIFIED","completeTime":"1751451300","sourceId":"80846064","paidAmount":158.50,"totalAmount":158.50,"companyId":"122283","businessId":"122772","orderVersion":4}',
                  'PENDING',
                  NULL
                );
              
              COMMIT;