name: "add permission for open_platform"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_open_platform"
            username: "${db.user.admin}"
            sql: |
              GRANT USAGE ON SCHEMA public TO developer_harvie;
              GRANT SELECT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO developer_harvie;