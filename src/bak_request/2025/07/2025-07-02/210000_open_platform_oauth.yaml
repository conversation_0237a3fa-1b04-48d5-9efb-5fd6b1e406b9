name: "Open Platform Webhook"
description: "add webhook"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_open_platform"
            sql: |
              CREATE TABLE oauth_tokens
              (
              id          BIGSERIAL PRIMARY KEY,
              company_id  BIGINT                              NOT NULL,
              business_id BIGINT                              NOT NULL,
              type        TEXT                                NOT NULL,
              token       TEXT                                NOT NULL,
              created_at  TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
              updated_at  TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
              deleted_at  TIMESTAMP                           NULL,
              created_by  BIGINT                              NOT NULL,
              updated_by  BIGINT                              NOT NULL,
              deleted_by  BIGINT                              NULL
              );
              CREATE INDEX idx_ot_company_id ON oauth_tokens (company_id);
              CREATE TABLE google_ads_setting
              (
                  id               BIGSERIAL PRIMARY KEY,
                  company_id       BIGINT                              NOT NULL,
                  business_id      BIGINT                              NOT NULL,
                  ads_customer_ids BIGINT[]                              NOT NULL,
                  created_at       TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
                  updated_at       TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
                  deleted_at       TIMESTAMP                           NULL,
                  created_by       BIGINT                              NOT NULL,
                  updated_by       BIGINT                              NOT NULL,
                  deleted_by       BIGINT                              NULL
              );
              CREATE INDEX idx_gas_company_id ON google_ads_setting (company_id);
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_open_platform"
            sql: |
              CREATE TABLE oauth_tokens
              (
              id          BIGSERIAL PRIMARY KEY,
              company_id  BIGINT                              NOT NULL,
              business_id BIGINT                              NOT NULL,
              type        TEXT                                NOT NULL,
              token       TEXT                                NOT NULL,
              created_at  TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
              updated_at  TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
              deleted_at  TIMESTAMP                           NULL,
              created_by  BIGINT                              NOT NULL,
              updated_by  BIGINT                              NOT NULL,
              deleted_by  BIGINT                              NULL
              );
              CREATE INDEX idx_ot_company_id ON oauth_tokens (company_id);
              CREATE TABLE google_ads_setting
              (
                  id               BIGSERIAL PRIMARY KEY,
                  company_id       BIGINT                              NOT NULL,
                  business_id      BIGINT                              NOT NULL,
                  ads_customer_ids BIGINT[]                              NOT NULL,
                  created_at       TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
                  updated_at       TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
                  deleted_at       TIMESTAMP                           NULL,
                  created_by       BIGINT                              NOT NULL,
                  updated_by       BIGINT                              NOT NULL,
                  deleted_by       BIGINT                              NULL
              );
              CREATE INDEX idx_gas_company_id ON google_ads_setting (company_id);