name: "pg moego_auto_message 库给better加读权限"
description: "pg moego_auto_message 库给better加读权限"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_auto_message"
            username: "${db.user.admin}"
            sql: |
              GRANT CONNECT ON DATABASE moego_auto_message TO developer_better;
              GRANT USAGE ON SCHEMA public TO developer_better;
              GRANT SELECT, UPDATE, DELETE, INSERT ON ALL TABLES IN SCHEMA public TO developer_better;
              ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, UPDATE, DELETE, INSERT ON TABLES TO developer_better;
