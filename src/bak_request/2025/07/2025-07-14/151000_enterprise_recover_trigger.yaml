name: "151000_enterprise_recover_trigger"
description: "151000_enterprise_recover_trigger"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_enterprise"
            sql: |
              CREATE TRIGGER update_at
              BEFORE UPDATE ON template_push_change_history
              FOR EACH ROW
              EXECUTE FUNCTION update_at();

