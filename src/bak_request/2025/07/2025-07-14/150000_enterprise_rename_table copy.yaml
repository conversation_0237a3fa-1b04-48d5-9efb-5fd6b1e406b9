name: "150000_enterprise_rename_table"
description: "150000_enterprise_rename_table"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_enterprise"
            sql: |
              ALTER TABLE "public"."service_change" RENAME COLUMN "service_id" TO "template_id";

              ALTER TABLE "public"."service_change" RENAME COLUMN "service_name" TO "template_name";

              ALTER TABLE "public"."service_change" RENAME COLUMN "service_before" TO "template_before";

              ALTER TABLE "public"."service_change" RENAME COLUMN "service_after" TO "template_after";

              ALTER TABLE "public"."service_change" 
                ADD COLUMN "template_type" text NOT NULL DEFAULT 'TYPE_UNSPECIFIED'::text;

              update "public"."service_change" 
                set template_type = 'SERVICE'::text
                where template_type = 'TYPE_UNSPECIFIED'::text;

              ALTER TABLE "public"."service_change" RENAME TO "template_push_change";

              ALTER TABLE "public"."service_change_history" RENAME COLUMN "service_id" TO "template_id";

              ALTER TABLE "public"."service_change_history" 
                ADD COLUMN "template_type" text NOT NULL DEFAULT 'TYPE_UNSPECIFIED'::text;

              update "public"."service_change_history" 
                set template_type = 'SERVICE'::text
                where template_type = 'TYPE_UNSPECIFIED'::text;

              ALTER TABLE "public"."service_change_history" RENAME TO "template_push_change_history";
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_enterprise"
            sql: |
              ALTER TABLE "public"."service_change" RENAME COLUMN "service_id" TO "template_id";

              ALTER TABLE "public"."service_change" RENAME COLUMN "service_name" TO "template_name";

              ALTER TABLE "public"."service_change" RENAME COLUMN "service_before" TO "template_before";

              ALTER TABLE "public"."service_change" RENAME COLUMN "service_after" TO "template_after";

              ALTER TABLE "public"."service_change" 
                ADD COLUMN "template_type" text NOT NULL DEFAULT 'TYPE_UNSPECIFIED'::text;

              update "public"."service_change" 
                set template_type = 'SERVICE'::text
                where template_type = 'TYPE_UNSPECIFIED'::text;

              ALTER TABLE "public"."service_change" RENAME TO "template_push_change";

              ALTER TABLE "public"."service_change_history" RENAME COLUMN "service_id" TO "template_id";

              ALTER TABLE "public"."service_change_history" 
                ADD COLUMN "template_type" text NOT NULL DEFAULT 'TYPE_UNSPECIFIED'::text;

              update "public"."service_change_history" 
                set template_type = 'SERVICE'::text
                where template_type = 'TYPE_UNSPECIFIED'::text;

              ALTER TABLE "public"."service_change_history" RENAME TO "template_push_change_history";

