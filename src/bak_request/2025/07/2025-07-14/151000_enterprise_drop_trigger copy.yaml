name: "151000_enterprise_drop_trigger"
description: "151000_enterprise_drop_trigger"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_enterprise"
            sql: |
              DROP TRIGGER update_at ON template_push_change_history;

