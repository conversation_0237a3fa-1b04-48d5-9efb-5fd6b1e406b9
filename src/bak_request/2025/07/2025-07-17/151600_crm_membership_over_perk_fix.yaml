name: "151600_crm_membership_over_perk_fix"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_membership"
            sql: |
              -- 为 membership_perk 表添加新字段
              ALTER TABLE membership_perk
                  ADD COLUMN subscription_id BIGINT       NOT NULL DEFAULT 0,
                  ADD COLUMN idempotency_key VARCHAR(255) NOT NULL DEFAULT '';

              -- 创建部分唯一索引，确保在 deleted_at 为 NULL 时，idempotency_key 和 membership_id 的组合是唯一的
              CREATE UNIQUE INDEX idx_membership_entitlement_perk_idempotency_unique
                  ON membership_perk (idempotency_key, membership_id, entitlement_id)
                  WHERE deleted_at IS NULL AND idempotency_key != '';

              -- 为 subscription_id 创建普通索引，提高查询性能
              CREATE INDEX idx_membership_perk_subscription_id
                  ON membership_perk (subscription_id);

              -- 为 idempotency_key 创建普通索引，提高查询性能
              CREATE INDEX idx_membership_perk_idempotency_key
                  ON membership_perk (idempotency_key);

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_membership"
            sql: |
              -- 为 membership_perk 表添加新字段
              ALTER TABLE membership_perk
                  ADD COLUMN subscription_id BIGINT       NOT NULL DEFAULT 0,
                  ADD COLUMN idempotency_key VARCHAR(255) NOT NULL DEFAULT '';

              -- 创建部分唯一索引，确保在 deleted_at 为 NULL 时，idempotency_key 和 membership_id 的组合是唯一的
              CREATE UNIQUE INDEX idx_membership_entitlement_perk_idempotency_unique
                  ON membership_perk (idempotency_key, membership_id, entitlement_id)
                  WHERE deleted_at IS NULL AND idempotency_key != '';

              -- 为 subscription_id 创建普通索引，提高查询性能
              CREATE INDEX idx_membership_perk_subscription_id
                  ON membership_perk (subscription_id);

              -- 为 idempotency_key 创建普通索引，提高查询性能
              CREATE INDEX idx_membership_perk_idempotency_key
                  ON membership_perk (idempotency_key);

