stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_customer"
            sql: |
              update moe_customer.moe_customer_profile_request
              set is_merged = 1
              where company_id = 123241
                and customer_id in (20938383, 20938390, 20938396, 20938415);

              update moe_customer.moe_customer_profile_request
              set customer_id = 20940218
              where company_id = 123241
                and customer_id in (20938421);

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-cluster
      actions:
        - execute_sql:
            database: "moe_customer"
            sql: |
              update moe_customer.moe_customer_profile_request
              set is_merged = 1
              where company_id = 123241
                and customer_id in (20938383, 20938390, 20938396, 20938415);

              update moe_customer.moe_customer_profile_request
              set customer_id = 20940218
              where company_id = 123241
                and customer_id in (20938421);
