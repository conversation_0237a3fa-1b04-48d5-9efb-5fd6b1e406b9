name: "172000_add_pet_id_ob_request_setting"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_membership"
            sql: |
              alter table "public"."ob_request_setting" add COLUMN "pet_id" int8 NOT NULL DEFAULT 0;
              COMMENT ON COLUMN "public"."ob_request_setting"."pet_id" IS 'ID of the pet associated with the request setting, 0 means all pets';

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_membership"
            sql: |
              alter table "public"."ob_request_setting" add COLUMN "pet_id" int8 NOT NULL DEFAULT 0;
              COMMENT ON COLUMN "public"."ob_request_setting"."pet_id" IS 'ID of the pet associated with the request setting, 0 means all pets';

