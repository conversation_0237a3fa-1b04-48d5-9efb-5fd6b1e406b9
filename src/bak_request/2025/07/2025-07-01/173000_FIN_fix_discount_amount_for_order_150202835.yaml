name: "fix discount amount for order 150202835"
description: "fix discount amount for order 150202835"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              BEGIN;
              
              UPDATE order_line_discount
              SET discount_amount = 56.1, update_time = NOW()
              WHERE order_id = 150202835
              AND id = 104304286;
              
              UPDATE order_line_item
              SET discount_amount = 240.58, total_amount = 419.42, update_time = NOW()
              WHERE order_id = 150202835
              AND id = 202035502;
              
              UPDATE "order"
              SET update_time = NOW(), discount_amount = 275.06, total_amount = 145.94
              WHERE id = 150202835;
              
              COMMIT;