name: "Fix deposit flag of payment and order_payment"
description: "修复 Payment & OrderPayment 上 is_deposit 标记错误的问题"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              UPDATE "order_payment" SET update_time = NOW(), is_deposit = 'true'
                  WHERE id = 100421306 AND is_deposit = 'false';

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              UPDATE "payment" SET update_time = unix_timestamp(), is_deposit = 'true'
              WHERE id = 32017312 AND order_payment_id = 100421306 AND is_deposit = 'false';
