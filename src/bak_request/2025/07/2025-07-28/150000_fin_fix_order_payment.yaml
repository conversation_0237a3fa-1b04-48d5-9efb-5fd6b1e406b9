name: "Fix order.paymentStatus"
description: "修补历史数据， 0 元订单在 Completed 的时候没有把 PaymentStatus 更新为 Paid。这里仅针对该商家 2025 年 Completed 的数据进行处理。数据经过 **人工** 确认是可以安全处理的."
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              UPDATE "order"
              SET update_time    = NOW(),
                  payment_status = 'PAID'
              WHERE business_id = 106312
                AND remain_amount = 0
                AND total_amount = 0
                AND id IN
                  (113655812, 126421154, 127138312, 127138393, 127700929, 127701150, 127701539, 128192166, 128192274, 128496947,
                  128497101, 128497397, 128497561, 128497606, 128497685, 128498325, 128498499, 128499380, 128499381, 129688687,
                  131081239, 131081241, 131894282, 131952603, 132980479, 132980480, 133790550, 133790734, 133790985, 133791012,
                  133791171, 133791347, 133791552, 133791600, 133791644, 133791687, 133791796, 133791797, 133792499, 133792500,
                  133792676, 133792809, 133793201, 133793377, 133795810, 133795985, 133796110, 133796172, 133796268, 133796269,
                  133796380, 133796409, 133796498, 133796709, 133796965, 133797008, 133797034, 133797054, 133797079, 133797106,
                  133797417, 133797598, 133797636, 133798029, 133798367, 133799263, 133799299, 133799321, 133799341, 133799342,
                  133799394, 133799395, 133799668, 133799853, 133799877, 133925945, 133925979, 133926026, 133926027, 133926069,
                  133926162, 133926187, 133926188, 133926251, 133926274, 134116506, 134116533, 134116565, 134353650, 134353651,
                  134353652, 134353703, 134353704, 134539420, 134539433, 134539653, 134539654, 134539671, 134539691, 134539692,
                  134540885, 134540887, 134540888, 134540889, 134541072, 134550526, 134550528, 134550529, 134550530, 134585452,
                  134607143, 134607203, 134607204, 134607205, 134607206, 134607207, 134607906, 134607907, 134607908, 134607909,
                  134607910, 134607911, 134608142, 134608144, 134608147, 134608285, 134608286, 134608288, 134608381, 134608382,
                  134608385, 134608386, 134608387, 134608669, 134608767, 134608773, 134671032, 134708182, 134708183, 134708184,
                  134708185, 134708186, 134708187, 134709928, 134709993, 134709994, 134709995, 134709996, 134711011, 134734661,
                  134741164, 134741170, 134741174, 134741178, 134741183, 134742370, 134744892, 134744896, 134744899, 134744906,
                  134744909, 134744911, 134746691, 134747470, 134747494, 134803630, 134803631, 134803632, 134803633, 134803634,
                  134806101, 134806125, 134806319, 134806321, 134806322, 134806374, 134806388, 134806686, 134806688, 134806689,
                  134806690, 134809759, 134809760, 134809761, 134809762, 134809763, 134809813, 134809816, 134809818, 134809819,
                  134809820, 134936425, 134936485, 134936654, 134936797, 134937740, 134939425, 134939520, 134939533, 134939537,
                  134939674, 134939729, 134939743, 135076469, 135092976, 135098853, 135099510, 135100647, 135100657, 135100662,
                  135100823, 135100832, 135100833, 135100834, 135100835, 135101097, 135101098, 135101099, 135101100, 135101101,
                  135101163, 135101165, 135101318, 135101438, 135101441, 135101442, 135101443, 135101444, 135102436, 135103055,
                  135103230, 135103232, 135103233, 135103234, 135118903, 135143639, 135143744, 135143745, 135143746, 135143747,
                  135143748, 135148264, 135148308, 135154746, 135154747, 135154748, 135154749, 135154750, 135156384, 135194372,
                  135195579, 135195681, 135195682, 135195684, 135196351, 135196408, 135196449, 135196451, 135202509, 135212700,
                  135212716, 135212718, 135212719, 135278710, 135289147, 135289274, 135289692, 135289693, 135289694, 135289801,
                  135294048, 135349322, 135437173, 135437653, 135437690, 135437691, 135437692, 135437693, 135451985, 135452746,
                  135452755, 135542573, 135546971, 135548160, 135548161, 135548162, 135586298, 135604018, 135659025, 135697928,
                  135698302, 135705018, 135710816, 135710817, 135710818, 135710819, 135757120, 135798271, 135798299, 135798491,
                  135798650, 135798762, 135799084, 135809170, 135857952, 135858000, 135858041, 135858095, 135858096, 135858098,
                  135867732, 135867745, 135876050, 135918381, 135918427, 135930685, 135932282, 135983170, 136022497, 136022614,
                  136022715, 136139051, 136158273, 136159776, 136161313, 136162732, 136166877, 136166977, 136166994, 136167113,
                  136170126, 136402520, 136403156, 136403192, 136403232, 136403253, 136403287, 136403343, 136414433, 136414927,
                  136415479, 136417098, 136417101, 136417108, 136417417, 136417421, 136417843, 136460766, 136469456, 136469520,
                  136473405, 136477490, 136537416, 136670735, 136752505, 136785179, 136799299, 136807028, 136807032, 136807034,
                  136807039, 136807044, 136918356, 136918372, 136918383, 136918390, 136918415, 136959331, 137065555, 137065569,
                  137065577, 137065621, 137065632, 137072309, 137072607, 137075432, 137111983, 137113768, 137117525, 137117541,
                  137117569, 137165927, 137170349, 137170422, 137178119, 137179624, 137467836, 137471384, 137471390, 137522517,
                  137522520, 137522524, 137565267, 137584413, 137584429, 137584507);
