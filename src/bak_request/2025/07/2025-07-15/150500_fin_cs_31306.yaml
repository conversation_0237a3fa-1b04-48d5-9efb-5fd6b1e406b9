name: "Fix Order data for ticket cs-31306"
description: "Fix Order table and payment && pay detail table data for ticket cs-31306"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              ---- 更新 status
              UPDATE public.order
              SET payment_status = 'PAID', remain_amount = 0,paid_amount = total_amount, update_time = NOW()
              where id = *********;
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              ---- 更新 payment status 和 insert moe_pay_detail record
              BEGIN;

              UPDATE moe_payment.payment SET status = 3, update_time = unix_timestamp(now()) WHERE invoice_id = ********* AND status = 2;

              INSERT INTO moe_payment.moe_pay_detail
                (business_id, payment_id, order_id, stripe_intent_id, amount, gross_sales, discount, tax, tips, booking_fee, convenience_fee, is_deposit, company_id)
                VALUES(112159, 32883383, *********, 'pi_3RiwfRIZwcIFVLGr2qPPAkBm', 135.00, 135.00, 33.75, 0.00, 0.00, 0.00, 0.00, 0, 111797);

              COMMIT;