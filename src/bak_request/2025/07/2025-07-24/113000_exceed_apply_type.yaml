name: "Update apply type for exceed pricing rule"
description: "Update apply type for exceed pricing rule"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              update service_charge set apply_type = 1 where id in (7497, 7596,7600,7552);
