name: "Fix wrong order line discount"
description: "Order line discount 计算优惠时用了原价而不是按顺序的折后价，导致付款之后 Total Amount 变少造成 Overpaid 假象。另外还有一个超付了 1 分钱，从某个 Item 上抠出来"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              BEGIN;
              UPDATE public."order_line_item"     SET update_time = NOW(), discount_amount = 155.1, total_amount = 504.9  WHERE id = 202035399 AND discount_amount = 165.0  AND total_amount = 495.0;
              UPDATE public."order_line_discount" SET update_time = NOW(), discount_amount = 56.1                         WHERE id = 104304276 AND discount_amount = 66.00;
              UPDATE public."order"               SET update_time = NOW(), discount_amount = 168.6, total_amount = 451.15 WHERE id = 150202809 AND discount_amount = 178.50 AND total_amount = 441.25;
              COMMIT;

              BEGIN;
              UPDATE public."order_line_item"     SET update_time = NOW(), discount_amount = 155.1, total_amount = 504.9  WHERE id = 202035637 AND discount_amount = 165.0  AND total_amount = 495.0;
              UPDATE public."order_line_discount" SET update_time = NOW(), discount_amount = 89.1                         WHERE id = 104304299 AND discount_amount = 99.00;
              UPDATE public."order"               SET update_time = NOW(), discount_amount = 161.85, total_amount = 241.65 WHERE id = 150202863 AND discount_amount = 171.75 AND total_amount = 231.75;
              COMMIT;

              BEGIN;
              UPDATE public."order_line_item"     SET update_time = NOW(), discount_amount = 155.1, total_amount = 504.9  WHERE id = 202035689 AND discount_amount = 165.0  AND total_amount = 495.0;
              UPDATE public."order_line_discount" SET update_time = NOW(), discount_amount = 56.1                         WHERE id = 104304313 AND discount_amount = 66.00;
              UPDATE public."order"               SET update_time = NOW(), discount_amount = 161.85, total_amount = 241.65 WHERE id = 150202884 AND discount_amount = 171.75 AND total_amount = 231.75;
              COMMIT;

              -- 超付 1 分钱
              BEGIN;
              UPDATE public."order_promotion_item" SET update_time = NOW(), applied_amount  = 200.47                       WHERE id = 2908      AND applied_amount = 200.48;
              UPDATE public."order_line_item"      SET update_time = NOW(), discount_amount = 348.97, total_amount = 1136.03  WHERE id = 209824584 AND discount_amount = 348.98  AND total_amount = 1136.02;
              UPDATE public."order_line_discount"  SET update_time = NOW(), discount_amount = 200.47                         WHERE id = 104857138 AND discount_amount = 200.48;
              UPDATE public."order"                SET update_time = NOW(), discount_amount = 360.97, total_amount = 622.43 WHERE id = 152848063 AND discount_amount = 360.98 AND total_amount = 622.42;
              COMMIT;
