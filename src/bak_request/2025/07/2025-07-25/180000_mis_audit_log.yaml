name: "Fix order payment"
description: "修复 Apple Pay 先失败后成功导致 Order Payment 结果同步异常的问题"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: staging-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_admin_v3"
            sql: |
              CREATE TABLE audit_log
              (
              id            VARCHAR(255) PRIMARY KEY,
              created_at    TIMESTAMPTZ  NOT NULL DEFAULT CURRENT_TIMESTAMP,
              request_id    VARCHAR(255),
              operator      VARCHAR(255) NOT NULL,
              action_type   VARCHAR(255) NOT NULL,
              resource_type VARCHAR(255),
              resource_id   VARCHAR(255),
              details       JSONB
              );
              
              COMMENT ON TABLE audit_log IS 'Stores records of sensitive actions performed for auditing purposes.';
              COMMENT ON COLUMN audit_log.id IS 'Client-provided unique identifier for the audit log entry (e.g., a UUID).';
              COMMENT ON COLUMN audit_log.created_at IS 'The timestamp when the event occurred (with timezone).';
              COMMENT ON COLUMN audit_log.request_id IS 'A unique identifier for tracing a request across multiple services.';
              COMMENT ON COLUMN audit_log.operator IS 'The email of the user or system that performed the action.';
              COMMENT ON COLUMN audit_log.action_type IS 'A string representing the type of action (e.g., ''USER_EMAIL_CHANGED'').';
              COMMENT ON COLUMN audit_log.resource_type IS 'The type of the resource being acted upon (e.g., ''USER'', ''BUSINESS'').';
              COMMENT ON COLUMN audit_log.resource_id IS 'The unique identifier of the resource object.';
              COMMENT ON COLUMN audit_log.details IS 'A JSONB object containing action-specific data (e.g., old/new values).';
              
              CREATE INDEX idx_audit_log_created_at ON audit_log (created_at DESC);
              CREATE INDEX idx_audit_log_request_id ON audit_log (request_id);
              CREATE INDEX idx_audit_log_operator ON audit_log (operator);
              CREATE INDEX idx_audit_log_action_type ON audit_log (action_type);
              CREATE INDEX idx_audit_log_resource ON audit_log (resource_type, resource_id);
              
              CREATE INDEX idx_audit_log_details_gin ON audit_log USING GIN (details);

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_admin_v3"
            sql: |
              CREATE TABLE audit_log
              (
              id            VARCHAR(255) PRIMARY KEY,
              created_at    TIMESTAMPTZ  NOT NULL DEFAULT CURRENT_TIMESTAMP,
              request_id    VARCHAR(255),
              operator      VARCHAR(255) NOT NULL,
              action_type   VARCHAR(255) NOT NULL,
              resource_type VARCHAR(255),
              resource_id   VARCHAR(255),
              details       JSONB
              );
              
              COMMENT ON TABLE audit_log IS 'Stores records of sensitive actions performed for auditing purposes.';
              COMMENT ON COLUMN audit_log.id IS 'Client-provided unique identifier for the audit log entry (e.g., a UUID).';
              COMMENT ON COLUMN audit_log.created_at IS 'The timestamp when the event occurred (with timezone).';
              COMMENT ON COLUMN audit_log.request_id IS 'A unique identifier for tracing a request across multiple services.';
              COMMENT ON COLUMN audit_log.operator IS 'The email of the user or system that performed the action.';
              COMMENT ON COLUMN audit_log.action_type IS 'A string representing the type of action (e.g., ''USER_EMAIL_CHANGED'').';
              COMMENT ON COLUMN audit_log.resource_type IS 'The type of the resource being acted upon (e.g., ''USER'', ''BUSINESS'').';
              COMMENT ON COLUMN audit_log.resource_id IS 'The unique identifier of the resource object.';
              COMMENT ON COLUMN audit_log.details IS 'A JSONB object containing action-specific data (e.g., old/new values).';
              
              CREATE INDEX idx_audit_log_created_at ON audit_log (created_at DESC);
              CREATE INDEX idx_audit_log_request_id ON audit_log (request_id);
              CREATE INDEX idx_audit_log_operator ON audit_log (operator);
              CREATE INDEX idx_audit_log_action_type ON audit_log (action_type);
              CREATE INDEX idx_audit_log_resource ON audit_log (resource_type, resource_id);
              
              CREATE INDEX idx_audit_log_details_gin ON audit_log USING GIN (details);
