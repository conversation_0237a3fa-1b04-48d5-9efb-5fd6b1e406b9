name: "Fix order payment"
description: "修复 Apple Pay 先失败后成功导致 Order Payment 结果同步异常的问题"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              BEGIN;
              UPDATE "order_payment" SET update_time = NOW(), fail_time = '1970-01-01 00:00:00.000000', payment_status = 'ORDER_PAYMENT_STATUS_PAID',    reason = '' WHERE id = 100385174 AND payment_status = 'ORDER_PAYMENT_STATUS_FAILED';
              UPDATE "order_payment" SET update_time = NOW(), cancel_time = NOW(),                      payment_status = 'ORDER_PAYMENT_STATUS_CANCELED'             WHERE id = 100385173 AND payment_status = 'ORDER_PAYMENT_STATUS_TRANSACTION_CREATED';
              COMMIT;
