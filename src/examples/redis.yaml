stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.CacheApi/ExecuteCacheCommand"
    body:
      identifier:
        platform: AWS_ELASTIC_CACHE
        identifier: "moego-redis-prod"
      redis_script:
        keys: ["devops-console:test:key"]
        argv: ["DevOps Console", "10"]
        script: |
          local n = redis.call('EXISTS', KEYS[1])
          if n == 0 then
            redis.call('SETEX', KEYS[1], ARGV[2], ARGV[1])
            return "OK"
          end
          
          return "key: " .. KEYS[1] .. " already exists"
