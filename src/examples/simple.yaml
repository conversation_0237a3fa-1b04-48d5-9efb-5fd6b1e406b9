stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.EnvironmentApi/ListEnvironments"
    body:
      statuses:
        - AVAILABLE
  - api: "/moego.devops.api.console.v1.MessageQueueApi/ListTopics"
    body:
      identifier:
        platform: SELF_HOSTED
        identifier: "development/kafka/kafka-controller"
      internal: false
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "postgres"
            username: "${db.user.admin}"
            sql: |
              DROP DATABASE IF EXISTS test_replica_lag;
        - execute_sql:
            database: "moego_todo_web"
            sql: |
              DROP TABLE IF EXISTS t_debug;
              
              CREATE TABLE t_debug(
                id int PRIMARY KEY,
                time timestamp
              );
              
              INSERT INTO t_debug(id, time)
              VALUES (1, NOW()),
                     (2, NOW());
              
              SELECT COUNT(*) FROM t_debug;
              SELECT * FROM t_debug ORDER BY id DESC;
              
              DROP TABLE IF EXISTS t_debug;
