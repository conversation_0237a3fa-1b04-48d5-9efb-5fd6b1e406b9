name: "<PERSON><PERSON><PERSON> Console Request Example"
description: "just test"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: SELF_HOSTED
        identifier: "debug-postgres"
      actions:
        - execute_sql:
            database: "postgres"
            username: "${db.user.admin}"
            sql: |
              DROP DATABASE IF EXISTS db_debug;
              DROP USER IF EXISTS u_debug;
        - create_database:
            database: "db_debug"
            username: "u_debug"
            create_user: true
        - execute_sql:
            database: "db_debug"
            username: "u_debug"
            sql: |
              CREATE TABLE t_debug(
                id int PRIMARY KEY,
                time timestamp
              );
              
              INSERT INTO t_debug(id, time)
              VALUES (1, NOW()),
                     (2, NOW());
              
              SELECT COUNT(*) FROM t_debug;
              SELECT * FROM t_debug ORDER BY id DESC;
        - execute_sql:
            database: "postgres"
            username: "${db.user.admin}"
            sql: |
              ALTER DATABASE db_debug OWNER TO postgres;
              DROP DATABASE IF EXISTS db_debug;
              DROP USER IF EXISTS u_debug;
  - api: "/moego.devops.api.console.v1.MessageQueueApi/ListTopics"
    body:
      identifier:
        platform: AWS_MSK
        identifier: "kafka-staging"
      internal: false
  - api: "/moego.devops.api.console.v1.MessageQueueApi/CreateTopics"
    body:
      identifier:
        platform: AWS_MSK
        identifier: "kafka-staging"
      topics:
        - name: "test-sample1"
          partition_number: 3
          replication_factor: 2
        - name: "test-sample2"
          partition_number: 3
          replication_factor: 2
  - api: "/moego.devops.api.console.v1.MessageQueueApi/ListTopics"
    body:
      identifier:
        platform: AWS_MSK
        identifier: "kafka-staging"
      internal: false
  - api: "/moego.devops.api.console.v1.MessageQueueApi/DeleteTopics"
    body:
      identifier:
        platform: AWS_MSK
        identifier: "kafka-staging"
      topics:
        - "test-sample1"
        - "test-sample2"
  - api: "/moego.devops.api.console.v1.MessageQueueApi/ListTopics"
    body:
      identifier:
        platform: AWS_MSK
        identifier: "kafka-staging"
      internal: false
