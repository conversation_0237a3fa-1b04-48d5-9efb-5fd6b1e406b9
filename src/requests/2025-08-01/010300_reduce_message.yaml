stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-message
      actions:
        - execute_sql:
            database: "moe_message"
            sql: |
              update moe_business_message_control
              set subscription_amount = 300
              where id = 232109;
