name: "150000_enterprise_add_index"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_enterprise"
            sql: |
              DROP INDEX IF EXISTS  idx_template_type_template_id;

              CREATE UNIQUE INDEX "idx_template_type_template_id" ON "public"."template_push_mapping" USING btree (
                "template_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
                "template_id" "pg_catalog"."int8_ops" ASC NULLS LAST,
                "target_organization_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
                "target_organization_id" "pg_catalog"."int8_ops" ASC NULLS LAST
              ) WHERE deleted_at IS NULL;
              

              CREATE INDEX "idx_template_push_organization_type_id" ON "public"."template_push_mapping" USING btree (
                "target_organization_type","target_organization_id"
              );
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_enterprise"
            sql: |
              ALTER table template_push_mapping add column deleted_at timestamp default null;

              DROP INDEX IF EXISTS  idx_template_type_template_id;

              CREATE UNIQUE INDEX "idx_template_type_template_id" ON "public"."template_push_mapping" USING btree (
                "template_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
                "template_id" "pg_catalog"."int8_ops" ASC NULLS LAST,
                "target_organization_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
                "target_organization_id" "pg_catalog"."int8_ops" ASC NULLS LAST
              ) WHERE deleted_at IS NULL;
              

              CREATE INDEX "idx_template_push_organization_type_id" ON "public"."template_push_mapping" USING btree (
                "target_organization_type","target_organization_id"
              ); 
