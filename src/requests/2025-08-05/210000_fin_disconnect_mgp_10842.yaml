name: "disconnect mgp for business 103984"
description: "disconnect mgp for business 103984"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              UPDATE moe_payment.mm_stripe_account 
              SET business_id = -10842, update_time = unix_timestamp(now())
              WHERE business_id = 10842;