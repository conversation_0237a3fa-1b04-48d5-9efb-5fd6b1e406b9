name: "fin_adyen"
description: "fin_adyen"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_payment"
            sql: |
              -- channel_recurring_payment_method
              DROP INDEX uniq_channel_payment_method;

              -- payment_transaction
              CREATE TABLE moego_payment.public.payment_transaction (
                id BIGSERIAL NOT NULL,
                created_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                updated_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                extra jsonb NOT NULL DEFAULT '{}'::jsonb,
                payer_type text NOT NULL DEFAULT '',
                payer_id BIGINT NOT NULL DEFAULT 0,
                payee_type text NOT NULL DEFAULT '',
                payee_id BIGINT NOT NULL DEFAULT 0,
                transaction_type text NOT NULL DEFAULT '',
                method_id BIGINT NOT NULL DEFAULT 0,
                channel_type text NOT NULL DEFAULT '',
                channel_transaction_id text NOT NULL DEFAULT '',
                channel_reference_id text NOT NULL DEFAULT '',
                status text NOT NULL DEFAULT '',
                currency CHAR(3) NOT NULL DEFAULT '',
                amount BIGINT NOT NULL DEFAULT 0,
                processing_fee BIGINT NOT NULL DEFAULT 0,
                convenience_fee BIGINT NOT NULL DEFAULT 0,
                tips BIGINT NOT NULL DEFAULT 0,
                paid_by text NOT NULL DEFAULT '',
                description text NOT NULL DEFAULT '',

                CONSTRAINT payment_transaction_pk PRIMARY KEY (id)
              );
              CREATE UNIQUE INDEX uniq_channel_transaction_reference ON public.payment_transaction (channel_reference_id);

              COMMENT ON TABLE payment_transaction IS 'channel account';
              COMMENT ON COLUMN payment_transaction.id is 'primary key';
              COMMENT ON COLUMN payment_transaction.created_time is 'created time';
              COMMENT ON COLUMN payment_transaction.updated_time is 'updated time';
              COMMENT ON COLUMN payment_transaction.extra is 'extra config, json';
              COMMENT ON COLUMN payment_transaction.payer_type is '买家类型,如CUSTOMER';
              COMMENT ON COLUMN payment_transaction.payer_id is '买家id,如customer id';
              COMMENT ON COLUMN payment_transaction.payee_type is '卖家类型,如BUSINESS';
              COMMENT ON COLUMN payment_transaction.payee_id is '卖家id,如business id';
              COMMENT ON COLUMN payment_transaction.transaction_type is '交易类型,STANDARD/PRE_AUTH/PRE_PAY/DEPOSIT';
              COMMENT ON COLUMN payment_transaction.method_id is '支付方式id,关联 payment_method 表';
              COMMENT ON COLUMN payment_transaction.channel_type is '渠道类型';
              COMMENT ON COLUMN payment_transaction.channel_transaction_id is '渠道支付id';
              COMMENT ON COLUMN payment_transaction.channel_reference_id is '渠道支付关联的幂等key,uuid';
              COMMENT ON COLUMN payment_transaction.status is '支付状态';
              COMMENT ON COLUMN payment_transaction.currency is '币种,三个字符';
              COMMENT ON COLUMN payment_transaction.amount is '支付总金额,单位:分';
              COMMENT ON COLUMN payment_transaction.processing_fee is '手续费,单位:分';
              COMMENT ON COLUMN payment_transaction.convenience_fee is 'fee by client,单位:分';
              COMMENT ON COLUMN payment_transaction.tips is 'payment直接关联的小费,单位:分';
              COMMENT ON COLUMN payment_transaction.paid_by is 'paid by,自定义,默认customer';
              COMMENT ON COLUMN payment_transaction.description is '支付描述,自定义';

              CREATE TRIGGER update_at
                BEFORE UPDATE
                ON public.payment_transaction
                FOR EACH ROW
              EXECUTE PROCEDURE update_at();

              ALTER SEQUENCE payment_transaction_id_seq RESTART WITH 5000000000;

              -- payment
              ALTER TABLE moego_payment.public.payment
                ADD COLUMN transaction_id BIGINT NOT NULL DEFAULT 0,
                ADD COLUMN method_id BIGINT NOT NULL DEFAULT 0;

              CREATE INDEX idx_payment_transaction ON public.payment (transaction_id);
              COMMENT ON COLUMN payment.transaction_id is '交易id,关联 payment_transaction 表';
              COMMENT ON COLUMN payment.method_id is '支付方式id,关联 payment_method 表';

              -- payment_method
              DROP INDEX uniq_payment_method;
              ALTER TABLE moego_payment.public.payment_method DROP COLUMN payment_id;

              -- dispute
              ALTER TABLE moego_payment.public.dispute RENAME COLUMN payment_id to transaction_id;
              ALTER TABLE moego_payment.public.dispute RENAME COLUMN channel_payment_id to channel_transaction_id;
              COMMENT ON COLUMN dispute.transaction_id is '关联的交易单据id';

              -- payment_setting
              COMMENT ON COLUMN payment_setting.pre_auth_bspd is 'business set pre-auth duration,提前发起 pre auth 的时间，单位秒';

              ALTER TABLE public.channel_recurring_payment_method
                ADD COLUMN channel_method_id TEXT NOT NULL DEFAULT '',
                ADD CONSTRAINT uniq_channel_type_method_id UNIQUE (channel_type, channel_method_id);

              COMMENT ON COLUMN public.channel_recurring_payment_method.channel_method_id IS '渠道方提供的唯一支付方式ID';

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_payment"
            sql: |
              -- channel_recurring_payment_method
              DROP INDEX uniq_channel_payment_method;

              -- payment_transaction
              CREATE TABLE moego_payment.public.payment_transaction (
                id BIGSERIAL NOT NULL,
                created_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                updated_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                extra jsonb NOT NULL DEFAULT '{}'::jsonb,
                payer_type text NOT NULL DEFAULT '',
                payer_id BIGINT NOT NULL DEFAULT 0,
                payee_type text NOT NULL DEFAULT '',
                payee_id BIGINT NOT NULL DEFAULT 0,
                transaction_type text NOT NULL DEFAULT '',
                method_id BIGINT NOT NULL DEFAULT 0,
                channel_type text NOT NULL DEFAULT '',
                channel_transaction_id text NOT NULL DEFAULT '',
                channel_reference_id text NOT NULL DEFAULT '',
                status text NOT NULL DEFAULT '',
                currency CHAR(3) NOT NULL DEFAULT '',
                amount BIGINT NOT NULL DEFAULT 0,
                processing_fee BIGINT NOT NULL DEFAULT 0,
                convenience_fee BIGINT NOT NULL DEFAULT 0,
                tips BIGINT NOT NULL DEFAULT 0,
                paid_by text NOT NULL DEFAULT '',
                description text NOT NULL DEFAULT '',

                CONSTRAINT payment_transaction_pk PRIMARY KEY (id)
              );
              CREATE UNIQUE INDEX uniq_channel_transaction_reference ON public.payment_transaction (channel_reference_id);

              COMMENT ON TABLE payment_transaction IS 'channel account';
              COMMENT ON COLUMN payment_transaction.id is 'primary key';
              COMMENT ON COLUMN payment_transaction.created_time is 'created time';
              COMMENT ON COLUMN payment_transaction.updated_time is 'updated time';
              COMMENT ON COLUMN payment_transaction.extra is 'extra config, json';
              COMMENT ON COLUMN payment_transaction.payer_type is '买家类型,如CUSTOMER';
              COMMENT ON COLUMN payment_transaction.payer_id is '买家id,如customer id';
              COMMENT ON COLUMN payment_transaction.payee_type is '卖家类型,如BUSINESS';
              COMMENT ON COLUMN payment_transaction.payee_id is '卖家id,如business id';
              COMMENT ON COLUMN payment_transaction.transaction_type is '交易类型,STANDARD/PRE_AUTH/PRE_PAY/DEPOSIT';
              COMMENT ON COLUMN payment_transaction.method_id is '支付方式id,关联 payment_method 表';
              COMMENT ON COLUMN payment_transaction.channel_type is '渠道类型';
              COMMENT ON COLUMN payment_transaction.channel_transaction_id is '渠道支付id';
              COMMENT ON COLUMN payment_transaction.channel_reference_id is '渠道支付关联的幂等key,uuid';
              COMMENT ON COLUMN payment_transaction.status is '支付状态';
              COMMENT ON COLUMN payment_transaction.currency is '币种,三个字符';
              COMMENT ON COLUMN payment_transaction.amount is '支付总金额,单位:分';
              COMMENT ON COLUMN payment_transaction.processing_fee is '手续费,单位:分';
              COMMENT ON COLUMN payment_transaction.convenience_fee is 'fee by client,单位:分';
              COMMENT ON COLUMN payment_transaction.tips is 'payment直接关联的小费,单位:分';
              COMMENT ON COLUMN payment_transaction.paid_by is 'paid by,自定义,默认customer';
              COMMENT ON COLUMN payment_transaction.description is '支付描述,自定义';

              CREATE TRIGGER update_at
                BEFORE UPDATE
                ON public.payment_transaction
                FOR EACH ROW
              EXECUTE PROCEDURE update_at();

              ALTER SEQUENCE payment_transaction_id_seq RESTART WITH 5000000000;

              -- payment
              ALTER TABLE moego_payment.public.payment
                ADD COLUMN transaction_id BIGINT NOT NULL DEFAULT 0,
                ADD COLUMN method_id BIGINT NOT NULL DEFAULT 0;

              CREATE INDEX idx_payment_transaction ON public.payment (transaction_id);
              COMMENT ON COLUMN payment.transaction_id is '交易id,关联 payment_transaction 表';
              COMMENT ON COLUMN payment.method_id is '支付方式id,关联 payment_method 表';

              -- payment_method
              DROP INDEX uniq_payment_method;
              ALTER TABLE moego_payment.public.payment_method DROP COLUMN payment_id;

              -- dispute
              ALTER TABLE moego_payment.public.dispute RENAME COLUMN payment_id to transaction_id;
              ALTER TABLE moego_payment.public.dispute RENAME COLUMN channel_payment_id to channel_transaction_id;
              COMMENT ON COLUMN dispute.transaction_id is '关联的交易单据id';

              -- payment_setting
              COMMENT ON COLUMN payment_setting.pre_auth_bspd is 'business set pre-auth duration,提前发起 pre auth 的时间，单位秒';

              ALTER TABLE public.channel_recurring_payment_method
                ADD COLUMN channel_method_id TEXT NOT NULL DEFAULT '',
                ADD CONSTRAINT uniq_channel_type_method_id UNIQUE (channel_type, channel_method_id);

              COMMENT ON COLUMN public.channel_recurring_payment_method.channel_method_id IS '渠道方提供的唯一支付方式ID';
