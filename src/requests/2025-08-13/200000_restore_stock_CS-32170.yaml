name: "Restore product stock"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_retail"
            sql: |
              UPDATE moe_retail.product SET update_time = 1755071361, stock = 1 WHERE id = 13097 AND business_id = 104213 AND stock = 0;
