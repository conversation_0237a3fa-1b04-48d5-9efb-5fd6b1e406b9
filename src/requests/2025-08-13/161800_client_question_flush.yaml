stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              update moe_grooming.moe_book_online_question
              set new_client_access_mode = case
              when accepted_customer_type in (1, 3)
              then 1 /* new/both -> enabled */
              when accepted_customer_type in (2)
              then 2 /* existing -> disabled */
              else 0
              end
              where accepted_customer_type in (1, 2, 3)
              and type = 2
              and new_client_access_mode = 0;
              
              update moe_grooming.moe_book_online_question
              set existing_client_access_mode = case
              when accepted_customer_type in (1) then 3 /* new -> disabled */
              when accepted_customer_type in (2, 3) and
              question in ('Phone number', 'Referral source')
              then 1 /* existing/both(phone/referral) -> view */
              when accepted_customer_type in (2, 3) and
              question not in ('Phone number', 'Referral source')
              then 2 /* existing/both(not phone/referral) -> view and edit */
              else 0
              end
              where accepted_customer_type in (1, 2, 3)
              and type = 2
              and existing_client_access_mode = 0;
