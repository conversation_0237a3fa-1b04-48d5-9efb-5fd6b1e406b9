name: "delete remain extra cv fee"
description: "delete remain extra cv fee cs ticket: https://moego.atlassian.net/browse/CS-32811"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              BEGIN;
              
              INSERT INTO public.order_line_extra_fee(business_id, order_id, order_item_id, apply_type, is_deleted, fee_type, amount, collect_type, description, apply_by, create_time, update_time) VALUES (108064, 155643047, 0, 'none', false, 'convenience fee', 2.31, 'subtract', '', 0, NOW(), NOW());

              UPDATE public."order" SET 
                  total_amount = total_amount - 2.31,
                  extra_fee_amount = extra_fee_amount - 2.31,
                  remain_amount = GREATEST(0, remain_amount - 2.31),
                  update_time = NOW()
              WHERE business_id = 108064 AND id = 155643047;          
              
              COMMIT;