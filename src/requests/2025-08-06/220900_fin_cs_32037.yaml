name: "Fix Order data for ticket cs-32037"
description: "移除多余的 fee 数据"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              ---- 更新 status
              BEGIN;
              
              UPDATE public.order
              SET extra_fee_amount = extra_fee_amount - 2.73,remain_amount = 0,total_amount = 0,update_time = NOW()
              where id = 151904127 and business_id = '116393';

              INSERT INTO public.order_line_extra_fee(business_id, order_id, order_item_id, apply_type, is_deleted, fee_type, amount, collect_type, description, apply_by, create_time, update_time) 
              VALUES (116393, 151904127, 0, 'none', false, 'convenience fee', 2.73, 'subtract', '', 0, NOW(), NOW());
              
              COMMIT;