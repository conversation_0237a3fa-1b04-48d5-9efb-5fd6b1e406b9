name: "Fix Order data for ticket cs-32156"
description: "Fix Order table and payment && pay detail table data for ticket cs-32156"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              ---- 更新 payment status 和 insert moe_pay_detail record
              BEGIN;

              UPDATE moe_payment.payment
                SET status = 3, update_time = unix_timestamp(now())
                WHERE invoice_id = ********* AND stripe_intent_id = 'pi_3RRiYMIZwcIFVLGr122Qq91m';

              INSERT INTO moe_payment.moe_pay_detail
                (business_id, payment_id, order_id, stripe_intent_id, amount, gross_sales, discount, tax, tips, booking_fee, convenience_fee, is_deposit, company_id)
                VALUES(122772, 30463658, *********, 'pi_3RRiYMIZwcIFVLGr122Qq91m', 410.00, 410.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0, 122283);
              
              COMMIT;
