name: "fin_adyen"
description: "fin_adyen"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_payment"
            sql: |
              ALTER TABLE moego_payment.public.payment_transaction DROP COLUMN extra;

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_payment"
            sql: |
              ALTER TABLE moego_payment.public.payment_transaction DROP COLUMN extra;
