name: "Limitation support or"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_business"
            sql: |
              create table day_hour_limit_group
              (
                id                   bigint auto_increment
                primary key,
                only_accept_selected boolean      default false                not null,
                created_at           timestamp(6) default CURRENT_TIMESTAMP(6) not null,
                updated_at           timestamp(6) default CURRENT_TIMESTAMP(6) not null
              );
              alter table day_hour_limit add group_id bigint default 0 not null;
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_business"
            sql: |
              create table day_hour_limit_group
              (
                id                   bigint auto_increment
                primary key,
                only_accept_selected boolean      default false                not null,
                created_at           timestamp(6) default CURRENT_TIMESTAMP(6) not null,
                updated_at           timestamp(6) default CURRENT_TIMESTAMP(6) not null
              );
              alter table day_hour_limit add group_id bigint default 0 not null;
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_online_booking"
            sql: |
              create table day_hour_limit_group
              (
                id                   bigserial primary key,
                only_accept_selected boolean      default false                not null,
                created_at           timestamp(6) default CURRENT_TIMESTAMP(6) not null,
                updated_at           timestamp(6) default CURRENT_TIMESTAMP(6) not null
              );
              alter table day_hour_limit add group_id bigint default 0 not null;
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_online_booking"
            sql: |
              create table day_hour_limit_group
              (
                id                   bigserial primary key,
                only_accept_selected boolean      default false                not null,
                created_at           timestamp(6) default CURRENT_TIMESTAMP(6) not null,
                updated_at           timestamp(6) default CURRENT_TIMESTAMP(6) not null
              );
              alter table day_hour_limit add group_id bigint default 0 not null;
