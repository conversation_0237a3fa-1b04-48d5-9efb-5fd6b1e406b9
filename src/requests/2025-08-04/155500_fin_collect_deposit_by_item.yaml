name: "155500_fin_collect_deposit_by_item"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              ALTER TABLE order_item_price_detail
                ADD COLUMN object_type TEXT DEFAULT 'ITEM_TYPE_UNSPECIFIED' NOT NULL,
                ADD COLUMN object_id BIGINT DEFAULT 0 NOT NULL;
              COMMENT ON COLUMN order_item_price_detail.object_type IS 'Referring object type';
              COMMENT ON COLUMN order_item_price_detail.object_id IS 'Referring object id';
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              ALTER TABLE order_item_price_detail
                ADD COLUMN object_type TEXT DEFAULT 'ITEM_TYPE_UNSPECIFIED' NOT NULL,
                ADD COLUMN object_id BIGINT DEFAULT 0 NOT NULL;
              COMMENT ON COLUMN order_item_price_detail.object_type IS 'Referring object type';
              COMMENT ON COLUMN order_item_price_detail.object_id IS 'Referring object id';
