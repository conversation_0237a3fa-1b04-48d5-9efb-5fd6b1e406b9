name: "adyen add terminal table"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_payment"
            username: "${db.user.admin}"
            sql: |
              CREATE TABLE moego_payment.public.terminal (
                  id BIGSERIAL NOT NULL,
                  created_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                  updated_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                  terminal_type text NOT NULL DEFAULT '',
                  channel_type text NOT NULL DEFAULT '',
                  channel_terminal_id text NOT NULL DEFAULT '',
                  entity_type text NOT NULL DEFAULT '',
                  entity_id BIGINT NOT NULL DEFAULT 0,
                  state text NOT NULL DEFAULT '',
                  last_transaction_id BIGINT NOT NULL DEFAULT 0,
                  CONSTRAINT terminal_pk PRIMARY KEY (id)
              );
              CREATE UNIQUE INDEX uniq_terminal_channel_terminal_id ON public.terminal (channel_type, channel_terminal_id);
              CREATE INDEX idx_terminal_entity_type_entity_id ON public.terminal (entity_type, entity_id);
              COMMENT ON TABLE terminal IS 'terminal';
              COMMENT ON COLUMN terminal.id IS 'pimary key';
              COMMENT ON COLUMN terminal.created_time IS 'created_time';
              COMMENT ON COLUMN terminal.updated_time IS 'updated_time';
              COMMENT ON COLUMN terminal.terminal_type IS 'enum: pb terminal_type';
              COMMENT ON COLUMN terminal.channel_type IS 'enum: pb channel_type';
              COMMENT ON COLUMN terminal.channel_terminal_id IS 'channel terminal id: model + serial_no';
              COMMENT ON COLUMN terminal.entity_type IS 'enum: pb entity_type';
              COMMENT ON COLUMN terminal.entity_id IS 'entity id';
              COMMENT ON COLUMN terminal.state IS 'enum: pb terminal.state';
              COMMENT ON COLUMN terminal.last_transaction_id IS 'last transaction id';

              CREATE TRIGGER update_at
                  BEFORE update
                  ON public.terminal 
                  FOR EACH ROW
              EXECUTE PROCEDURE update_at();

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_payment"
            username: "${db.user.admin}"
            sql: |
              CREATE TABLE moego_payment.public.terminal (
                  id BIGSERIAL NOT NULL,
                  created_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                  updated_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
                  terminal_type text NOT NULL DEFAULT '',
                  channel_type text NOT NULL DEFAULT '',
                  channel_terminal_id text NOT NULL DEFAULT '',
                  entity_type text NOT NULL DEFAULT '',
                  entity_id BIGINT NOT NULL DEFAULT 0,
                  state text NOT NULL DEFAULT '',
                  last_transaction_id BIGINT NOT NULL DEFAULT 0,
                  CONSTRAINT terminal_pk PRIMARY KEY (id)
              );
              CREATE UNIQUE INDEX uniq_terminal_channel_terminal_id ON public.terminal (channel_type, channel_terminal_id);
              CREATE INDEX idx_terminal_entity_type_entity_id ON public.terminal (entity_type, entity_id);
              COMMENT ON TABLE terminal IS 'terminal';
              COMMENT ON COLUMN terminal.id IS 'pimary key';
              COMMENT ON COLUMN terminal.created_time IS 'created_time';
              COMMENT ON COLUMN terminal.updated_time IS 'updated_time';
              COMMENT ON COLUMN terminal.terminal_type IS 'enum: pb terminal_type';
              COMMENT ON COLUMN terminal.channel_type IS 'enum: pb channel_type';
              COMMENT ON COLUMN terminal.channel_terminal_id IS 'channel terminal id: model + serial_no';
              COMMENT ON COLUMN terminal.entity_type IS 'enum: pb entity_type';
              COMMENT ON COLUMN terminal.entity_id IS 'entity id';
              COMMENT ON COLUMN terminal.state IS 'enum: pb terminal.state';
              COMMENT ON COLUMN terminal.last_transaction_id IS 'last transaction id';

              CREATE TRIGGER update_at
                  BEFORE update
                  ON public.terminal 
                  FOR EACH ROW
              EXECUTE PROCEDURE update_at();
