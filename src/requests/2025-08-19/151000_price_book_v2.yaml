name: "151000_price_book_v2"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_enterprise"
            sql: |

              CREATE TABLE "public"."lodging_type" (
                "id" BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
                "enterprise_id" BIGINT NOT NULL DEFAULT 0,
                "description" TEXT NOT NULL DEFAULT '',
                "name" TEXT NOT NULL DEFAULT '',
                "photo" JSONB DEFAULT '[]'::jsonb,
                "max_pet_num" INT NOT NULL DEFAULT 0,
                "max_pet_total_weight" INT NOT NULL DEFAULT 0,
                "pet_size_filter" BOOLEAN NOT NULL DEFAULT FALSE,
                "allowed_pet_size_list" BIGINT[] NOT NULL DEFAULT '{}',
                "type" TEXT NOT NULL DEFAULT 'LODGING_UNIT_TYPE_UNSPECIFIED'::TEXT,
                "sort" INT NOT NULL DEFAULT 0,
                "created_at" TIMESTAMP NOT NULL DEFAULT NOW(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(),
                "deleted_at" TIMESTAMP DEFAULT NULL,
                "pushed_at" TIMESTAMP DEFAULT NULL
                );


              COMMENT ON COLUMN "public"."lodging_type"."photo" IS 'photo list';

              COMMENT ON COLUMN "public"."lodging_type"."max_pet_num" IS 'maximum number of pets the lodging can accommodate';

              COMMENT ON COLUMN "public"."lodging_type"."max_pet_total_weight" IS 'maximum weight of pets the lodging can accommodate';

              COMMENT ON COLUMN "public"."lodging_type"."pet_size_filter" IS 'if need pet size filter';

              COMMENT ON COLUMN "public"."lodging_type"."allowed_pet_size_list" IS 'allowed pet size list, only when service_filter is true';

              COMMENT ON COLUMN "public"."lodging_type"."type" IS 'lodging 类型：1-Room/kennel type；2-Area type';

              COMMENT ON TABLE "public"."lodging_type" IS 'lodging template';


              CREATE TABLE IF NOT EXISTS pricing_rule (
                  id BIGSERIAL PRIMARY KEY,
                  enterprise_id BIGINT NOT NULL DEFAULT 0,
                  price_book_id BIGINT NOT NULL DEFAULT 0,
                  rule_type TEXT NOT NULL DEFAULT 'RULE_TYPE_UNSPECIFIED'::TEXT, 
                  rule_name TEXT NOT NULL DEFAULT '',
                  is_active BOOLEAN NOT NULL DEFAULT FALSE,
                  rule_apply_type TEXT NOT NULL DEFAULT 'RULE_APPLY_TYPE_UNSPECIFIED'::TEXT,
                  need_in_same_lodging BOOLEAN NOT NULL DEFAULT FALSE,
                  rule_configuration JSONB NOT NULL DEFAULT '{}'::JSONB,
                  is_charge_per_lodging BOOLEAN NOT NULL DEFAULT FALSE,
                  limitation JSONB NOT NULL DEFAULT '{}'::JSONB,
                  sort BIGINT NOT NULL DEFAULT 0,
                  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
                  updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
                  deleted_at TIMESTAMP,
                  pushed_at  TIMESTAMP DEFAULT NULL
              );
              CREATE INDEX "idx_pricing_rule_enterprise_id" ON "public"."pricing_rule" USING btree (
                "enterprise_id"
              );
              -- service_charge
              create table if not exists service_charge
              (
                  id                      BIGSERIAL PRIMARY KEY,
                  enterprise_id           bigint         default 0                    not null,
                  price_book_id           bigint         default 0                    not null,
                  name                    text           default ''                   not null,
                  description             text           default ''                   not null,
                  service_item_types    bigint[] NOT NULL DEFAULT '{}',
                  price                   numeric(20,2)  default 0.00                 not null,
                  tax_rate                bigint         default 0                    not null,
                  sort                    integer        default 0                    not null,
                  auto_apply_status       text           default 'AUTO_APPLY_STATUS_UNSPECIFIED' not null,
                  auto_apply_condition    text           default 'AUTO_APPLY_CONDITION_UNSPECIFIED' not null,
                  auto_apply_time         integer        default 0                    not null,
                  is_active               boolean        default true                 not null,
                  apply_type              text           default 'APPLY_TYPE_UNSPECIFIED' not null,
                  auto_apply_time_type    text           default null,
                  surcharge_type          text           default null,
                  charge_method           text           default 'CHARGE_METHOD_UNSPECIFIED' not null,
                  time_based_pricing_type text           default null,
                  multiple_pets_charge_type text         default null,
                  hourly_exceed_rules     jsonb          default '{}'                 not null,
                  limitation              jsonb          default '{}'                 not null,
                  created_at              timestamp      default now()                not null,
                  updated_at              timestamp      default now()                not null,
                  deleted_at              timestamp      default null,
                  pushed_at               TIMESTAMP      DEFAULT NULL
              );

              CREATE TRIGGER update_at
                  BEFORE UPDATE
                  ON service_charge
                  FOR EACH ROW
              EXECUTE PROCEDURE update_at();

              CREATE INDEX "idx_service_charge_enterprise_id" ON "public"."service_charge" USING btree (
                "enterprise_id"
              );
              alter table service add column pushed_at TIMESTAMP DEFAULT NULL;
              alter table service add column require_dedicated_staff boolean default false not null;
              alter table service add column auto_rule text default '{}' not null;
              create table if not exists pet_code
              (
                id            bigint generated always as identity primary key,
                abbreviation  text      not null default '',
                description   text      not null default '',
                color         text      not null default '',
                sort          integer   not null default 0,
                enterprise_id bigint    not null default 0,
                updated_at    timestamp not null default now(),
                created_at    timestamp not null default now(),
                deleted_at    timestamp,
                pushed_at     timestamp
              );

              create index idx_pet_code_enterprise_id on pet_code (enterprise_id);

              comment on table pet_code is 'pet code';
              comment on column pet_code.id is 'id';
              comment on column pet_code.abbreviation is 'abbreviation';
              comment on column pet_code.description is 'description';
              comment on column pet_code.color is 'color';
              comment on column pet_code.sort is 'sort';
              comment on column pet_code.enterprise_id is 'enterprise id';
              comment on column pet_code.updated_at is 'updated timestamp';
              comment on column pet_code.created_at is 'created timestamp';
              comment on column pet_code.deleted_at is 'deleted timestamp';
              comment on column pet_code.pushed_at is 'pushed timestamp';

              CREATE TRIGGER update_at
                BEFORE UPDATE
                ON pet_code
                FOR EACH ROW
              EXECUTE PROCEDURE update_at();

              -- pet_metadata
              create table if not exists pet_metadata
              (
                id             bigint generated always as identity primary key,
                enterprise_id  bigint    not null default 0,
                metadata_name  text      not null default '',
                metadata_value text      not null default '',
                extra_json     jsonb     not null default '{}'::jsonb,
                sort           integer   not null default 0,
                created_at     timestamp not null default now(),
                updated_at     timestamp not null default now(),
                pushed_at      timestamp,
                deleted_at     timestamp
              );

              create index idx_pet_metadata_enterprise_id on pet_metadata (enterprise_id);

              comment on table pet_metadata is 'pet metadata';
              comment on column pet_metadata.id is 'id';
              comment on column pet_metadata.enterprise_id is 'enterprise id';
              comment on column pet_metadata.metadata_name is 'metadata name';
              comment on column pet_metadata.metadata_value is 'metadata value';
              comment on column pet_metadata.extra_json is 'extra json';
              comment on column pet_metadata.sort is 'sort';
              comment on column pet_metadata.created_at is 'created timestamp';
              comment on column pet_metadata.updated_at is 'updated timestamp';
              comment on column pet_metadata.pushed_at is 'pushed timestamp';
              comment on column pet_metadata.deleted_at is 'deleted timestamp';

              CREATE TRIGGER update_at
                BEFORE UPDATE
                ON pet_metadata
                FOR EACH ROW
              EXECUTE PROCEDURE update_at();
              ALTER TABLE "template_push_change_history" 
              ADD COLUMN "push_result" jsonb NOT NULL DEFAULT '{}'::jsonb;
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_enterprise"
            sql: |
              CREATE TABLE "public"."price_book" (
                  "id" bigint generated always as identity primary key,
                  "enterprise_id" int8 NOT NULL DEFAULT 0,
                  "name" text COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::text,
                  "created_at" timestamp(6) NOT NULL DEFAULT now(),
                  "updated_at" timestamp(6) NOT NULL DEFAULT now(),
                  "deleted_at" timestamp(6)
              )
              ;

              CREATE INDEX "idx_pbc_enterprise_id" ON "public"."price_book" USING btree (
              "enterprise_id" "pg_catalog"."int8_ops" ASC NULLS LAST
              );

              CREATE TRIGGER "update_at" BEFORE UPDATE ON "public"."price_book"
              FOR EACH ROW
              EXECUTE PROCEDURE "public"."update_at"();

              INSERT INTO "public"."price_book" ("name") VALUES ('Standard');
              INSERT INTO "public"."price_book" ("name") VALUES ('Premium');
              INSERT INTO "public"."price_book" ("name") VALUES ('Economic');

              --evaluation
              create table if not exists evaluation
              (
                  id                    bigint generated always as identity primary key,
                  enterprise_id         bigint         default 0                    not null,
                  price_book_id         bigint         default 0                    not null,
                  service_item_types    bigint[] NOT NULL DEFAULT '{}',
                  price                 numeric(20,2)  default 0.00                 not null,
                  duration              text           default '0s'                 not null,
                  color_code            text           default ''                   not null,
                  name                  text           default ''                   not null,
                  is_active             boolean        default true                 not null,
                  description           text           default ''                   not null,
                  alias_for_online_booking text        default ''                   not null,
                  allow_staff_auto_assign boolean      default false                not null,
                  is_resettable         boolean        default false                not null,
                  reset_interval_days   bigint         default 0                    not null,
                  sort                  bigint         default 0                    not null,
                  tax_rate              bigint         default 0                    not null,
                  limitation            JSONB          default '{}'                 not null,
                  created_at            timestamp      default now()                not null,
                  updated_at            timestamp      default now()                not null,
                  deleted_at            timestamp      default null,
                  pushed_at             TIMESTAMP      DEFAULT NULL
              );

              CREATE TRIGGER update_at
                  BEFORE UPDATE
                  ON evaluation
                  FOR EACH ROW
              EXECUTE PROCEDURE update_at();

              CREATE INDEX "idx_evaluation_enterprise_id" ON "public"."evaluation" USING btree (
                "enterprise_id"
              );

              CREATE TABLE "public"."lodging_type" (
                "id" BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
                "enterprise_id" BIGINT NOT NULL DEFAULT 0,
                "description" TEXT NOT NULL DEFAULT '',
                "name" TEXT NOT NULL DEFAULT '',
                "photo" JSONB DEFAULT '[]'::jsonb,
                "max_pet_num" INT NOT NULL DEFAULT 0,
                "max_pet_total_weight" INT NOT NULL DEFAULT 0,
                "pet_size_filter" BOOLEAN NOT NULL DEFAULT FALSE,
                "allowed_pet_size_list" BIGINT[] NOT NULL DEFAULT '{}',
                "type" TEXT NOT NULL DEFAULT 'LODGING_UNIT_TYPE_UNSPECIFIED'::TEXT,
                "sort" INT NOT NULL DEFAULT 0,
                "created_at" TIMESTAMP NOT NULL DEFAULT NOW(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(),
                "deleted_at" TIMESTAMP DEFAULT NULL,
                "pushed_at" TIMESTAMP DEFAULT NULL
                );


              COMMENT ON COLUMN "public"."lodging_type"."photo" IS 'photo list';

              COMMENT ON COLUMN "public"."lodging_type"."max_pet_num" IS 'maximum number of pets the lodging can accommodate';

              COMMENT ON COLUMN "public"."lodging_type"."max_pet_total_weight" IS 'maximum weight of pets the lodging can accommodate';

              COMMENT ON COLUMN "public"."lodging_type"."pet_size_filter" IS 'if need pet size filter';

              COMMENT ON COLUMN "public"."lodging_type"."allowed_pet_size_list" IS 'allowed pet size list, only when service_filter is true';

              COMMENT ON COLUMN "public"."lodging_type"."type" IS 'lodging 类型：1-Room/kennel type；2-Area type';

              COMMENT ON TABLE "public"."lodging_type" IS 'lodging template';


              CREATE TABLE IF NOT EXISTS pricing_rule (
                  id BIGSERIAL PRIMARY KEY,
                  enterprise_id BIGINT NOT NULL DEFAULT 0,
                  price_book_id BIGINT NOT NULL DEFAULT 0,
                  rule_type TEXT NOT NULL DEFAULT 'RULE_TYPE_UNSPECIFIED'::TEXT, 
                  rule_name TEXT NOT NULL DEFAULT '',
                  is_active BOOLEAN NOT NULL DEFAULT FALSE,
                  rule_apply_type TEXT NOT NULL DEFAULT 'RULE_APPLY_TYPE_UNSPECIFIED'::TEXT,
                  need_in_same_lodging BOOLEAN NOT NULL DEFAULT FALSE,
                  rule_configuration JSONB NOT NULL DEFAULT '{}'::JSONB,
                  is_charge_per_lodging BOOLEAN NOT NULL DEFAULT FALSE,
                  limitation JSONB NOT NULL DEFAULT '{}'::JSONB,
                  sort BIGINT NOT NULL DEFAULT 0,
                  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
                  updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
                  deleted_at TIMESTAMP,
                  pushed_at  TIMESTAMP DEFAULT NULL
              );
              CREATE INDEX "idx_pricing_rule_enterprise_id" ON "public"."pricing_rule" USING btree (
                "enterprise_id"
              );
              -- service_charge
              create table if not exists service_charge
              (
                  id                      BIGSERIAL PRIMARY KEY,
                  enterprise_id           bigint         default 0                    not null,
                  price_book_id           bigint         default 0                    not null,
                  name                    text           default ''                   not null,
                  description             text           default ''                   not null,
                  service_item_types    bigint[] NOT NULL DEFAULT '{}',
                  price                   numeric(20,2)  default 0.00                 not null,
                  tax_rate                bigint         default 0                    not null,
                  sort                    integer        default 0                    not null,
                  auto_apply_status       text           default 'AUTO_APPLY_STATUS_UNSPECIFIED' not null,
                  auto_apply_condition    text           default 'AUTO_APPLY_CONDITION_UNSPECIFIED' not null,
                  auto_apply_time         integer        default 0                    not null,
                  is_active               boolean        default true                 not null,
                  apply_type              text           default 'APPLY_TYPE_UNSPECIFIED' not null,
                  auto_apply_time_type    text           default null,
                  surcharge_type          text           default null,
                  charge_method           text           default 'CHARGE_METHOD_UNSPECIFIED' not null,
                  time_based_pricing_type text           default null,
                  multiple_pets_charge_type text         default null,
                  hourly_exceed_rules     jsonb          default '{}'                 not null,
                  limitation              jsonb          default '{}'                 not null,
                  created_at              timestamp      default now()                not null,
                  updated_at              timestamp      default now()                not null,
                  deleted_at              timestamp      default null,
                  pushed_at               TIMESTAMP      DEFAULT NULL
              );

              CREATE TRIGGER update_at
                  BEFORE UPDATE
                  ON service_charge
                  FOR EACH ROW
              EXECUTE PROCEDURE update_at();

              CREATE INDEX "idx_service_charge_enterprise_id" ON "public"."service_charge" USING btree (
                "enterprise_id"
              );
              alter table service add column pushed_at TIMESTAMP DEFAULT NULL;
              alter table service add column require_dedicated_staff boolean default false not null;
              alter table service add column auto_rule text default '{}' not null;
              create table if not exists pet_code
              (
                id            bigint generated always as identity primary key,
                abbreviation  text      not null default '',
                description   text      not null default '',
                color         text      not null default '',
                sort          integer   not null default 0,
                enterprise_id bigint    not null default 0,
                updated_at    timestamp not null default now(),
                created_at    timestamp not null default now(),
                deleted_at    timestamp,
                pushed_at     timestamp
              );

              create index idx_pet_code_enterprise_id on pet_code (enterprise_id);

              comment on table pet_code is 'pet code';
              comment on column pet_code.id is 'id';
              comment on column pet_code.abbreviation is 'abbreviation';
              comment on column pet_code.description is 'description';
              comment on column pet_code.color is 'color';
              comment on column pet_code.sort is 'sort';
              comment on column pet_code.enterprise_id is 'enterprise id';
              comment on column pet_code.updated_at is 'updated timestamp';
              comment on column pet_code.created_at is 'created timestamp';
              comment on column pet_code.deleted_at is 'deleted timestamp';
              comment on column pet_code.pushed_at is 'pushed timestamp';

              CREATE TRIGGER update_at
                BEFORE UPDATE
                ON pet_code
                FOR EACH ROW
              EXECUTE PROCEDURE update_at();

              -- pet_metadata
              create table if not exists pet_metadata
              (
                id             bigint generated always as identity primary key,
                enterprise_id  bigint    not null default 0,
                metadata_name  text      not null default '',
                metadata_value text      not null default '',
                extra_json     jsonb     not null default '{}'::jsonb,
                sort           integer   not null default 0,
                created_at     timestamp not null default now(),
                updated_at     timestamp not null default now(),
                pushed_at      timestamp,
                deleted_at     timestamp
              );

              create index idx_pet_metadata_enterprise_id on pet_metadata (enterprise_id);

              comment on table pet_metadata is 'pet metadata';
              comment on column pet_metadata.id is 'id';
              comment on column pet_metadata.enterprise_id is 'enterprise id';
              comment on column pet_metadata.metadata_name is 'metadata name';
              comment on column pet_metadata.metadata_value is 'metadata value';
              comment on column pet_metadata.extra_json is 'extra json';
              comment on column pet_metadata.sort is 'sort';
              comment on column pet_metadata.created_at is 'created timestamp';
              comment on column pet_metadata.updated_at is 'updated timestamp';
              comment on column pet_metadata.pushed_at is 'pushed timestamp';
              comment on column pet_metadata.deleted_at is 'deleted timestamp';

              CREATE TRIGGER update_at
                BEFORE UPDATE
                ON pet_metadata
                FOR EACH ROW
              EXECUTE PROCEDURE update_at();
              ALTER TABLE "template_push_change_history" 
              ADD COLUMN "push_result" jsonb NOT NULL DEFAULT '{}'::jsonb;