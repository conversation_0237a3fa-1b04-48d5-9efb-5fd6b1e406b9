name: "Staff support slot free service"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_business"
            sql: |
              create table staff_slot_free_service
              (
                id          bigint auto_increment
                primary key,
                company_id  bigint                              not null,
                business_id bigint                              not null,
                staff_id    bigint                              not null,
                service_id  bigint                              not null,
                created_at  timestamp default CURRENT_TIMESTAMP not null
              );
              create index idx_business_staff on staff_slot_free_service (business_id, staff_id);
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_business"
            sql: |
              create table staff_slot_free_service
              (
                id          bigint auto_increment
                primary key,
                company_id  bigint                              not null,
                business_id bigint                              not null,
                staff_id    bigint                              not null,
                service_id  bigint                              not null,
                created_at  timestamp default CURRENT_TIMESTAMP not null
              );
              create index idx_business_staff on staff_slot_free_service (business_id, staff_id);
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_online_booking"
            sql: |
              create table staff_slot_free_service
              (
                id          bigserial
                primary key,
                company_id  bigint                              not null,
                business_id bigint                              not null,
                staff_id    bigint                              not null,
                service_id  bigint                              not null,
                created_at  timestamp default CURRENT_TIMESTAMP not null
              );
              create index idx_business_staff on staff_slot_free_service (business_id, staff_id);
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_online_booking"
            sql: |
              create table staff_slot_free_service
              (
                id          bigserial
                primary key,
                company_id  bigint                              not null,
                business_id bigint                              not null,
                staff_id    bigint                              not null,
                service_id  bigint                              not null,
                created_at  timestamp default CURRENT_TIMESTAMP not null
              );
              create index idx_business_staff on staff_slot_free_service (business_id, staff_id);
