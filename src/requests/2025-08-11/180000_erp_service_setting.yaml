name: "Update service require_dedicated_staff for CS31866 company"
description: "Update service require_dedicated_staff for CS31866 company"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-grooming"
      actions:
        - execute_sql:
            database: "moe_grooming"
            sql: |
              update moe_grooming.moe_grooming_service
              set require_dedicated_staff = true, update_time = unix_timestamp()
              where id in (1669067, 1669074, 1669076, 1669081, 1669086, 1669091, 1669092, 1669093, 1669096, 1669097, 1669098, 1669101,
                           1669102, 1669103, 1669109, 1669110, 1669111, 1669114, 1669119, 1669120, 1669123, 1669124, 1669144)
                and company_id = 123132
                and category_id != 291410;