name: "add column source"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_offering"
            sql: |
              ALTER TABLE "public"."pricing_rule_record" 
              ADD COLUMN "source" int4 NOT NULL DEFAULT 1;
              COMMENT ON COLUMN "public"."pricing_rule_record"."source" IS '1-MoeGo Platform 2-Enterprise Hub';
              
              ALTER TABLE "public"."evaluation" 
                ADD COLUMN "source" int4 NOT NULL DEFAULT 1;
              COMMENT ON COLUMN "public"."evaluation"."source" IS '1-MoeGo Platform 2-Enterprise Hub';
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_offering"
            sql: |
              ALTER TABLE "public"."pricing_rule_record" 
              ADD COLUMN "source" int4 NOT NULL DEFAULT 1;
              COMMENT ON COLUMN "public"."pricing_rule_record"."source" IS '1-MoeGo Platform 2-Enterprise Hub';

              ALTER TABLE "public"."evaluation" 
                ADD COLUMN "source" int4 NOT NULL DEFAULT 1;
              COMMENT ON COLUMN "public"."evaluation"."source" IS '1-MoeGo Platform 2-Enterprise Hub';
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              ALTER TABLE "public"."service_charge" 
              ADD COLUMN "source" int2 NOT NULL DEFAULT 1;
              COMMENT ON COLUMN "public"."service_charge"."source" IS '1-MoeGo Platform 2-Enterprise Hub';
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              ALTER TABLE "public"."service_charge" 
              ADD COLUMN "source" int2 NOT NULL DEFAULT 1;
              COMMENT ON COLUMN "public"."service_charge"."source" IS '1-MoeGo Platform 2-Enterprise Hub';

