name: "disconnect mgp for business 112045"
description: "disconnect mgp for business 112045"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              UPDATE moe_payment.mm_stripe_account 
              SET business_id = -7774, update_time = unix_timestamp(now())
              WHERE business_id = 7774;
              
              UPDATE moe_payment.mm_stripe_account 
              SET business_id = -7775, update_time = unix_timestamp(now())
              WHERE business_id = 7775;
              
              UPDATE moe_payment.mm_stripe_account 
              SET business_id = -7776, update_time = unix_timestamp(now())
              WHERE business_id = 7776;