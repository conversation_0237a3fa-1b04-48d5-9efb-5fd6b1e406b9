name: "Remove Red Dog evaluation override configuration"
description: "Remove Red Dog evaluation override configuration"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_offering"
            sql: |
              update evaluation_location_override set deleted_at = now() where evaluation_id = 1589;
