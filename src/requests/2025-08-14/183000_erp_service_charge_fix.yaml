name: "service charge applicable care type fix"
description: "service charge applicable care type fix"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              UPDATE public.service_charge SET service_item_types = '{1}', updated_at = now() WHERE surcharge_type = 2 and service_item_types = '{}' and auto_apply_status = 2 and is_deleted = false;
