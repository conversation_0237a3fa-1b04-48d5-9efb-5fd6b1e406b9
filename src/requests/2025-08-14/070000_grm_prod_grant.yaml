name: "Grant permission to <PERSON>"
description: "grant permission to <PERSON>"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_offering"
            username: "${db.user.admin}"
            sql: |
              GRANT USAGE ON SCHEMA public TO developer_bob;
              GRANT SELECT, UPDATE, DELETE, INSERT ON ALL TABLES IN SCHEMA public TO developer_bob;
        - execute_sql:
            database: "moego_online_booking"
            username: "${db.user.admin}"
            sql: |
              GRANT USAGE ON SCHEMA public TO developer_bob;
              GRANT SELECT, UPDATE, DELETE, INSERT ON ALL TABLES IN SCHEMA public TO developer_bob;
        - execute_sql:
            database: "moego_appointment"
            username: "${db.user.admin}"
            sql: |
              GRANT USAGE ON SCHEMA public TO developer_bob;
              GRANT SELECT, UPDATE, <PERSON>LETE, INSERT ON ALL TABLES IN SCHEMA public TO developer_bob;
