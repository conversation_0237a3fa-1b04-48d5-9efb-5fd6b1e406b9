name: "Fix Payment data for 33981181"
description: "Fix payment && pay detail table data for 33981181"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-cluster"
      actions:
        - execute_sql:
            database: "moe_payment"
            sql: |
              ---- 更新 payment status 和 insert moe_pay_detail record
              BEGIN;

              UPDATE moe_payment.payment
                SET status = 3, update_time = unix_timestamp(now())
                WHERE id = 33981181;

              INSERT INTO moe_payment.moe_pay_detail
                (business_id, payment_id, order_id, stripe_intent_id, amount, gross_sales, discount, tax, tips, booking_fee, convenience_fee, is_deposit, company_id)
                VALUES(122287, 33981181, *********, 'pi_3RnKctIZwcIFVLGr25u6Hllx', 174.26, 174.26, 0.00, 0.00, 0, 0.00, 0.00, 0, 121834);
              
              COMMIT;
