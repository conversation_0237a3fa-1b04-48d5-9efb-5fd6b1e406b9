name: "141000_lodging_add_source"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_offering"
            sql: |
              ALTER TABLE "public"."lodging_type" 
                ADD COLUMN "source" int4 NOT NULL DEFAULT 1;

              COMMENT ON COLUMN "public"."lodging_type"."source" IS '1-MoeGo Platform 2-Enterprise Hub';
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_offering"
            sql: |
              ALTER TABLE "public"."lodging_type" 
                ADD COLUMN "source" int4 NOT NULL DEFAULT 1;

              COMMENT ON COLUMN "public"."lodging_type"."source" IS '1-MoeGo Platform 2-Enterprise Hub';
