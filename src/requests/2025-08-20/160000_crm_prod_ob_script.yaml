name: "ddl for customer portal ob script"
description: "add new table"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-postgres-cluster
      actions:
        - execute_sql:
            database: "moego_customer_portal"
            sql: |
              CREATE TABLE online_booking_setting
              (
              id          BIGSERIAL
              PRIMARY KEY,
              company_id  BIGINT    NOT NULL,
              business_id BIGINT    NOT NULL,
              css         TEXT      NOT NULL DEFAULT '',
              js          TEXT      NOT NULL DEFAULT '',
              
              created_by  BIGINT    NOT NULL,
              updated_by  BIGINT    NOT NULL,
              deleted_by  BIGINT,
              created_at  TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
              updated_at  TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
              deleted_at  TIMESTAMP
              );