name: "CRM notification arch tables"
description: "Create moe_message.moe_notification_bak_20250815 and moe_notification_record_bak_20250815"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: prod-mysql-message
      actions:
        - execute_sql:
            database: "moe_message"
            sql: |
              create table moe_notification_bak_20250815
              (
                  id          int unsigned auto_increment
                      primary key,
                  business_id int           default 0                      not null,
                  title       varchar(500)  default ''                     not null,
                  body        varchar(1000) default ''                     not null,
                  type        varchar(50)   default 'activity_book_create' not null,
                  extra       varchar(1000) default ''                     not null,
                  create_time bigint        default 0                      not null,
                  company_id  bigint        default 0                      not null
              )
                  collate = utf8mb4_unicode_520_ci;
        - execute_sql:
            database: "moe_message"
            sql: |
              create table moe_notification_record_bak_20250815
              (
                  id             int unsigned auto_increment
                      primary key,
                  notifaction_id int         default 0                      not null,
                  business_id    int         default 0                      not null,
                  account_id     int         default 0                      not null,
                  staff_id       int         default 0                      not null,
                  is_successed   tinyint     default 1                      not null comment '是否发送成功：1-成功，2-失败',
                  status         tinyint     default 1                      not null comment '1 正常 2dismiss',
                  send_time      bigint      default 0                      not null,
                  read_time      bigint      default 0                      not null,
                  create_time    bigint      default 0                      not null,
                  update_time    bigint      default 0                      not null,
                  company_id     bigint      default 0                      not null,
                  type           varchar(50) default 'activity_book_create' not null
              )
                  collate = utf8mb4_unicode_520_ci;

