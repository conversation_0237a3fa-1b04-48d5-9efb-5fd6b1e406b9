name: "update CS-32451 extra CV fee"
description: "because the business added a 100% discount on the legacy invoice, the extra CV fee needs to be removed."
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              BEGIN;
              
              INSERT INTO public.order_line_extra_fee(business_id, order_id, order_item_id, apply_type, is_deleted, fee_type, amount, collect_type, description, apply_by, create_time, update_time) VALUES (116393, 149890237, 0, 'none', false, 'convenience fee', 5.15, 'subtract', '', 0, NOW(), NOW());

              UPDATE public."order" SET 
                  total_amount = total_amount - 5.15,
                  extra_fee_amount = extra_fee_amount - 5.15,
                  remain_amount = GREATEST(0, remain_amount - 5.15),
                  update_time = NOW()
              WHERE business_id = 116393 AND id = 149890237;
              
              COMMIT;