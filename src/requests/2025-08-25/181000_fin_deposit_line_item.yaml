name: "deposit line item"
description: "deposit line item"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              ALTER TABLE public."order" ADD COLUMN deposit_to_tips_amount NUMERIC(20, 2) DEFAULT 0.00 NOT NULL;
              ALTER TABLE public."order_line_item" ADD COLUMN deposit_amount NUMERIC(20, 2) DEFAULT 0.00 NOT NULL;

              ALTER TABLE public.refund_order
                  ADD COLUMN refund_deposit_amount numeric(20, 2) DEFAULT 0 NOT NULL,
                  ADD COLUMN refund_deposit_to_tips_amount numeric(20, 2) DEFAULT 0 NOT NULL;

              ALTER TABLE public.order_line_item
                  ADD COLUMN refunded_deposit_amount numeric(20, 2) DEFAULT 0 NOT NULL;

              ALTER TABLE public.refund_order_item
                  ADD COLUMN refund_deposit_amount numeric(20, 2) DEFAULT 0 NOT NULL;

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              ALTER TABLE public."order" ADD COLUMN deposit_to_tips_amount NUMERIC(20, 2) DEFAULT 0.00 NOT NULL;
              ALTER TABLE public."order_line_item" ADD COLUMN deposit_amount NUMERIC(20, 2) DEFAULT 0.00 NOT NULL;

              ALTER TABLE public.refund_order
                  ADD COLUMN refund_deposit_amount numeric(20, 2) DEFAULT 0 NOT NULL,
                  ADD COLUMN refund_deposit_to_tips_amount numeric(20, 2) DEFAULT 0 NOT NULL;

              ALTER TABLE public.order_line_item
                  ADD COLUMN refunded_deposit_amount numeric(20, 2) DEFAULT 0 NOT NULL;

              ALTER TABLE public.refund_order_item
                  ADD COLUMN refund_deposit_amount numeric(20, 2) DEFAULT 0 NOT NULL;
