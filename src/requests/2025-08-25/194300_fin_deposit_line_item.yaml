name: "deposit line item"
description: "deposit line item"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              COMMENT ON COLUMN "order".deposit_to_tips_amount IS 'Tips amount deducted from the deposit';
              COMMENT ON COLUMN refund_order.refund_deposit_to_tips_amount IS 'Refunded tips amount which is deducted from the deposit';
              COMMENT ON COLUMN order_line_item.refunded_deposit_amount IS 'Refunded deposit amount';
              COMMENT ON COLUMN order_line_item.deposit_amount IS 'Deposit amount deducted to this item';
              COMMENT ON COLUMN refund_order_item.refund_deposit_amount IS 'Refunded deposit amount which is deducted to this item';

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              COMMENT ON COLUMN "order".deposit_to_tips_amount IS 'Tips amount deducted from the deposit';
              COMMENT ON COLUMN refund_order.refund_deposit_to_tips_amount IS 'Refunded tips amount which is deducted from the deposit';
              COMMENT ON COLUMN order_line_item.refunded_deposit_amount IS 'Refunded deposit amount';
              COMMENT ON COLUMN order_line_item.deposit_amount IS 'Deposit amount deducted to this item';
              COMMENT ON COLUMN refund_order_item.refund_deposit_amount IS 'Refunded deposit amount which is deducted to this item';
