name: "update phone number type"
description: "update phone number type"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-message"
      actions:
        - execute_sql:
            database: "moe_message"
            sql: |
              select * from moe_phone_number_type where id = 6462653;
              update moe_phone_number_type set type = 'mobile' where id = 6462653;
              select * from moe_phone_number_type where id = 6462653;