name: "Fix developer_better database access permissions"
description: "Grant necessary permissions to developer_better to fix access denied error"
stopWhenError: true
requests:
  # Production MySQL - Grant database access permissions
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-message"
      actions:
        - execute_sql:
            database: "moe_message"
            username: "${db.user.admin}"
            sql: |
              -- Grant database connection permission
              GRANT CONNECT ON DATABASE moe_message TO developer_better;
              
              -- Grant usage on schema
              GRANT USAGE ON SCHEMA * TO developer_better;
              
              -- Grant basic permissions on all tables
              GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA * TO developer_better;
              
              -- Grant permissions on future tables
              ALTER DEFAULT PRIVILEGES IN SCHEMA * GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO developer_better;
