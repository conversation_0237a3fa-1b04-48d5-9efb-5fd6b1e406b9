stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_fulfillment"
            username: "${db.user.admin}"
            sql: |
              drop index public.group_class_detail_uni_pet_instance;
  
              create unique index group_class_detail_uni_pet_instance
              on public.group_class_detail (group_class_instance_id, pet_id)
              where deleted_at is null;

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_fulfillment"
            username: "${db.user.admin}"
            sql: |
              drop index public.group_class_detail_uni_pet_instance;
  
              create unique index group_class_detail_uni_pet_instance
              on public.group_class_detail (group_class_instance_id, pet_id)
              where deleted_at is null;
