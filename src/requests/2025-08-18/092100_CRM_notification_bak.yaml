name: "CRM notification backup permissions and indexes"
description: "<PERSON> delete permission to developer_better and add indexes for notification backup tables"
stopWhenError: true
requests:
  # Production MySQL - Grant permissions and add indexes
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-message"
      actions:
        - execute_sql:
            database: "moe_message"
            username: "${db.user.admin}"
            sql: |
              -- Grant delete permission to developer_better
              GRANT DELETE ON moe_notification_bak_20250815 TO developer_better;
              GRANT DELETE ON moe_notification_record_bak_20250815 TO developer_better;
              
              -- Add indexes for id fields
              CREATE INDEX idx_notification_bak_id ON moe_notification_bak_20250815 (id);
              CREATE INDEX idx_notification_record_bak_id ON moe_notification_record_bak_20250815 (id);
