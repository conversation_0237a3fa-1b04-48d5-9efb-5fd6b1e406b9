name: "在 Order 表增加一个 Index 给自检和对账服务使用"
description: ""
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "staging-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              CREATE INDEX order_index_order_version_update_time
                ON public."order" (order_version, update_time)
                WHERE (order_version >= 4);

  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              CREATE INDEX order_index_order_version_update_time
                ON public."order" (order_version, update_time)
                WHERE (order_version >= 4);
