name: "CRM notification permissions for developer_better"
description: "<PERSON> delete permission to developer_better for notification tables"
stopWhenError: true
requests:
  # Production MySQL - Grant permissions
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-mysql-message"
      actions:
        - execute_sql:
            database: "moe_message"
            username: "${db.user.admin}"
            sql: |
              -- <PERSON> delete permission to developer_better
              GRANT DELETE ON moe_notification TO developer_better;
              GRANT DELETE ON moe_notification_record TO developer_better;
