name: "Fix CS-32308"
description: "Remove Legacy Invoice extra CV FEE"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: AWS_RDS
        identifier: "prod-postgres-cluster"
      actions:
        - execute_sql:
            database: "moego_order"
            sql: |
              BEGIN;
              
              INSERT INTO public.order_line_extra_fee(business_id, order_id, order_item_id, apply_type, is_deleted, fee_type, amount, collect_type, description, apply_by, create_time, update_time) VALUES (114867, 147323906, 0, 'none', false, 'convenience fee', 8.3, 'subtract', '', 0, NOW(), NOW());

              UPDATE public."order" SET 
                  total_amount = total_amount - 8.3,
                  extra_fee_amount = extra_fee_amount - 8.3,
                  remain_amount = GREATEST(0, remain_amount - 8.3),
                  update_time = NOW()
              WHERE business_id = 114867 AND id = 147323906;
              
              COMMIT;