# DevOps Console
执行对 MoeGo 环境动态资源的变更，例如：执行 SQL，创建 Topic，执行 Redis 命令等。

DevOpsConsole 支持的 API 参见 [schema/proto](schema/proto)

## 如何使用
要执行变更请提交 PR，示例：
```yaml
name: "DevOps Console Request Example"
description: "just test"
issues:
  - "IFRBE-492"
stopWhenError: true
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: "platform-name"
        identifier: "database-instance-name"
      actions:
        - execute_sql:
            database: "database-name"
            username: "username"
            sql: |
              CREATE TABLE t_debug(
                id int PRIMARY KEY,
                time timestamp
              );

              INSERT INTO t_debug(id, time)
              VALUES (1, NOW()),
                     (2, NOW());

              SELECT COUNT(*) FROM t_debug;
              SELECT * FROM t_debug ORDER BY id DESC;
  - api: "/moego.devops.api.console.v1.MessageQueueApi/ListTopics"
    body:
      identifier:
        platform: "platform-name"
        identifier: "kafka-instance-name"
      internal: false
  - api: ...
```
要执行变更请提交类似上述示例所展示的一个或多个文件，每个文件中可以指定任意多个 API 请求，然后在 [src/requests.yaml](src/requests.yaml) 中明确指定要执行哪些文件即可。
例如，在某次需求中编写了两个请求文件：`src/requests/2024-12-01/093001_file1.yaml`，`src/requests/2024-12-01/093001_file2.yaml`，那么应该同时更新 `src/requests.yaml` 如下：
```yaml
# src/requests.yaml
# 注意：文件路径是相对 src/requests.yaml 而言的
resources:
  - requests/2024-12-01/093001_file1.yaml
  - requests/2024-12-01/093001_file2.yaml
```
将以上 3 个文件作为一个 PR 一起提交，在 PR 通过后的 merge actions 中将按照定义的顺序挨个执行文件中定义的请求内容。

### Q & A
* 为什么需要 `src/requests.yaml` 文件？
  ```text
  当然，我可以在 CI actions 中自动检测改动了哪些文件并执行变更请求。但有些场景可能是改动了一些文件但并不想被执行，或者想执行一个之前已经存在的文件。
  另一个问题是，在某些较大的请求变更时可能会有顺序要求，自动检测的执行结果无法保证与预期的顺序一致，因此在一个文件中明确指定要执行的内容能减少歧义的发生。
  ```
* 请求文件可以放在任意位置吗？
  ```text
  理论上是的，但是为了方便管理我们最好还是约定一下位置和命名规则：
    - 所有的请求变更文件都统一放在 src/requests 目录下
    - 暂定下一级是以日期命名的子目录
    - 然后是以精确到秒的时间戳为前缀的文件名
    - 文件名后缀可以是 issue id，或业务相关的描述，最好仅包含：数字/字母/下划线/短横线
  规则的目的是减少文件冲突，又易于查找和识别。示例：
    src/requests/2024-12-01/093001_FDN_123.yaml
    src/requests/2024-12-01/105003_insert_grooming.yaml
    src/requests/2024-12-02/103000_something.json
  ```
* 文件类型有要求吗？
  ```text
  目前允许 yaml、json 文件格式，按文件扩展名识别，有效的文件扩展名为：.yaml, .yml, .json
  强烈建议采用 yaml 文件格式，因为从表达性上 yaml 更好。
  ```
* 在请求中需要指定要执行变更的资源标识符，这个在哪里查看？
  ```text
  在 schema/resources 目录下存放了目前本仓库所有可以处理的资源，按对应类型查阅即可。
  ```
* 执行规则是怎样的？
  ```text
  此仓库的 PR 必须由至少两个以上 owner approved 才可以 merge
  在提交 PR 之后 merge 之前 push 内容，将执行 --dry-run 选项，对请求进行 explain 而不是真的执行，可在 CI actions 中查看 explain 结果。
  在 merge 到主干分支时，执行真正的请求动作，可在 CI actions 中查看请求结果。
  ```
* 我不通过 PR，直接向 DevOps Console 发送 API 请求可以吗？
  ```text
  DevOps Console API 是公开的，但是必须通过有效的授权才可以访问，目前只有本仓库的 CI actions 配置了相关的 Token 才可以正确访问。
  ```

## 示例
### 提交 SQL 请求
SQL 请求动作包含两种：
* create database: 这是因为创建一个新数据库通常需要 admin 账号才能执行，并且数据库创建后通常还需要给指定的用户分配访问权限，甚至需要为新数据库创建一个专有的账号，但是账号/密码又不能由提交者指定。
  因此专门设计一个 create sql action 来简化此操作的编写，此 action 主要是为生产环境的 postgres 数据设计使用。
* execute sql: 执行任意合法的 sql，注意同一个 action 中的 sql 是在同一个 database 上下文中执行，例如，指定 `database: db1`, `sql: select * from table ...`,
  如果是 mysql 数据库，则相当于 `select * from db1.table ...`。
* 目前仅支持 mysql 和 postgres 数据库
```yaml
requests:
  - api: "/moego.devops.api.console.v1.DatabaseApi/ExecuteSql"
    body:
      identifier:
        platform: "platform-name"
        identifier: "database-instance-name"
      actions:
        - create_database:
            database: "database-name"
            username: "username"
            create_user: true
        - execute_sql:
            database: "database-name"
            username: "username"
            sql: |
              CREATE TABLE t_debug(
                id int PRIMARY KEY,
                time timestamp
              );

              INSERT INTO t_debug(id, time)
              VALUES (1, NOW()),
                     (2, NOW());

              SELECT COUNT(*) FROM t_debug;
              SELECT * FROM t_debug ORDER BY id DESC;
        - execute_sql:
            database: "other-database"
            username: "username"
            sql: “SELECT * FROM other_table ORDER BY id DESC LIMIT 10;”
```
### 提交 Topic 请求
* 允许的动作有： `ListTopics`, `CreateTopics`, `DeleteTopics`
* 目前仅支持 kafka 引擎
* `CreateTopics` 请求中的 `tenant` 和 `namespace` 字段是为 `pulsar` 设计的，暂时保留。
```yaml
requests:
  - api: "/moego.devops.api.console.v1.MessageQueueApi/ListTopics"
    body:
      identifier:
        platform: "platform-name"
        identifier: "kafka-instance-name"
      internal: false
  - api: "/moego.devops.api.console.v1.MessageQueueApi/CreateTopics"
    body:
      identifier:
        platform: "platform-name"
        identifier: "kafka-instance-name"
      topics:
        - name: "test-sample1"
          partition_number: 3
          replication_factor: 2
        - name: "test-sample2"
          partition_number: 3
          replication_factor: 2
  - api: "/moego.devops.api.console.v1.MessageQueueApi/DeleteTopics"
    body:
      identifier:
        platform: "platform-name"
        identifier: "kafka-instance-name"
      topics:
        - "test-sample1"
        - "test-sample2"
```
更多示例参见：[src/examples](src/examples)